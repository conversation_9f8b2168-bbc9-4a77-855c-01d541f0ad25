package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 热量分析结果对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "热量分析结果", description = "文本中包含的热量摄入和消耗信息分析结果")
public class HeatAnalysisResult {

    /**
     * 记录ID
     */
    @Schema(description = "记录ID", example = "1")
    private Long id;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称", example = "跑步半小时")
    private String eventName;

    /**
     * 类型：摄入热量，消耗热量
     */
    @Schema(description = "类型", example = "消耗热量", allowableValues = { "摄入热量", "消耗热量" })
    private String type;

    /**
     * 单位：克，分钟等
     */
    @Schema(description = "单位：克，分钟等", example = "分钟")
    private String unit;

    /**
     * 单位对应的数量
     */
    @Schema(description = "单位对应的数量", example = "30")
    private Integer quantity;

    /**
     * 热量值，单位：千卡(kcal)
     */
    @Schema(description = "热量值，单位：千卡(kcal)", example = "300")
    private Integer heatValue;

    /**
     * 碳水化合物（克）
     */
    @Schema(description = "碳水化合物（克）", example = "30")
    private Integer carbohydrate;

    /**
     * 蛋白质（克）
     */
    @Schema(description = "蛋白质（克）", example = "30")
    private Integer protein;

    /**
     * 脂肪（克）
     */
    @Schema(description = "脂肪（克）", example = "30")
    private Integer fat;

    /**
     * 体重（千克）
     */
    @Schema(description = "体重（千克）", example = "70.5")
    private Double weight;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}