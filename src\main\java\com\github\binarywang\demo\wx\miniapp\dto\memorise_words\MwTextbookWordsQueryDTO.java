package com.github.binarywang.demo.wx.miniapp.dto.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 记忆单词-课本单词查询参数
 */
@Data
@Schema(description = "获取课本词语请求参数")
public class MwTextbookWordsQueryDTO {

    @Schema(description = "记忆单词-课本单词ID列表")
    private List<Long> ids;

    @Schema(description = "是否包含课文划词")
    private Boolean includeLessonWords;
}