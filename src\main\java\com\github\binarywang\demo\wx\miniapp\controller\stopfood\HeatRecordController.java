package com.github.binarywang.demo.wx.miniapp.controller.stopfood;

import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.NutrientSummaryRequest;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.DailyNutrientPageRequest;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodHeatRecordMapper;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.NutrientSummaryVO;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.DailyNutrientSummaryVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 热量记录控制器
 */
@RestController
@Slf4j
@RequestMapping("/wx/heat-record/{appid}")
@Tag(name = "热量记录接口")
public class HeatRecordController {

    private final StopfoodHeatRecordMapper heatRecordMapper;
    private final JwtUtils jwtUtils;

    @Autowired
    public HeatRecordController(StopfoodHeatRecordMapper heatRecordMapper, JwtUtils jwtUtils) {
        this.heatRecordMapper = heatRecordMapper;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 获取指定日期范围内用户摄入的营养素总和
     */
    @Operation(summary = "获取指定日期内用户摄入的营养素之合", description = "获取用户在指定日期范围内摄入的碳水化合物、蛋白质和脂肪的总量。默认查询今天的数据。", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "营养素汇总请求", required = false, content = @Content(mediaType = "application/json", schema = @Schema(implementation = NutrientSummaryRequest.class))))
    @PostMapping("/nutrient-summary")
    public ApiResponse<NutrientSummaryVO> getNutrientSummary(
            @PathVariable String appid,
            @Valid @RequestBody(required = false) NutrientSummaryRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 如果请求体为空，创建默认请求（今天）
            if (request == null) {
                request = new NutrientSummaryRequest();
            }

            // 获取日期范围，默认为今天
            String startDate = request.getStartDate();
            String endDate = request.getEndDate();

            // 验证日期格式和有效性
            try {
                LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE);
                LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE);

                if (start.isAfter(end)) {
                    return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "开始日期不能晚于结束日期");
                }
            } catch (DateTimeParseException e) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "日期格式错误，请使用YYYY-MM-DD格式");
            }

            log.info("用户[{}]查询营养素汇总：开始日期={}, 结束日期={}", openId, startDate, endDate);

            // 查询营养素总和
            Map<String, Object> result = heatRecordMapper.getNutrientSumByDateRange(openId, startDate, endDate);

            // 构建响应对象
            NutrientSummaryVO response = NutrientSummaryVO.builder()
                    .totalCarbohydrate(((Number) result.get("totalCarbohydrate")).intValue())
                    .totalProtein(((Number) result.get("totalProtein")).intValue())
                    .totalFat(((Number) result.get("totalFat")).intValue())
                    .startDate(startDate)
                    .endDate(endDate)
                    .build();

            log.info("用户[{}]营养素汇总查询成功：碳水化合物={}g, 蛋白质={}g, 脂肪={}g",
                    openId, response.getTotalCarbohydrate(), response.getTotalProtein(), response.getTotalFat());

            return ApiResponse.success("查询成功", response);

        } catch (Exception e) {
            log.error("营养素汇总查询失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取每日的热量总摄入、热量总消耗、总摄入的碳水化合物、总摄入的蛋白质、总摄入的脂肪
     */
    @Operation(summary = "分页获取每日营养素汇总", description = "分页获取用户每日的热量摄入、消耗和营养素数据，默认查询最近7天（包含今天），最多支持21天的数据查询。按日期倒序返回。", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "每日营养素分页查询请求", required = false, content = @Content(mediaType = "application/json", schema = @Schema(implementation = DailyNutrientPageRequest.class))))
    @PostMapping("/daily-nutrient-summary")
    public ApiResponse<PageResult<DailyNutrientSummaryVO>> getDailyNutrientSummary(
            @PathVariable String appid,
            @Valid @RequestBody(required = false) DailyNutrientPageRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 如果请求体为空，创建默认请求
            if (request == null) {
                request = new DailyNutrientPageRequest();
            }

            // 获取分页参数
            int pageNum = request.getPageNum();
            int pageSize = request.getPageSize();
            int offset = (pageNum - 1) * pageSize;

            log.info("用户[{}]分页查询每日营养素汇总：页码={}, 每页大小={}", openId, pageNum, pageSize);

            // 查询总记录数
            int totalCount = heatRecordMapper.getDailyNutrientSummaryCount(openId);

            // 查询分页数据
            List<Map<String, Object>> dataList = heatRecordMapper.getDailyNutrientSummaryByPage(openId, offset,
                    pageSize);

            // 转换为VO对象
            List<DailyNutrientSummaryVO> voList = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                DailyNutrientSummaryVO vo = DailyNutrientSummaryVO.builder()
                        .recordDate(data.get("recordDate").toString())
                        .totalIntakeCalories(((Number) data.get("totalIntakeCalories")).intValue())
                        .totalConsumeCalories(((Number) data.get("totalConsumeCalories")).intValue())
                        .totalCarbohydrate(((Number) data.get("totalCarbohydrate")).intValue())
                        .totalProtein(((Number) data.get("totalProtein")).intValue())
                        .totalFat(((Number) data.get("totalFat")).intValue())
                        .build();

                // 计算净热量
                vo.calculateNetCalories();
                voList.add(vo);
            }

            // 构建分页结果
            PageResult<DailyNutrientSummaryVO> pageResult = new PageResult<>();
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotal((long) totalCount);
            pageResult.setPages((int) Math.ceil((double) totalCount / pageSize));
            pageResult.setList(voList);

            log.info("用户[{}]分页查询每日营养素汇总成功，页码：{}，每页大小：{}，总记录数：{}",
                    openId, pageNum, pageSize, totalCount);

            return ApiResponse.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页获取每日营养素汇总失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }
}