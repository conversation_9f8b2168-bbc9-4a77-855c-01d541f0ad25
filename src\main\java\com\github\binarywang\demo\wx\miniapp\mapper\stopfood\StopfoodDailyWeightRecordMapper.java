package com.github.binarywang.demo.wx.miniapp.mapper.stopfood;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDailyWeightRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StopfoodDailyWeightRecordMapper {

        /**
         * 查询用户指定日期的体重记录
         */
        StopfoodDailyWeightRecord selectByOpenIdAndDate(@Param("openId") String openId,
                        @Param("recordDate") String recordDate);

        /**
         * 插入新的体重记录
         */
        int insertWeightRecord(StopfoodDailyWeightRecord record);

        /**
         * 更新体重记录
         */
        int updateWeightRecord(StopfoodDailyWeightRecord record);

        /**
         * 查询用户体重记录总数
         */
        Long countWeightRecords(@Param("openId") String openId);

        /**
         * 分页查询用户体重记录
         */
        List<StopfoodDailyWeightRecord> selectWeightRecordsByPage(@Param("openId") String openId,
                        @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);
}