package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 基础代谢请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "基础代谢请求参数")
public class BmrRequest {

    /**
     * 用户的基础代谢，单位：千卡(kcal)
     */
    @NotNull(message = "基础代谢值不能为空")
    @Schema(description = "用户的基础代谢，单位：千卡(kcal)", required = true)
    private Integer bmrValue;

    /**
     * 基础代谢的生成方式：公式计算、自定义
     */
    @NotBlank(message = "基础代谢生成方式不能为空")
    @Schema(description = "基础代谢的生成方式：公式计算、自定义", required = true)
    private String generationMode;
}