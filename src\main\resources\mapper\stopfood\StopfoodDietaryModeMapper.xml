<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodDietaryModeMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode" id="StopfoodDietaryModeResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="mode" column="mode"/>
        <result property="coefficient" column="coefficient"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectStopfoodDietaryModeVo">
        select id, open_id, union_id, mode, coefficient, create_time, update_time
        from stopfood_dietary_mode
    </sql>

    <select id="selectByOpenId" parameterType="String" resultMap="StopfoodDietaryModeResult">
        <include refid="selectStopfoodDietaryModeVo"/>
        where open_id = #{openId}
    </select>

    <insert id="insert" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode" useGeneratedKeys="true" keyProperty="id">
        insert into stopfood_dietary_mode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="mode != null and mode != ''">mode,</if>
            <if test="coefficient != null">coefficient,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="mode != null and mode != ''">#{mode},</if>
            <if test="coefficient != null">#{coefficient},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode">
        update stopfood_dietary_mode
        <trim prefix="SET" suffixOverrides=",">
            <if test="mode != null and mode != ''">mode = #{mode},</if>
            <if test="coefficient != null">coefficient = #{coefficient},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

</mapper> 