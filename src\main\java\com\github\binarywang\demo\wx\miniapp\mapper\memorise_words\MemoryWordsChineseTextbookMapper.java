package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsChineseTextbook;

/**
 * 记忆单词-中文课本单词Mapper接口
 */
@Mapper
public interface MemoryWordsChineseTextbookMapper {
    /**
     * 查询记忆单词-中文课本单词列表
     * 
     * @return 记忆单词-中文课本单词列表
     */
    List<MemoryWordsChineseTextbook> selectMemoryWordsChineseTextbookList();

    /**
     * 根据ID查询记忆单词-中文课本单词
     * 
     * @param id 记忆单词-中文课本单词ID
     * @return 记忆单词-中文课本单词
     */
    MemoryWordsChineseTextbook selectMemoryWordsChineseTextbookById(Long id);

    /**
     * 新增记忆单词-中文课本单词
     * 
     * @param memoryWordsChineseTextbook 记忆单词-中文课本单词
     * @return 结果
     */
    int insertMemoryWordsChineseTextbook(MemoryWordsChineseTextbook memoryWordsChineseTextbook);

    /**
     * 修改记忆单词-中文课本单词
     * 
     * @param memoryWordsChineseTextbook 记忆单词-中文课本单词
     * @return 结果
     */
    int updateMemoryWordsChineseTextbook(MemoryWordsChineseTextbook memoryWordsChineseTextbook);

    /**
     * 删除记忆单词-中文课本单词
     * 
     * @param id 记忆单词-中文课本单词ID
     * @return 结果
     */
    int deleteMemoryWordsChineseTextbookById(Long id);
}