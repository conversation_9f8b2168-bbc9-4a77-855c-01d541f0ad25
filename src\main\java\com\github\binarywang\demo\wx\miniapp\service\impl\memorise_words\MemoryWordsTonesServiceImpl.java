package com.github.binarywang.demo.wx.miniapp.service.impl.memorise_words;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTones;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsTonesMapper;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsTonesService;

import lombok.extern.slf4j.Slf4j;

/**
 * 记忆单词-音色表Service实现类
 */
@Service
@Slf4j
public class MemoryWordsTonesServiceImpl implements MemoryWordsTonesService {

    @Autowired
    private MemoryWordsTonesMapper memoryWordsTonesMapper;

    @Override
    public MemoryWordsTones getTonesInfo(String environment, String voiceTypeKey, String speedRatioKey) {
        try {
            log.info("查询音色信息，环境:{}，音色类型:{}，语速:{}", environment, voiceTypeKey, speedRatioKey);
            return memoryWordsTonesMapper.getTonesInfo(environment, voiceTypeKey, speedRatioKey);
        } catch (Exception e) {
            log.error("查询音色信息失败", e);
            return null;
        }
    }

    @Override
    public String getVoiceTypeValue(String environment, String voiceTypeKey) {
        try {
            log.info("查询音色类型值，环境:{}，音色类型:{}", environment, voiceTypeKey);
            String value = memoryWordsTonesMapper.getVoiceTypeValue(environment, voiceTypeKey);

            // 如果没有查到音色类型值，则使用默认值
            if (value == null) {
                if ("中文".equals(environment)) {
                    value = "BV700_streaming"; // 中文环境默认值
                } else {
                    value = "BV040_streaming"; // 英文环境默认值
                }
                log.info("未找到音色类型值，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询音色类型值失败", e);
            // 返回默认值
            return "中文".equals(environment) ? "BV700_streaming" : "BV040_streaming";
        }
    }

    @Override
    public Float getSpeedRatioValue(String speedRatioKey) {
        try {
            log.info("查询语速值，语速类型:{}", speedRatioKey);
            Float value = memoryWordsTonesMapper.getSpeedRatioValue(speedRatioKey);

            // 如果没有查到语速值，则使用默认值
            if (value == null) {
                if ("慢速".equals(speedRatioKey)) {
                    value = 0.8f;
                } else if ("快速".equals(speedRatioKey)) {
                    value = 1.5f;
                } else {
                    value = 1.0f; // "中速"或其他未知值，使用默认值
                }
                log.info("未找到语速值，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询语速值失败", e);
            // 返回默认值
            if ("慢速".equals(speedRatioKey)) {
                return 0.8f;
            } else if ("快速".equals(speedRatioKey)) {
                return 1.5f;
            } else {
                return 1.0f; // "中速"或其他未知值，使用默认值
            }
        }
    }
}