package com.github.binarywang.demo.wx.miniapp.controller.stopfood;

import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.HeatAnalysisRequest;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.HeatCalculationRequest;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.WeixinStepsRequest;
import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord;
import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDailyWeightRecord;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodHeatRecordMapper;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodDailyWeightRecordMapper;
import com.github.binarywang.demo.wx.miniapp.service.stopfood.HeatAnalysisService;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.DailyHeatSummary;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.HeatAnalysisResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 热量分析控制器
 */
@RestController
@Slf4j
@RequestMapping("/wx/heat/{appid}")
@Tag(name = "热量分析接口")
public class HeatController {

    private final HeatAnalysisService heatAnalysisService;
    private final StopfoodHeatRecordMapper heatRecordMapper;
    private final StopfoodDailyWeightRecordMapper weightRecordMapper;
    private final JwtUtils jwtUtils;

    @Autowired
    public HeatController(HeatAnalysisService heatAnalysisService,
            StopfoodHeatRecordMapper heatRecordMapper,
            StopfoodDailyWeightRecordMapper weightRecordMapper,
            JwtUtils jwtUtils) {
        this.heatAnalysisService = heatAnalysisService;
        this.heatRecordMapper = heatRecordMapper;
        this.weightRecordMapper = weightRecordMapper;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 分析并保存用户摄入或消耗的热量
     */
    @Operation(summary = "分析热量信息", description = "解析文本中包含的热量摄入和消耗信息，并保存到数据库", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "热量分析请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = HeatAnalysisRequest.class))))
    @PostMapping("/analyze")
    public ApiResponse<List<HeatAnalysisResult>> analyzeHeat(
            @PathVariable String appid,
            @RequestBody HeatAnalysisRequest request,
            HttpServletRequest httpRequest) {
        // 验证请求参数
        if (request == null || StringUtils.isBlank(request.getContent())) {
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "请求内容不能为空");
        }

        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 调用服务分析文本中的热量信息，使用新的V2版本提示词
            List<HeatAnalysisResult> results = heatAnalysisService.analyzeHeatFromTextV2(request.getContent());

            // 保存热量记录到数据库
            if (results != null && !results.isEmpty()) {
                List<StopfoodHeatRecord> records = new ArrayList<>();

                // 处理所有热量事件
                for (HeatAnalysisResult result : results) {
                    if (StringUtils.isNotBlank(result.getEventName())) {
                        StopfoodHeatRecord record = StopfoodHeatRecord.builder()
                                .openId(openId)
                                .eventName(result.getEventName())
                                .type(result.getType())
                                .unit(result.getUnit())
                                .quantity(result.getQuantity())
                                .heatValue(result.getHeatValue())
                                .carbohydrate(result.getCarbohydrate())
                                .protein(result.getProtein())
                                .fat(result.getFat())
                                .build();
                        records.add(record);
                    }
                }

                // 批量保存记录
                if (!records.isEmpty()) {
                    heatRecordMapper.batchInsertHeatRecords(records);
                    log.info("用户[{}]的热量记录已保存，共{}条", openId, records.size());
                }

                // 处理体重记录
                Double weight = results.get(0).getWeight();
                if (weight != null && weight > 0) {
                    // 获取今天的日期
                    String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);

                    // 查询今天是否已有体重记录
                    StopfoodDailyWeightRecord existingRecord = weightRecordMapper.selectByOpenIdAndDate(openId, today);

                    if (existingRecord == null) {
                        // 没有记录，创建新记录
                        StopfoodDailyWeightRecord newRecord = StopfoodDailyWeightRecord.builder()
                                .openId(openId)
                                .recordDate(today)
                                .weight(weight)
                                .build();
                        weightRecordMapper.insertWeightRecord(newRecord);
                        log.info("用户[{}]新增体重记录：{}kg", openId, weight);
                    } else {
                        // 有记录，更新记录
                        existingRecord.setWeight(weight);
                        weightRecordMapper.updateWeightRecord(existingRecord);
                        log.info("用户[{}]更新体重记录：{}kg", openId, weight);
                    }
                }
            } else {
                log.warn("未能从文本中解析出有效的热量事件");
            }

            return ApiResponse.success("热量分析成功", results);
        } catch (Exception e) {
            log.error("热量分析出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "热量分析失败: " + e.getMessage());
        }
    }

    /**
     * 精确计算热量及营养素
     */
    @Operation(summary = "精确计算热量及营养素", description = "根据事件类型、名称、单位和数量精确计算热量值及营养素", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "热量计算请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = HeatCalculationRequest.class))))
    @PostMapping("/accurateCalculateHeatAndNutrient")
    public ApiResponse<HeatAnalysisResult> accurateCalculateHeatAndNutrient(
            @PathVariable String appid,
            @Valid @RequestBody HeatCalculationRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 验证token
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 验证类型参数
            if (!"摄入热量".equals(request.getType()) && !"消耗热量".equals(request.getType())) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "类型只能是'摄入热量'或'消耗热量'");
            }

            log.info("用户[{}]请求计算热量：类型={}, 事件={}, 单位={}, 数量={}",
                    openId, request.getType(), request.getEventName(), request.getUnit(), request.getQuantity());

            HeatAnalysisResult accurateCalculationOfHeatV2 = heatAnalysisService.accurateCalculateHeatAndNutrient(request.getType(), request.getEventName(), request.getUnit(), request.getQuantity());

            if(accurateCalculationOfHeatV2 != null){
                return ApiResponse.success("热量计算成功", accurateCalculationOfHeatV2);
            } else {
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "热量计算失败: ");
            }
            
        } catch (Exception e) {
            log.error("精确计算热量出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "热量计算失败: " + e.getMessage());
        }
    }

    /**
     * 精确计算热量
     */
    @Deprecated
    @Operation(summary = "精确计算热量", description = "根据事件类型、名称、单位和数量精确计算热量值", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "热量计算请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = HeatCalculationRequest.class))))
    @PostMapping("/calculateHeat")
    public ApiResponse<Map<String, Object>> calculateHeat(
            @PathVariable String appid,
            @Valid @RequestBody HeatCalculationRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 验证token
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 验证类型参数
            if (!"摄入热量".equals(request.getType()) && !"消耗热量".equals(request.getType())) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "类型只能是'摄入热量'或'消耗热量'");
            }

            log.info("用户[{}]请求计算热量：类型={}, 事件={}, 单位={}, 数量={}",
                    openId, request.getType(), request.getEventName(), request.getUnit(), request.getQuantity());

            // 调用服务计算热量
            Integer heatValue = heatAnalysisService.accurateCalculationOfHeat(
                    request.getType(), request.getEventName(), request.getUnit(), request.getQuantity());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("type", request.getType());
            result.put("eventName", request.getEventName());
            result.put("unit", request.getUnit());
            result.put("quantity", request.getQuantity());
            result.put("heatValue", heatValue);

            if (heatValue != null && heatValue > 0) {
                return ApiResponse.success("热量计算成功", result);
            } else {
                return ApiResponse.success("未找到匹配的热量数据，返回默认值0", result);
            }
        } catch (Exception e) {
            log.error("精确计算热量出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "热量计算失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户今日的热量记录及营养素
     */
    @Operation(summary = "获取今日热量记录及营养素", description = "获取用户今日的所有热量摄入和消耗记录，包括具体的数量单位信息和营养素信息（碳水化合物、蛋白质、脂肪）", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    @GetMapping("/today")
    public ApiResponse<List<HeatAnalysisResult>> getTodayHeatRecordsAndNutrients(
            @PathVariable String appid,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 调用服务查询今日热量记录及营养素
            List<HeatAnalysisResult> results = heatAnalysisService.getTodayHeatRecordsAndNutrients(openId);

            if (results == null || results.isEmpty()) {
                return ApiResponse.success("今日还没有热量记录", new ArrayList<>());
            }

            log.info("用户[{}]查询今日热量记录成功，共{}条记录", openId, results.size());
            return ApiResponse.success("查询成功", results);
        } catch (Exception e) {
            log.error("查询今日热量记录及营养素出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取用户每天的热量记录
     */
    @Operation(summary = "分页获取每天热量记录", description = "分页获取用户每天的热量摄入和消耗记录汇总", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "pageNum", description = "页码，从1开始", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "pageSize", description = "每页记录数", required = true, in = ParameterIn.QUERY)
    })
    @GetMapping("/dailyRecordsByPage")
    public ApiResponse<PageResult<DailyHeatSummary>> getDailyHeatSummaryByPage(
            @PathVariable String appid,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest httpRequest) {

        // 验证分页参数
        if (pageNum < 1) {
            pageNum = 1;
        }

        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 调用服务查询热量记录
            PageResult<DailyHeatSummary> result = heatAnalysisService.getDailyHeatSummaryByPage(openId, pageNum,
                    pageSize);

            return ApiResponse.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询热量记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除热量记录
     */
    @Operation(summary = "删除热量记录", description = "根据记录ID集合删除用户的热量记录", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "记录ID集合", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))))
    @PostMapping("/deleteRecords")
    public ApiResponse<Map<String, Object>> deleteHeatRecords(
            @PathVariable String appid,
            @RequestBody Map<String, List<Long>> requestBody,
            HttpServletRequest httpRequest) {
        try {
            // 从请求body中获取ID集合
            List<Long> ids = requestBody.get("ids");
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "记录ID集合不能为空");
            }

            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 调用Mapper删除记录
            int count = heatRecordMapper.deleteHeatRecordsByIds(ids, openId);

            // 返回删除结果
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", count);

            if (count > 0) {
                return ApiResponse.success("删除成功", result);
            } else {
                return ApiResponse.success("没有找到符合条件的记录", result);
            }
        } catch (Exception e) {
            log.error("删除热量记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新热量记录
     */
    @Operation(summary = "更新热量记录", description = "根据记录ID更新事件名称、单位、数量、热量值和营养素信息", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "热量记录更新请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class))))
    @PostMapping("/updateHeatRecord")
    public ApiResponse<Map<String, Object>> updateHeatRecord(
            @PathVariable String appid,
            @RequestBody Map<String, Object> requestBody,
            HttpServletRequest httpRequest) {
        try {
            // 从请求body中获取更新信息
            Long id = Long.valueOf(requestBody.get("id").toString());
            String eventName = (String) requestBody.get("eventName");
            String unit = (String) requestBody.get("unit");
            Integer quantity = requestBody.get("quantity") != null
                    ? Integer.valueOf(requestBody.get("quantity").toString())
                    : null;
            Integer heatValue = requestBody.get("heatValue") != null
                    ? Integer.valueOf(requestBody.get("heatValue").toString())
                    : null;
            // 营养素字段
            Integer carbohydrate = requestBody.get("carbohydrate") != null
                    ? Integer.valueOf(requestBody.get("carbohydrate").toString())
                    : null;
            Integer protein = requestBody.get("protein") != null
                    ? Integer.valueOf(requestBody.get("protein").toString())
                    : null;
            Integer fat = requestBody.get("fat") != null
                    ? Integer.valueOf(requestBody.get("fat").toString())
                    : null;

            if (id == null) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "记录ID不能为空");
            }

            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 先检查记录是否属于当前用户
            StopfoodHeatRecord record = heatRecordMapper.selectHeatRecordById(id);
            if (record == null) {
                return ApiResponse.error(ApiResponseConstant.Code.NOT_FOUND, "记录不存在");
            }

            if (!openId.equals(record.getOpenId())) {
                return ApiResponse.error(ApiResponseConstant.Code.FORBIDDEN, "无权操作此记录");
            }

            // 更新记录对象
            if (eventName != null) {
                record.setEventName(eventName);
            }
            if (unit != null) {
                record.setUnit(unit);
            }
            if (quantity != null) {
                record.setQuantity(quantity);
            }
            if (heatValue != null) {
                record.setHeatValue(heatValue);
            }
            // 更新营养素字段
            if (carbohydrate != null) {
                record.setCarbohydrate(carbohydrate);
            }
            if (protein != null) {
                record.setProtein(protein);
            }
            if (fat != null) {
                record.setFat(fat);
            }

            // 调用Mapper更新记录
            int count = heatRecordMapper.updateHeatRecord(record);

            // 返回更新结果
            Map<String, Object> result = new HashMap<>();
            result.put("updated", count > 0);
            result.put("record", record);

            if (count > 0) {
                log.info("用户[{}]成功更新热量记录[{}]", openId, id);
                return ApiResponse.success("更新成功", result);
            } else {
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "更新失败");
            }
        } catch (NumberFormatException e) {
            log.error("参数格式错误", e);
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "参数格式错误");
        } catch (Exception e) {
            log.error("更新热量记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 新增/更新当天的微信运动步数消耗热量
     */
    @Operation(summary = "新增/更新微信运动步数", description = "新增或更新当天的微信运动步数所消耗的热量", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "微信运动步数请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = WeixinStepsRequest.class))))
    @PostMapping("/weixinSteps")
    public ApiResponse<Map<String, Object>> updateWeixinSteps(
            @PathVariable String appid,
            @Valid @RequestBody WeixinStepsRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            log.info("用户[{}]更新微信运动步数：{}", openId, request.getSteps());

            // 计算步数消耗的热量 - 使用公式：体重（kg）× 步数 × 0.0005
            Double weight = request.getWeight();
            if (weight == null || weight <= 0) {
                weight = 60.0; // 默认体重60kg
            }

            // 热量计算公式：体重（kg）× 步数 × 0.0005
            double calculatedHeat = weight * request.getSteps() * 0.0005;
            Integer heatValue = (int) Math.round(calculatedHeat); // 四舍五入取整

            log.info("用户[{}]热量计算：体重{}kg × 步数{} × 0.0005 = {}千卡", openId, weight, request.getSteps(), heatValue);

            // 查询今天是否已存在微信运动步数记录
            List<StopfoodHeatRecord> todayStepsRecords = heatRecordMapper.getTodayWeixinStepsRecords(openId);

            Map<String, Object> result = new HashMap<>();

            if (todayStepsRecords == null || todayStepsRecords.isEmpty()) {
                // 今天第一条微信运动步数记录，新增
                StopfoodHeatRecord newRecord = StopfoodHeatRecord.builder()
                        .openId(openId)
                        .eventName("自动-微信运动步数")
                        .type("消耗热量")
                        .unit("步")
                        .quantity(request.getSteps())
                        .heatValue(heatValue)
                        .build();

                int insertCount = heatRecordMapper.insertHeatRecord(newRecord);

                if (insertCount > 0) {
                    result.put("action", "新增");
                    result.put("recordId", newRecord.getId());
                    result.put("steps", request.getSteps());
                    result.put("weight", weight);
                    result.put("heatValue", heatValue);
                    log.info("用户[{}]新增微信运动步数记录成功，步数：{}，体重：{}kg，消耗热量：{}", openId, request.getSteps(), weight, heatValue);
                    return ApiResponse.success("新增微信运动步数记录成功", result);
                } else {
                    return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "新增记录失败");
                }
            } else {
                // 今天已有记录，更新最新的一条
                StopfoodHeatRecord latestRecord = todayStepsRecords.get(0); // 已按创建时间倒序排列
                latestRecord.setQuantity(request.getSteps());
                latestRecord.setHeatValue(heatValue);

                int updateCount = heatRecordMapper.updateHeatRecord(latestRecord);

                if (updateCount > 0) {
                    result.put("action", "更新");
                    result.put("recordId", latestRecord.getId());
                    result.put("steps", request.getSteps());
                    result.put("weight", weight);
                    result.put("heatValue", heatValue);
                    log.info("用户[{}]更新微信运动步数记录成功，步数：{}，体重：{}kg，消耗热量：{}", openId, request.getSteps(), weight, heatValue);
                    return ApiResponse.success("更新微信运动步数记录成功", result);
                } else {
                    return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "更新记录失败");
                }
            }
        } catch (Exception e) {
            log.error("处理微信运动步数出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "处理失败: " + e.getMessage());
        }
    }

}