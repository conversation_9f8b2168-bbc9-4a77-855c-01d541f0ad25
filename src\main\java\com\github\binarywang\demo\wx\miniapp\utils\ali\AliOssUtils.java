package com.github.binarywang.demo.wx.miniapp.utils.ali;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.*;

@Slf4j
public class AliOssUtils {

    private static final String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
    private static final String accessKeyId = "LTAI5tAuUdMvmtDuaaWRUJQc";
    private static final String accessKeySecret = "******************************";

    private static final String bucketName = "runningcat001";
    private static final String WRITE_PRIVATE_READ_PUBLIC_DIR = "WritePrivateReadPublic";

    // 文本文件：.txt、.doc、.docx、.pdf
    // 图像文件：.jpg、.jpeg、.png、.gif、.bmp
    // 视频文件：.mp4、.avi、.mov、.mkv、.flv
    // 音频文件：.mp3、.wav、.wma、.m4a
    // 压缩文件：.zip、.rar、.tar、.gz
    // 程序文件：.exe、.jar、.apk
    // 表格文件：.xls、.xlsx、.csv
    // 网页文件：.html、.htm、.css、.js
    // 邮件文件：.eml、.pst

    public static OSS getOssClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    public static String uploadFileToOss(InputStream inputStream, String fileName) {
        return uploadFileToOss(inputStream, WRITE_PRIVATE_READ_PUBLIC_DIR, fileName);
    }

    public static String uploadFileToOss(InputStream inputStream, String dir, String fileName) {
        OSS ossClient = getOssClient();
        ossClient.putObject(bucketName, dir + "/" + fileName, inputStream);
        ossClient.shutdown();
        String ossUrl = endpoint.split("//")[0] + "//" + bucketName + "." + endpoint.split("//")[1] + "/"
                + dir + "/" + fileName;
        return ossUrl;
    }

}
