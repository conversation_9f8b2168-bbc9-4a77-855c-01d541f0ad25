package com.github.binarywang.demo.wx.miniapp.vo.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 记忆单词-比对结果VO
 */
@Data
@Schema(description = "比对结果")
public class MwCompareResultVO {

    @Schema(description = "汇总信息")
    private SummaryInfo summary;

    @Schema(description = "明细信息")
    private List<DetailInfo> details;

    /**
     * 汇总信息
     */
    @Data
    @Schema(description = "汇总信息")
    public static class SummaryInfo {
        @Schema(description = "总词数")
        private Integer totalCount;

        @Schema(description = "正确数")
        private Integer correctCount;

        @Schema(description = "错误数")
        private Integer wrongCount;

        @Schema(description = "空白数")
        private Integer blankCount;
    }

    /**
     * 明细信息
     */
    @Data
    @Schema(description = "明细信息")
    public static class DetailInfo {
        @Schema(description = "原始单词")
        private String originalWord;

        @Schema(description = "用户写的单词")
        private String userWord;

        @Schema(description = "比对结果类型：对/错/空白")
        private String result;
    }
}