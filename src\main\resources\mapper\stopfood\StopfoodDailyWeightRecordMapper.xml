<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodDailyWeightRecordMapper">
    
    <resultMap id="BaseResultMap" type="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDailyWeightRecord">
        <id column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="union_id" property="unionId" />
        <result column="record_date" property="recordDate" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="selectByOpenIdAndDate" resultMap="BaseResultMap">
        SELECT * FROM stopfood_daily_weight_record 
        WHERE open_id = #{openId} AND record_date = #{recordDate}
        LIMIT 1
    </select>

    <insert id="insertWeightRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stopfood_daily_weight_record (
            open_id, union_id, record_date, weight, create_time, update_time
        ) VALUES (
            #{openId}, #{unionId}, #{recordDate}, #{weight}, NOW(), NOW()
        )
    </insert>

    <update id="updateWeightRecord">
        UPDATE stopfood_daily_weight_record
        SET weight = #{weight},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="countWeightRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM stopfood_daily_weight_record 
        WHERE open_id = #{openId}
    </select>

    <select id="selectWeightRecordsByPage" resultMap="BaseResultMap">
        SELECT * FROM stopfood_daily_weight_record 
        WHERE open_id = #{openId} 
        ORDER BY record_date DESC 
        LIMIT #{offset}, #{pageSize}
    </select>
</mapper> 