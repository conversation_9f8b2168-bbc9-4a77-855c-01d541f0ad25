package com.github.binarywang.demo.wx.miniapp.utils.ali;

import javazoom.jl.decoder.Bitstream;
import javazoom.jl.decoder.Header;
import java.io.ByteArrayInputStream;
import java.io.IOException;

/**
 * 音频时长计算工具类
 * 使用MP3专用解析器准确计算音频文件的时长
 */
public class AudioDurationUtils {

    /**
     * 音频时长计算结果
     */
    public static class DurationResult {
        private Integer durationSeconds;
        private String method;
        private String details;

        public DurationResult(Integer durationSeconds, String method, String details) {
            this.durationSeconds = durationSeconds;
            this.method = method;
            this.details = details;
        }

        public Integer getDurationSeconds() {
            return durationSeconds;
        }

        public String getMethod() {
            return method;
        }

        public String getDetails() {
            return details;
        }

        @Override
        public String toString() {
            return "DurationResult{duration=" + durationSeconds + "s, method='" + method + "', details='" + details
                    + "'}";
        }
    }

    /**
     * 计算音频时长（使用MP3专用解析器）
     * 
     * @param audioBytes   音频字节数组
     * @param originalText 原始文本内容（未使用，保持接口兼容性）
     * @param speechRate   语速参数（未使用，保持接口兼容性）
     * @param sampleRate   采样率（未使用，保持接口兼容性）
     * @return 时长计算结果
     */
    public static DurationResult calculateAudioDuration(byte[] audioBytes, String originalText,
            Integer speechRate, Integer sampleRate) {

        return calculateByMP3Parser(audioBytes);
    }

    /**
     * 使用MP3专用解析器计算音频时长
     * 
     * @param audioBytes 音频字节数组
     * @return 计算结果
     */
    private static DurationResult calculateByMP3Parser(byte[] audioBytes) {
        try {
            double durationInSeconds = getDuration(audioBytes);
            int duration = (int) Math.ceil(durationInSeconds);

            String details = String.format("精确时长=%.2f秒, 文件大小=%d字节",
                    durationInSeconds, audioBytes.length);

            return new DurationResult(duration > 0 ? duration : 1, "MP3专用解析器", details);

        } catch (Exception e) {
            System.err.println("MP3解析失败: " + e.getMessage());
            // 解析失败时返回默认1秒
            return new DurationResult(1, "默认值", "MP3解析失败，返回默认1秒");
        }
    }

    /**
     * 计算MP3音频的时长
     * 
     * @param mp3Data MP3音频字节数组
     * @return 音频时长（秒）
     * @throws IOException 解析失败时抛出异常
     */
    public static double getDuration(byte[] mp3Data) throws IOException {
        if (mp3Data == null || mp3Data.length == 0) {
            throw new IOException("MP3数据为空");
        }

        try (ByteArrayInputStream bais = new ByteArrayInputStream(mp3Data)) {
            Bitstream bitstream = new Bitstream(bais);
            double duration = 0.0;
            Header header;

            while ((header = bitstream.readFrame()) != null) {
                duration += header.ms_per_frame(); // 累加每帧时长（毫秒）
                bitstream.closeFrame(); // 移动到下一帧
            }
            bitstream.close();

            // 转换为秒并四舍五入到小数点后2位
            double durationInSeconds = duration / 1000.0;
            return Math.round(durationInSeconds * 100.0) / 100.0;

        } catch (Exception e) {
            throw new IOException("解析MP3失败: " + e.getMessage(), e);
        }
    }

}