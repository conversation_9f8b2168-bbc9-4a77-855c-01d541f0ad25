package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;

/**
 * 体重记录请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "体重记录请求参数")
public class WeightRecordRequest {

    /**
     * 记录日期，格式：yyyy-MM-dd
     */
    @NotBlank(message = "记录日期不能为空")
    @Schema(description = "记录日期，格式：yyyy-MM-dd", required = true, example = "2024-01-01")
    private String recordDate;

    /**
     * 体重，单位：千克(kg)
     */
    @NotNull(message = "体重不能为空")
    @DecimalMin(value = "0.1", message = "体重必须大于0.1kg")
    @DecimalMax(value = "999.99", message = "体重不能超过999.99kg")
    @Schema(description = "体重，单位：千克(kg)", required = true, example = "65.5")
    private Double weight;
}