package com.github.binarywang.demo.wx.miniapp.mapper.stopfood;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户饮食模式数据库操作接口
 */
@Mapper
public interface StopfoodDietaryModeMapper {

    /**
     * 根据openId查询饮食模式
     * 
     * @param openId 微信openId
     * @return 饮食模式对象，不存在则返回null
     */
    StopfoodDietaryMode selectByOpenId(@Param("openId") String openId);

    /**
     * 插入新的饮食模式记录
     * 
     * @param dietaryMode 饮食模式对象
     * @return 影响的行数
     */
    int insert(StopfoodDietaryMode dietaryMode);

    /**
     * 根据ID更新饮食模式
     * 
     * @param dietaryMode 饮食模式对象
     * @return 影响的行数
     */
    int updateById(StopfoodDietaryMode dietaryMode);
}