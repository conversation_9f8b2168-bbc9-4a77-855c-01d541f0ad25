package com.github.binarywang.demo.wx.miniapp.entity.stopfood;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 热量记录表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopfoodHeatRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionid
     */
    private String unionId;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 类型：摄入热量，消耗热量
     */
    private String type;

    /**
     * 单位：克，分钟等
     */
    private String unit;

    /**
     * 单位对应的数量
     */
    private Integer quantity;

    /**
     * 热量值，单位：千卡(kcal)
     */
    private Integer heatValue;

    /**
     * 碳水化合物（克）
     */
    private Integer carbohydrate;

    /**
     * 蛋白质（克）
     */
    private Integer protein;

    /**
     * 脂肪（克）
     */
    private Integer fat;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}