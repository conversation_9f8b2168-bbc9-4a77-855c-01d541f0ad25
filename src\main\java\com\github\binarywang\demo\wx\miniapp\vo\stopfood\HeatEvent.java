package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 热量事件对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "热量事件", description = "表示一个热量摄入或消耗事件")
public class HeatEvent {

    @Schema(description = "事件名称", example = "跑步半小时")
    private String eventName;

    @Schema(description = "事件类型", example = "消耗热量", allowableValues = { "摄入热量", "消耗热量" })
    private String type;

    @Schema(description = "热量值(千卡)", example = "300")
    private Integer heatValue;
}