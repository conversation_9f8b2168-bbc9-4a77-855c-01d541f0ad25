package com.github.binarywang.demo.wx.miniapp.utils;

import com.github.binarywang.demo.wx.miniapp.constant.TokenConstant;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * Token工具类
 */
public class TokenUtils {

    /**
     * 从请求头中获取Token，去掉Bearer前缀
     *
     * @param request HTTP请求
     * @return 提取出的token
     */
    public static String getTokenFromRequest(HttpServletRequest request) {
        String token = request.getHeader(TokenConstant.AUTHORIZATION_HEADER);
        if (StringUtils.hasText(token) && token.startsWith(TokenConstant.TOKEN_PREFIX_BEARER)) {
            // 去掉Bearer前缀
            return token.substring(TokenConstant.TOKEN_PREFIX_BEARER.length());
        }
        return null;
    }

    /**
     * 根据openId获取Redis中存储token的key
     *
     * @param openId 用户openId
     * @return Redis key
     */
    public static String getTokenKey(String openId) {
        return TokenConstant.TOKEN_PREFIX + openId;
    }
}