package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 微信运动步数请求DTO
 */
@Data
public class WeixinStepsRequest {

    /**
     * 当天的微信运动步数
     */
    @NotNull(message = "步数不能为空")
    @Min(value = 0, message = "步数不能小于0")
    private Integer steps;

    /**
     * 体重（kg），如果没有传入则默认为60kg
     */
    @Min(value = 1, message = "体重必须大于0")
    private Double weight = 60.0;
}