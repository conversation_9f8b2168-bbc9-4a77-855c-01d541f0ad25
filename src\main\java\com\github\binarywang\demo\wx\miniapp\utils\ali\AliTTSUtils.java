package com.github.binarywang.demo.wx.miniapp.utils.ali;

import okhttp3.*;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;
import com.google.gson.JsonObject;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import javax.sound.sampled.*;

/**
 * 阿里云语音合成工具类
 * 使用POST方法实现文字转语音功能
 */
public class AliTTSUtils {

    // 阿里云应用密钥配置（实际使用时应该从配置文件或环境变量获取）
    private static final String APP_KEY = "rAKtvRWKjVISmZV8"; // 替换为您的应用密钥

    // 阿里云语音合成API端点
    private static final String TTS_API_URL = "https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts";

    /**
     * TTS合成结果类
     */
    public static class TTSResult {
        private String ossUrl;
        private Integer durationSeconds;

        public TTSResult(String ossUrl, Integer durationSeconds) {
            this.ossUrl = ossUrl;
            this.durationSeconds = durationSeconds;
        }

        public String getOssUrl() {
            return ossUrl;
        }

        public Integer getDurationSeconds() {
            return durationSeconds;
        }

        @Override
        public String toString() {
            return "TTSResult{ossUrl='" + ossUrl + "', durationSeconds=" + durationSeconds + "}";
        }
    }

    /**
     * TTS参数配置类
     */
    public static class TTSParams {
        private String format = "mp3"; // 音频格式: pcm, wav, mp3
        private int sampleRate = 16000; // 采样率: 8000, 16000
        private String voice = "xiaoyun"; // 发音人
        private int volume = 50; // 音量: 0-100
        private int speechRate = 0; // 语速: -500 到 500
        private int pitchRate = 0; // 语调: -500 到 500

        // Getter和Setter方法
        public String getFormat() {
            return format;
        }

        public TTSParams setFormat(String format) {
            this.format = format;
            return this;
        }

        public int getSampleRate() {
            return sampleRate;
        }

        public TTSParams setSampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
            return this;
        }

        public String getVoice() {
            return voice;
        }

        public TTSParams setVoice(String voice) {
            this.voice = voice;
            return this;
        }

        public int getVolume() {
            return volume;
        }

        public TTSParams setVolume(int volume) {
            this.volume = volume;
            return this;
        }

        public int getSpeechRate() {
            return speechRate;
        }

        public TTSParams setSpeechRate(int speechRate) {
            this.speechRate = speechRate;
            return this;
        }

        public int getPitchRate() {
            return pitchRate;
        }

        public TTSParams setPitchRate(int pitchRate) {
            this.pitchRate = pitchRate;
            return this;
        }
    }

    /**
     * 获取默认的应用密钥
     * 
     * @return 应用密钥
     */
    public static String getDefaultAppKey() {
        return APP_KEY;
    }

    /**
     * 合成文本列表的语音并上传到阿里云OSS
     * 
     * @param sampleRate      采样率: 8000, 16000
     * @param voiceValue      发音人-value
     * @param volume          音量0-100
     * @param speechRateValue 语速-value
     * @param textList        要合成的文本内容列表
     * @param repeatCount     重复次数
     * @param pauseSeconds    停顿秒数
     * @return 语音文件在OSS上的URL，失败时返回null
     */
    public static String synthesizeVoiceOfTextListToOss(Integer sampleRate, String voiceValue, Integer volume,
            Integer speechRateValue, List<String> textList, Integer repeatCount, Float pauseSeconds) {

        TTSResult result = synthesizeVoiceOfTextListToOssWithDuration(sampleRate, voiceValue, volume,
                speechRateValue, textList, repeatCount, pauseSeconds);
        return result != null ? result.getOssUrl() : null;
    }

    /**
     * 合成文本列表的语音并上传到阿里云OSS（带时长信息）
     * 
     * @param sampleRate      采样率: 8000, 16000
     * @param voiceValue      发音人-value
     * @param volume          音量0-100
     * @param speechRateValue 语速-value
     * @param textList        要合成的文本内容列表
     * @param repeatCount     重复次数
     * @param pauseSeconds    停顿秒数
     * @return TTSResult对象，包含OSS URL和音频时长，失败时返回null
     */
    public static TTSResult synthesizeVoiceOfTextListToOssWithDuration(Integer sampleRate, String voiceValue,
            Integer volume,
            Integer speechRateValue, List<String> textList, Integer repeatCount, Float pauseSeconds) {

        return synthesizeVoiceOfTextListToOssWithDuration(sampleRate, voiceValue, volume, speechRateValue,
                textList, repeatCount, pauseSeconds, null);
    }

    /**
     * 合成文本列表的语音并上传到阿里云OSS（带时长信息，支持自定义文件名）
     * 
     * @param sampleRate      采样率: 8000, 16000
     * @param voiceValue      发音人-value
     * @param volume          音量0-100
     * @param speechRateValue 语速-value
     * @param textList        要合成的文本内容列表
     * @param repeatCount     重复次数
     * @param pauseSeconds    停顿秒数
     * @param customFileName  自定义文件名（不包含扩展名），为null时使用默认命名
     * @return TTSResult对象，包含OSS URL和音频时长，失败时返回null
     */
    public static TTSResult synthesizeVoiceOfTextListToOssWithDuration(Integer sampleRate, String voiceValue,
            Integer volume, Integer speechRateValue, List<String> textList, Integer repeatCount,
            Float pauseSeconds, String customFileName) {

        try {
            // 第一步：获取Access Token（使用默认凭据）
            AliAccessTokenUtils.TokenInfo tokenInfo = AliAccessTokenUtils.getAccessToken();

            if (tokenInfo == null || tokenInfo.isExpired()) {
                System.err.println("获取阿里云Token失败或已过期");
                return null;
            }

            String token = tokenInfo.getToken();

            // 第二步：构建合成文本
            String combinedText = buildCombinedText(textList, repeatCount, pauseSeconds);

            // 第三步：构建TTS参数
            TTSParams params = new TTSParams()
                    .setFormat("mp3")
                    .setSampleRate(sampleRate != null ? sampleRate : 16000)
                    .setVoice(voiceValue != null ? voiceValue : "xiaoyun")
                    .setVolume(volume != null ? volume : 50)
                    .setSpeechRate(speechRateValue != null ? speechRateValue : 0)
                    .setPitchRate(0); // 语调使用默认值

            // 第四步：调用阿里云TTS API（使用默认appkey）
            byte[] audioBytes = synthesizeSpeechToBytes(getDefaultAppKey(), token, combinedText, params);

            if (audioBytes == null) {
                System.err.println("语音合成失败");
                return null;
            }

            // 第五步：使用新的音频时长计算工具
            String originalText = String.join("", textList); // 获取原始文本用于分析
            AudioDurationUtils.DurationResult durationResult = AudioDurationUtils.calculateAudioDuration(
                    audioBytes, originalText, params.getSpeechRate(), params.getSampleRate());

            Integer durationSeconds = (durationResult != null) ? durationResult.getDurationSeconds() : 1;

            System.out.println("音频时长计算结果: " + durationResult);

            // 第六步：上传到OSS
            String fileName;
            if (customFileName != null && !customFileName.trim().isEmpty()) {
                fileName = customFileName.trim() + ".mp3";
            } else {
                fileName = "ali_speech_" + System.currentTimeMillis() + ".mp3";
            }
            InputStream inputStream = new ByteArrayInputStream(audioBytes);
            String ossUrl = AliOssUtils.uploadFileToOss(inputStream, "memory_words", fileName);

            System.out.println("阿里云语音合成完成，音频文件已上传到OSS: " + ossUrl);
            System.out.println("音频时长: " + durationSeconds + " 秒");

            return new TTSResult(ossUrl, durationSeconds);

        } catch (Exception e) {
            System.err.println("语音合成过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 构建合成文本，支持重复和停顿
     * 
     * @param textList     文本列表
     * @param repeatCount  重复次数
     * @param pauseSeconds 停顿秒数
     * @return 合成后的文本
     */
    private static String buildCombinedText(List<String> textList, Integer repeatCount, Float pauseSeconds) {
        StringBuilder ssmlBuilder = new StringBuilder();
        ssmlBuilder.append("<speak>");
        ssmlBuilder.append("<break time=\"1.0s\"/>");
        for (String text : textList) {
            for (int i = 0; i < repeatCount; i++) {
                ssmlBuilder.append(text);
                ssmlBuilder.append("<break time=\"" + pauseSeconds + "s\"/>");
            }
        }
        ssmlBuilder.append("</speak>");

        return ssmlBuilder.toString();
    }

    /**
     * 调用阿里云TTS API合成语音并返回音频字节数组
     * 
     * @param appkey 应用密钥
     * @param token  访问令牌
     * @param text   待合成的文本
     * @param params TTS参数配置
     * @return 音频字节数组，失败时返回null
     */
    private static byte[] synthesizeSpeechToBytes(String appkey, String token, String text, TTSParams params) {
        try {
            // 构建请求JSON
            JsonObject jsonBody = new JsonObject();
            jsonBody.addProperty("appkey", appkey);
            jsonBody.addProperty("text", text);
            jsonBody.addProperty("token", token);
            jsonBody.addProperty("format", params.getFormat());
            jsonBody.addProperty("sample_rate", params.getSampleRate());
            jsonBody.addProperty("voice", params.getVoice());
            jsonBody.addProperty("volume", params.getVolume());
            jsonBody.addProperty("speech_rate", params.getSpeechRate());
            jsonBody.addProperty("pitch_rate", params.getPitchRate());

            // 创建请求体
            RequestBody requestBody = RequestBody.create(
                    jsonBody.toString(),
                    MediaType.parse("application/json; charset=utf-8"));

            // 构建请求
            Request request = new Request.Builder()
                    .url(TTS_API_URL)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("X-NLS-Token", token)
                    .build();

            // 执行请求
            try (Response response = OkHttpClientUtil.getInstance().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    System.err.println("HTTP请求失败: " + response.code() + " " + response.message());
                    return null;
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    System.err.println("响应体为空");
                    return null;
                }

                // 检查响应类型
                String contentType = response.header("Content-Type", "");
                if (contentType.contains("application/json")) {
                    // 错误响应
                    String errorMessage = responseBody.string();
                    System.err.println("API错误: " + errorMessage);
                    return null;
                } else if (contentType.contains("audio/")) {
                    // 成功响应，返回音频字节数组
                    byte[] audioBytes = responseBody.bytes();
                    System.out.println("语音合成成功，音频大小: " + audioBytes.length + " 字节");
                    return audioBytes;
                } else {
                    System.err.println("未知的响应类型: " + contentType);
                    return null;
                }
            }
        } catch (IOException e) {
            System.err.println("网络请求异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        } catch (Exception e) {
            System.err.println("未知异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
