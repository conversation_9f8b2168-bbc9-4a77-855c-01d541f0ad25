package com.github.binarywang.demo.wx.miniapp.dto.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "获取课文标题列表请求参数")
public class MwTextbookTitlesQueryDTO {

    @Schema(description = "学科")
    private String subject;

    @Schema(description = "教材版本")
    private String textbookVersion;

    @Schema(description = "年级")
    private Integer grade;

    @Schema(description = "学期")
    private Integer term;
}