package com.github.binarywang.demo.wx.miniapp.utils.baidu;

import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;

public class BaiduChatErnieUtils {

    public static final String API_KEY = "M1E5JWvJz48JJVU8uvO1Wkrn";
    public static final String SECRET_KEY = "iQqKIYd00YZlb7oyDHfbvQiW9POhvLM2";

    public static final String BAIDU_CHAT_ERNIE_35_8K = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions";

    public static final Map<String, Object> BAIDU_CHAT_ERNIE_PARAMS_MAP = new HashMap<>();
    static {
        // BAIDU_CHAT_ERNIE_PARAMS_MAP.put("stream", false);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("temperature", 0.95);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("top_p", 0.8);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("penalty_score", 1);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("enable_system_memory", false);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("disable_search", false);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("enable_citation", false);
        BAIDU_CHAT_ERNIE_PARAMS_MAP.put("response_format", "json_object");
    }

    public static String call(Map<String, Object> contentMap) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        String jsonString = JSONObject.toJSONString(contentMap);
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                // .url("https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token="
                .url(BAIDU_CHAT_ERNIE_35_8K + "?access_token="
                        + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = OkHttpClientUtil.getInstance().newCall(request).execute();
        String responseString = response.body().string();
        return responseString;
    }

    public static JSONArray parseResponseString(String responseString) {
        JSONObject jsonObject = JSONObject.parseObject(responseString);
        JSONObject resultObject = jsonObject.getJSONObject("result");
        JSONArray jieguoArray = resultObject.getJSONArray("结果");
        return jieguoArray;
    }

    public static Map<String, Object> getContentMap(String wholePrompt) {
        Map<String, Object> messagesMap = new HashMap<>();
        messagesMap.put("role", "user");
        messagesMap.put("content", wholePrompt);
        System.out.println(wholePrompt);

        Map<String, Object> contentMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(messagesMap);
        contentMap.put("messages", list);
        contentMap.putAll(BaiduChatErnieUtils.BAIDU_CHAT_ERNIE_PARAMS_MAP);
        return contentMap;
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     * 使用统一的百度AccessToken管理器
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getAccessToken() throws IOException {
        return BaiduAccessTokenManager.getAccessToken(API_KEY, SECRET_KEY);
    }

}
