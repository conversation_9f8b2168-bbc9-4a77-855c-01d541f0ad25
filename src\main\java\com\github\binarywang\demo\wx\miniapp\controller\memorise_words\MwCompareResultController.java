package com.github.binarywang.demo.wx.miniapp.controller.memorise_words;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.GetMapping;

import com.github.binarywang.demo.wx.miniapp.utils.ali.AliOssUtils;
import com.github.binarywang.demo.wx.miniapp.utils.baidu.BaiduHandwritingOcrUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.MwCompareResultRequest;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsCompareResultService;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwCompareResultVO;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 记忆单词-比对结果控制器
 */
@RestController
@RequestMapping("/memoriseWords/compareResult")
@Tag(name = "记忆单词-比对结果")
@Slf4j
public class MwCompareResultController {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private MemoryWordsCompareResultService compareResultService;

    /**
     * 识别用户手写单词
     * 将用户上传的图片上传到阿里云OSS，然后通过百度手写文字识别API识别图片中的文字内容
     * 
     * @param file        用户上传的图片文件
     * @param httpRequest HTTP请求
     * @return 识别出的文字内容
     */
    @PostMapping("/recognizeUserWords")
    @Operation(summary = "识别用户手写单词", description = "上传图片文件识别手写单词，支持jpg、png等图片格式")
    public ApiResponse<String> recognizeUserWords(
            @Parameter(description = "要上传的图片文件", required = true) @RequestParam("file") MultipartFile file,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 验证文件
            if (file == null || file.isEmpty()) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "图片文件不能为空");
            }

            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (StringUtils.isBlank(originalFilename)) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "文件名不能为空");
            }

            String fileExtension = getFileExtension(originalFilename);
            if (!isValidImageExtension(fileExtension)) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                        "不支持的图片格式，请上传jpg、jpeg、png、bmp、gif格式的图片");
            }

            log.info("用户[{}]上传手写单词识别图片，文件名：{}，大小：{} bytes",
                    openId, originalFilename, file.getSize());

            // 第1步：将用户上传的图片上传到阿里云OSS
            String fileName = generateFileName(openId, fileExtension);
            String ossImageUrl = AliOssUtils.uploadFileToOss(file.getInputStream(), "memory_words", fileName);

            if (StringUtils.isBlank(ossImageUrl)) {
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "图片上传到OSS失败");
            }

            log.info("图片上传OSS成功，URL：{}", ossImageUrl);

            // 第2步：调用百度手写文字识别API
            BaiduHandwritingOcrUtils.OcrResult ocrResult = BaiduHandwritingOcrUtils.recognizeOnlineImage(ossImageUrl);

            if (ocrResult == null) {
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "OCR识别服务调用失败");
            }

            if (!ocrResult.isSuccess()) {
                log.error("OCR识别失败，错误信息：{}", ocrResult.getErrorMessage());
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR,
                        "文字识别失败：" + ocrResult.getErrorMessage());
            }

            // 第3步：解析识别结果，获取所有文字内容
            String recognizedText = BaiduHandwritingOcrUtils.getAllText(ocrResult);

            log.info("用户[{}]手写单词识别完成，识别到文字：{}", openId, recognizedText);

            return ApiResponse.success("手写单词识别成功", recognizedText);

        } catch (IOException e) {
            log.error("处理文件时发生IO异常", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "文件处理失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("识别用户手写单词时发生异常", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "系统错误：" + e.getMessage());
        }
    }

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名（小写）
     */
    private String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 验证是否为有效的图片扩展名
     * 
     * @param extension 文件扩展名
     * @return 是否有效
     */
    private boolean isValidImageExtension(String extension) {
        return StringUtils.isNotBlank(extension) &&
                ("jpg".equals(extension) || "jpeg".equals(extension) ||
                        "png".equals(extension) || "bmp".equals(extension) || "gif".equals(extension));
    }

    /**
     * 新增比对结果
     * 根据relationId获取原始内容，与用户输入内容进行比对，并保存结果
     * 
     * @param request     比对结果请求参数
     * @param httpRequest HTTP请求
     * @return 比对结果
     */
    @PostMapping("/addCompareResult")
    @Operation(summary = "新增比对结果", description = "根据relationId获取原始内容，与用户输入内容进行比对，并保存结果")
    public ApiResponse<MwCompareResultVO> addCompareResult(
            @Parameter(description = "比对结果请求参数", required = true) @Valid @RequestBody MwCompareResultRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            String unionId = null; // JwtUtils可能没有getUnionidFromToken方法

            log.info("用户[{}]请求新增比对结果，relationId: {}", openId, request.getRelationId());

            // 调用Service处理比对逻辑
            MwCompareResultVO result = compareResultService.addCompareResult(request, openId, unionId);

            log.info("用户[{}]比对结果处理完成", openId);

            return ApiResponse.success("比对结果保存成功", result);

        } catch (Exception e) {
            log.error("新增比对结果时发生异常", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "系统错误：" + e.getMessage());
        }
    }

    /**
     * 分页查询录音历史批改结果
     * 
     * @param relationId  录音与用户关系id，可为空
     * @param pageNum     页码，默认1
     * @param pageSize    每页大小，默认10
     * @param httpRequest HTTP请求
     * @return 分页结果
     */
    @GetMapping("/pageCompareResults")
    @Operation(summary = "分页查询录音历史批改结果", description = "根据 relation_id 分页查询比对结果，按 create_time 倒序")
    public ApiResponse<com.github.binarywang.demo.wx.miniapp.vo.PageResult<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult>> pageCompareResults(
            @RequestParam(name = "relationId") Long relationId,
            @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            HttpServletRequest httpRequest) {
        try {
            if (relationId == null) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "relationId不能为空");
            }
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }
            com.github.binarywang.demo.wx.miniapp.vo.PageResult<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult> pageResult = compareResultService
                    .pageCompareResults(openId, relationId, pageNum, pageSize);
            return ApiResponse.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("分页查询录音历史批改结果异常", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据id查询单条比对结果
     * 
     * @param id          比对结果主键id
     * @param httpRequest HTTP请求
     * @return 比对结果VO
     */
    @GetMapping("/getCompareResultById")
    @Operation(summary = "根据id查询单条比对结果", description = "根据比对结果主键id查询单条比对结果详情")
    public ApiResponse<MwCompareResultVO> getCompareResultById(
            @RequestParam(name = "id") Long id,
            HttpServletRequest httpRequest) {
        try {
            if (id == null) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "比对结果ID不能为空");
            }

            // 验证token并获取openid（可选，用于权限验证）
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isNotBlank(token)) {
                String openId = jwtUtils.getOpenidFromToken(token);
                if (StringUtils.isBlank(openId)) {
                    return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
                }
            }

            MwCompareResultVO result = compareResultService.getCompareResultById(id);
            return ApiResponse.success("查询成功", result);
        } catch (Exception e) {
            log.error("根据id查询单条比对结果异常，id: {}", id, e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件名
     * 
     * @param openId    用户openId
     * @param extension 文件扩展名
     * @return 生成的文件名
     */
    private String generateFileName(String openId, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortOpenId = openId.length() > 8 ? openId.substring(openId.length() - 8) : openId;
        return "handwriting_" + shortOpenId + "_" + timestamp + "." + extension;
    }
}