package com.github.binarywang.demo.wx.miniapp.utils.ali;

import okhttp3.*;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 阿里云智能语音交互Access Token获取工具类
 * 基于阿里云POP协议实现Token获取功能
 */
public class AliAccessTokenUtils {

    // 阿里云凭据配置（实际使用时应该从配置文件或环境变量获取）
    private static final String ACCESS_KEY_ID = "LTAI5tAuUdMvmtDuaaWRUJQc"; // 替换为您的AccessKey ID
    private static final String ACCESS_KEY_SECRET = "******************************"; // 替换为您的AccessKey Secret

    // 阿里云获取Token的API端点
    private static final String TOKEN_API_URL = "http://nls-meta.cn-shanghai.aliyuncs.com/";

    // Token缓存相关静态变量
    private static TokenInfo cachedTokenInfo = null;
    private static String cachedAccessKeyId = null;
    private static String cachedAccessKeySecret = null;
    private static final Object TOKEN_LOCK = new Object();

    /**
     * Token信息类
     */
    public static class TokenInfo {
        private String token;
        private long expireTime;

        public TokenInfo(String token, long expireTime) {
            this.token = token;
            this.expireTime = expireTime;
        }

        public String getToken() {
            return token;
        }

        public long getExpireTime() {
            return expireTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() / 1000 >= expireTime;
        }

        public String getExpireDateString() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new Date(expireTime * 1000));
        }

        @Override
        public String toString() {
            return "TokenInfo{token='" + token + "', expireTime=" + expireTime +
                    ", expireDate='" + getExpireDateString() + "'}";
        }
    }

    /**
     * 获取阿里云智能语音交互的Access Token（使用默认凭据）
     * 
     * @return TokenInfo对象，包含token和过期时间，失败时返回null
     */
    public static TokenInfo getAccessToken() {
        return getAccessToken(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
    }

    /**
     * 获取阿里云智能语音交互的Access Token（带生命周期管理）
     * 如果缓存的Token未过期且AccessKey未变化，则返回缓存的Token
     * 否则重新获取新的Token
     * 
     * @param accessKeyId     阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @return TokenInfo对象，包含token和过期时间，失败时返回null
     */
    public static TokenInfo getAccessToken(String accessKeyId, String accessKeySecret) {
        synchronized (TOKEN_LOCK) {
            // 检查缓存的Token是否有效
            if (cachedTokenInfo != null
                    && !cachedTokenInfo.isExpired()
                    && accessKeyId.equals(cachedAccessKeyId)
                    && accessKeySecret.equals(cachedAccessKeySecret)) {

                return cachedTokenInfo;
            }

            // 缓存失效或AccessKey变化，重新获取Token
            TokenInfo newTokenInfo = getAccessTokenDirect(accessKeyId, accessKeySecret);

            if (newTokenInfo != null) {
                // 更新缓存
                cachedTokenInfo = newTokenInfo;
                cachedAccessKeyId = accessKeyId;
                cachedAccessKeySecret = accessKeySecret;
            }

            return newTokenInfo;
        }
    }

    /**
     * 获取默认的AccessKey ID
     * 
     * @return AccessKey ID
     */
    public static String getDefaultAccessKeyId() {
        return ACCESS_KEY_ID;
    }

    /**
     * 获取默认的AccessKey Secret
     * 
     * @return AccessKey Secret
     */
    public static String getDefaultAccessKeySecret() {
        return ACCESS_KEY_SECRET;
    }

    /**
     * 直接获取阿里云智能语音交互的Access Token（不使用缓存）
     * 
     * @param accessKeyId     阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @return TokenInfo对象，包含token和过期时间，失败时返回null
     */
    private static TokenInfo getAccessTokenDirect(String accessKeyId, String accessKeySecret) {
        try {
            // 构建请求参数
            Map<String, String> parameters = new HashMap<>();
            parameters.put("AccessKeyId", accessKeyId);
            parameters.put("Action", "CreateToken");
            parameters.put("Format", "JSON");
            parameters.put("RegionId", "cn-shanghai");
            parameters.put("SignatureMethod", "HMAC-SHA1");
            parameters.put("SignatureNonce", UUID.randomUUID().toString());
            parameters.put("SignatureVersion", "1.0");
            parameters.put("Timestamp", getCurrentTimestamp());
            parameters.put("Version", "2019-02-28");

            // 构造规范化的请求字符串
            String queryString = canonicalizedQuery(parameters);
            if (queryString == null) {
                System.err.println("构造规范化的请求字符串失败！");
                return null;
            }

            // 构造签名字符串
            String method = "GET";
            String urlPath = "/";
            String stringToSign = createStringToSign(method, urlPath, queryString);
            if (stringToSign == null) {
                System.err.println("构造签名字符串失败");
                return null;
            }

            // 计算签名
            String signature = sign(stringToSign, accessKeySecret + "&");
            if (signature == null) {
                System.err.println("计算签名失败!");
                return null;
            }

            // 构造完整的请求URL
            String fullUrl = TOKEN_API_URL + "?Signature=" + urlEncode(signature) + "&" + queryString;

            // 发送HTTP GET请求
            Request request = new Request.Builder()
                    .url(fullUrl)
                    .get()
                    .addHeader("Accept", "application/json")
                    .build();

            try (Response response = OkHttpClientUtil.getInstance().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    System.err.println("获取Token HTTP请求失败: " + response.code() + " " + response.message());
                    return null;
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    System.err.println("获取Token响应体为空");
                    return null;
                }

                String responseString = responseBody.string();

                // 解析JSON响应
                Gson gson = new Gson();
                JsonObject jsonResponse = gson.fromJson(responseString, JsonObject.class);

                if (jsonResponse.has("Token")) {
                    JsonObject tokenObj = jsonResponse.getAsJsonObject("Token");
                    String token = tokenObj.get("Id").getAsString();
                    long expireTime = tokenObj.get("ExpireTime").getAsLong();

                    return new TokenInfo(token, expireTime);
                } else {
                    System.err.println("Token响应格式错误: " + responseString);
                    return null;
                }
            }

        } catch (Exception e) {
            System.err.println("获取Token异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前UTC时间戳（ISO 8601格式）
     */
    private static String getCurrentTimestamp() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        df.setTimeZone(TimeZone.getTimeZone("UTC"));
        return df.format(new Date());
    }

    /**
     * 构造规范化的请求字符串
     */
    private static String canonicalizedQuery(Map<String, String> parameters) {
        try {
            List<String> sortedKeys = new ArrayList<>(parameters.keySet());
            Collections.sort(sortedKeys);

            List<String> pairs = new ArrayList<>();
            for (String key : sortedKeys) {
                String value = parameters.get(key);
                pairs.add(urlEncode(key) + "=" + urlEncode(value));
            }

            return String.join("&", pairs);
        } catch (Exception e) {
            System.err.println("构造规范化请求字符串异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 构造待签名字符串
     */
    private static String createStringToSign(String method, String urlPath, String queryString) {
        try {
            return method + "&" + urlEncode(urlPath) + "&" + urlEncode(queryString);
        } catch (Exception e) {
            System.err.println("构造待签名字符串异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 计算HMAC-SHA1签名
     */
    private static String sign(String stringToSign, String secret) {
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
            mac.init(keySpec);
            byte[] rawHmac = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(rawHmac);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            System.err.println("计算签名异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * URL编码（符合阿里云POP规范）
     */
    private static String urlEncode(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            System.err.println("URL编码异常: " + e.getMessage());
            return value;
        }
    }
}
