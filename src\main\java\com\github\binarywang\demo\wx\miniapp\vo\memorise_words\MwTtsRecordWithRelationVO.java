package com.github.binarywang.demo.wx.miniapp.vo.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 记忆单词-TTS记录（包含关系ID）VO
 */
@Data
@Schema(description = "TTS记录（包含关系ID）")
public class MwTtsRecordWithRelationVO {

    @Schema(description = "TTS记录ID")
    private Long id;

    @Schema(description = "关系表主键ID（relation_id）")
    private Long relationId;

    @Schema(description = "创建人-微信openid")
    private String openId;

    @Schema(description = "创建人-微信unionid")
    private String unionId;

    @Schema(description = "接口类型，可选值'火山引擎'或'阿里云'")
    private String apiType;

    @Schema(description = "环境，可选值为'中文'或'英文'")
    private String environment;

    @Schema(description = "音色类型-key，如 普通话、英式英语、美式英语")
    private String voiceType;

    @Schema(description = "音色类型-value，如BV700_streaming、BV040_streaming、BV027_streaming")
    private String voiceValue;

    @Schema(description = "合成的文本内容")
    private String textContent;

    @Schema(description = "文本类型，plain(普通文本)或ssml(带标记语言)")
    private String textType;

    @Schema(description = "语速-key")
    private String speedKey;

    @Schema(description = "语速-value，实际值")
    private Float speedValue;

    @Schema(description = "重复次数，仅在textType=ssml时生效")
    private Integer repeatCount;

    @Schema(description = "停顿秒数，仅在textType=ssml时生效")
    private Float pauseSeconds;

    @Schema(description = "合成后的语音文件URL")
    private String audioUrl;

    @Schema(description = "语音时长(秒)")
    private Integer audioDuration;

    @Schema(description = "状态：0-处理中，1-成功，2-失败")
    private Byte status;

    @Schema(description = "错误信息，仅在状态为失败时有值")
    private String errorMsg;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}