<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.WeixinUserMapper">
    
    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.WeixinUser" id="WeixinUserResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="phone" column="phone"/>
        <result property="nickName" column="nick_name"/>
        <result property="gender" column="gender"/>
        <result property="birthDate" column="birth_date"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <sql id="selectWeixinUserVo">
        select id, open_id, union_id, phone, nick_name, gender, birth_date, height, weight, avatar_url, login_ip, login_date, create_time, update_time, remark
        from weixin_user
    </sql>
    
    <!-- 查询单个用户 -->
    <select id="selectWeixinUserById" parameterType="Long" resultMap="WeixinUserResult">
        <include refid="selectWeixinUserVo"/>
        where id = #{id}
    </select>
    
    <!-- 根据OpenId查询用户 -->
    <select id="selectWeixinUserByOpenId" parameterType="String" resultMap="WeixinUserResult">
        <include refid="selectWeixinUserVo"/>
        where open_id = #{openId}
    </select>
    
    <!-- 根据手机号查询用户 -->
    <select id="selectWeixinUserByPhone" parameterType="String" resultMap="WeixinUserResult">
        <include refid="selectWeixinUserVo"/>
        where phone = #{phone}
    </select>
    
    <!-- 插入用户 -->
    <insert id="insertWeixinUser" parameterType="com.github.binarywang.demo.wx.miniapp.entity.WeixinUser" useGeneratedKeys="true" keyProperty="id">
        insert into weixin_user (
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="gender != null">gender,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="height != null">height,</if>
            <if test="weight != null">weight,</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url,</if>
            <if test="loginIp != null and loginIp != ''">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
            update_time
        ) values (
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="height != null">#{height},</if>
            <if test="weight != null">#{weight},</if>
            <if test="avatarUrl != null and avatarUrl != ''">#{avatarUrl},</if>
            <if test="loginIp != null and loginIp != ''">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
            sysdate()
        )
    </insert>
    
    <!-- 更新用户 -->
    <update id="updateWeixinUser" parameterType="com.github.binarywang.demo.wx.miniapp.entity.WeixinUser">
        update weixin_user
        <set>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="height != null">height = #{height},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url = #{avatarUrl},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where open_id = #{openId}
    </update>
</mapper> 