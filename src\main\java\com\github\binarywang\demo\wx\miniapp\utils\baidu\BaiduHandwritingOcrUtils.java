package com.github.binarywang.demo.wx.miniapp.utils.baidu;

import okhttp3.*;
import org.json.JSONObject;
import org.json.JSONArray;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;

import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 百度手写文字识别工具类
 * 支持在线图片的手写文字识别
 */
public class BaiduHandwritingOcrUtils {

    // API Key和Secret Key
    public static final String API_KEY = "m1jBAOcLbkvBsGU9I5tpAWjO";
    public static final String SECRET_KEY = "sIT94FRSvnkS2c942CmV43SzMlnYAF6A";

    // 百度OCR手写识别接口地址
    private static final String OCR_HANDWRITING_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting";

    /**
     * 识别结果封装类
     */
    public static class OcrResult {
        private boolean success;
        private String errorMessage;
        private int wordsResultNum;
        private List<WordResult> wordsResult;
        private long logId;

        public OcrResult() {
            this.wordsResult = new ArrayList<>();
        }

        // getter和setter方法
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public int getWordsResultNum() {
            return wordsResultNum;
        }

        public void setWordsResultNum(int wordsResultNum) {
            this.wordsResultNum = wordsResultNum;
        }

        public List<WordResult> getWordsResult() {
            return wordsResult;
        }

        public void setWordsResult(List<WordResult> wordsResult) {
            this.wordsResult = wordsResult;
        }

        public long getLogId() {
            return logId;
        }

        public void setLogId(long logId) {
            this.logId = logId;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("OcrResult{");
            sb.append("success=").append(success);
            if (!success) {
                sb.append(", errorMessage='").append(errorMessage).append('\'');
            } else {
                sb.append(", wordsResultNum=").append(wordsResultNum);
                sb.append(", logId=").append(logId);
                sb.append(", words=[");
                for (int i = 0; i < wordsResult.size(); i++) {
                    if (i > 0)
                        sb.append(", ");
                    sb.append(wordsResult.get(i).getWords());
                }
                sb.append("]");
            }
            sb.append("}");
            return sb.toString();
        }
    }

    /**
     * 单个文字识别结果
     */
    public static class WordResult {
        private String words;
        private Location location;

        public String getWords() {
            return words;
        }

        public void setWords(String words) {
            this.words = words;
        }

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }
    }

    /**
     * 文字位置信息
     */
    public static class Location {
        private int left;
        private int top;
        private int width;
        private int height;

        public int getLeft() {
            return left;
        }

        public void setLeft(int left) {
            this.left = left;
        }

        public int getTop() {
            return top;
        }

        public void setTop(int top) {
            this.top = top;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }
    }

    /**
     * 识别在线图片中的手写文字
     * 
     * @param imageUrl 在线图片URL
     * @return 识别结果
     */
    public static OcrResult recognizeOnlineImage(String imageUrl) {
        return recognizeOnlineImage(imageUrl, false, false, false);
    }

    /**
     * 识别在线图片中的手写文字（带详细参数）
     * 
     * @param imageUrl         在线图片URL
     * @param detectDirection  是否检测图像朝向
     * @param probability      是否返回识别结果中每一行的置信度
     * @param detectAlteration 是否检测图像篡改
     * @return 识别结果
     */
    public static OcrResult recognizeOnlineImage(String imageUrl, boolean detectDirection,
            boolean probability, boolean detectAlteration) {
        try {
            // 直接使用URL进行OCR识别，不需要下载图片
            return performOcrWithUrl(imageUrl, detectDirection, probability, detectAlteration);

        } catch (Exception e) {
            OcrResult result = new OcrResult();
            result.setSuccess(false);
            result.setErrorMessage("处理在线图片时发生错误: " + e.getMessage());
            return result;
        }
    }

    /**
     * 执行OCR识别（使用图片URL）
     * 
     * @param imageUrl         图片URL地址
     * @param detectDirection  是否检测图像朝向
     * @param probability      是否返回置信度
     * @param detectAlteration 是否检测篡改
     * @return 识别结果
     */
    private static OcrResult performOcrWithUrl(String imageUrl, boolean detectDirection,
            boolean probability, boolean detectAlteration) {
        try {
            // 获取access token
            String token = getAccessToken();
            if (token == null) {
                OcrResult result = new OcrResult();
                result.setSuccess(false);
                result.setErrorMessage("获取access token失败");
                return result;
            }

            // 构建请求参数，使用url参数而不是image参数
            StringBuilder params = new StringBuilder();
            params.append("url=").append(URLEncoder.encode(imageUrl, "utf-8"));
            params.append("&detect_direction=").append(detectDirection);
            params.append("&probability=").append(probability);
            params.append("&detect_alteration=").append(detectAlteration);

            MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
            RequestBody body = RequestBody.create(mediaType, params.toString());

            Request request = new Request.Builder()
                    .url(OCR_HANDWRITING_URL + "?access_token=" + token)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "application/json")
                    .build();

            Response response = OkHttpClientUtil.getInstance().newCall(request).execute();
            String responseBody = response.body().string();

            // 解析响应结果
            return parseOcrResponse(responseBody);

        } catch (Exception e) {
            OcrResult result = new OcrResult();
            result.setSuccess(false);
            result.setErrorMessage("OCR识别过程中发生错误: " + e.getMessage());
            return result;
        }
    }

    /**
     * 解析OCR响应结果
     * 
     * @param responseBody 响应JSON字符串
     * @return 解析后的结果
     */
    private static OcrResult parseOcrResponse(String responseBody) {
        OcrResult result = new OcrResult();

        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查是否有错误
            if (jsonResponse.has("error_code")) {
                result.setSuccess(false);
                String errorMsg = jsonResponse.optString("error_msg", "未知错误");
                int errorCode = jsonResponse.optInt("error_code", -1);
                result.setErrorMessage("错误代码: " + errorCode + ", 错误信息: " + errorMsg);
                return result;
            }

            // 解析成功结果
            result.setSuccess(true);
            result.setLogId(jsonResponse.optLong("log_id", 0));
            result.setWordsResultNum(jsonResponse.optInt("words_result_num", 0));

            if (jsonResponse.has("words_result")) {
                JSONArray wordsArray = jsonResponse.getJSONArray("words_result");
                List<WordResult> wordResults = new ArrayList<>();

                for (int i = 0; i < wordsArray.length(); i++) {
                    JSONObject wordObj = wordsArray.getJSONObject(i);
                    WordResult wordResult = new WordResult();
                    wordResult.setWords(wordObj.optString("words", ""));

                    // 解析位置信息
                    if (wordObj.has("location")) {
                        JSONObject locationObj = wordObj.getJSONObject("location");
                        Location location = new Location();
                        location.setLeft(locationObj.optInt("left", 0));
                        location.setTop(locationObj.optInt("top", 0));
                        location.setWidth(locationObj.optInt("width", 0));
                        location.setHeight(locationObj.optInt("height", 0));
                        wordResult.setLocation(location);
                    }

                    wordResults.add(wordResult);
                }

                result.setWordsResult(wordResults);
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("解析响应结果时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     * 使用统一的百度AccessToken管理器
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getAccessToken() throws IOException {
        return BaiduAccessTokenManager.getAccessToken(API_KEY, SECRET_KEY);
    }

    /**
     * 获取识别结果中的所有文字内容（简化版）
     * 
     * @param result OCR识别结果
     * @return 拼接后的文字内容
     */
    public static String getAllText(OcrResult result) {
        if (result == null || !result.isSuccess() || result.getWordsResult() == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (WordResult wordResult : result.getWordsResult()) {
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(wordResult.getWords());
        }
        return sb.toString();
    }
}