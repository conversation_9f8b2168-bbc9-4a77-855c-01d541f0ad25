package com.github.binarywang.demo.wx.miniapp.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 通用-用户建议表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonUserSuggestions implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionid
     */
    private String unionId;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 用户意见
     */
    private String suggestion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}