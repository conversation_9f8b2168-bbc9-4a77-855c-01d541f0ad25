package com.github.binarywang.demo.wx.miniapp.controller.memorise_words;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.GetMapping;

import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.AliTTSParamsDto;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsAliTones;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsAliTonesService;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsTtsRecordService;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsUserAndRecordRelationService;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsUserAndRecordRelation;
import com.github.binarywang.demo.wx.miniapp.utils.VolcanoEngineTTSUtil;
import com.github.binarywang.demo.wx.miniapp.utils.ali.AliTTSUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTtsRecordWithRelationVO;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;

/**
 * 阿里云-语音合成控制器
 */
@RestController
@RequestMapping("/memoriseWords/aliTts")
@Tag(name = "记忆单词-阿里云-语音合成")
@Slf4j
public class MwAliTtsController {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private MemoryWordsTtsRecordService ttsRecordService;

    @Autowired
    private MemoryWordsAliTonesService memoryWordsAliTonesService;

    @Autowired
    private MemoryWordsUserAndRecordRelationService relationService;

    /**
     * 阿里云语音合成接口
     * 将文本转换为语音，使用阿里云TTS服务。
     * 成功后会在memory_words_tts_record表中创建录音记录，
     * 并在memory_words_user_and_record_relation表中创建用户与录音的关系记录。
     * 
     * @param paramsDto   请求参数
     * @param httpRequest HTTP请求
     * @return 返回OSS音频文件URL
     */
    @PostMapping("/synthesize")
    @Operation(summary = "阿里云语音合成", description = "将文本转换为语音，使用阿里云TTS服务，并创建用户与录音的关系记录")
    public ApiResponse<String> synthesize(
            @RequestBody AliTTSParamsDto paramsDto,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 验证请求参数
            if (paramsDto == null || StringUtils.isBlank(paramsDto.getTextContent())) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "文本内容不能为空");
            }

            String environment = paramsDto.getEnvironment();
            String voiceKey = paramsDto.getVoiceKey();
            String speechRateKey = paramsDto.getSpeechRateKey();

            // 从阿里云音色表获取配置信息
            MemoryWordsAliTones aliTones = memoryWordsAliTonesService.getAliTonesInfo(environment, voiceKey,
                    speechRateKey);

            Integer sampleRate;
            String voiceValue;
            Integer volume;
            Integer speechRateValue;

            if (aliTones != null) {
                // 从数据库获取配置
                sampleRate = aliTones.getSampleRate();
                voiceValue = aliTones.getVoiceValue();
                volume = paramsDto.getVolume() != null ? paramsDto.getVolume() : aliTones.getVolume();
                speechRateValue = aliTones.getSpeechRateValue();
            } else {
                // 使用默认值或从DTO获取
                sampleRate = paramsDto.getSampleRate();
                voiceValue = memoryWordsAliTonesService.getVoiceValue(environment, voiceKey);
                volume = paramsDto.getVolume();
                speechRateValue = memoryWordsAliTonesService.getSpeechRateValue(speechRateKey);

                log.warn("未找到阿里云音色配置，使用默认值。环境：{}，发音人：{}，语速：{}", environment, voiceKey, speechRateKey);
            }

            // 获取unionId，如果方法不存在，则使用null
            String unionId = null; // JwtUtils可能没有getUnionidFromToken方法
            String text = paramsDto.getTextContent();
            Integer repeatCount = paramsDto.getRepeatCount();
            Float pauseSeconds = paramsDto.getPauseSeconds();

            log.info("用户[{}]请求阿里云语音合成，环境：{}，文本长度：{}", openId, environment, text.length());

            // 先创建记录，状态为处理中
            MemoryWordsTtsRecord ttsRecord = new MemoryWordsTtsRecord();
            ttsRecord.setOpenId(openId);
            ttsRecord.setUnionId(unionId);
            ttsRecord.setApiType("阿里云"); // 设置接口类型为阿里云
            ttsRecord.setEnvironment(environment);
            ttsRecord.setVoiceType(voiceKey); // 使用voiceKey作为音色类型-key
            ttsRecord.setVoiceValue(voiceValue); // 使用voiceValue作为音色类型-value
            ttsRecord.setTextContent(text);
            ttsRecord.setTextType("ssml"); // 默认都是ssml标签语言
            ttsRecord.setSpeedKey(speechRateKey); // 设置语速-key
            ttsRecord.setSpeedValue(speechRateValue != null ? speechRateValue.floatValue() : 0.0f); // 设置语速-value
            ttsRecord.setRepeatCount(repeatCount);
            ttsRecord.setPauseSeconds(pauseSeconds);
            ttsRecord.setStatus((byte) 0); // 0-处理中

            // 保存记录到数据库
            Long recordId = ttsRecordService.saveRecord(ttsRecord);

            // 处理文本列表
            List<String> textList = VolcanoEngineTTSUtil.splitTextByNewLine(text);

            // 生成自定义文件名：voiceKey-speechRateKey-textList第一个值的前4个字符
            String customFileName = generateCustomFileName(voiceKey, speechRateKey, textList);

            try {
                // 调用阿里云语音合成方法（获取带时长信息的结果）
                AliTTSUtils.TTSResult ttsResult = AliTTSUtils.synthesizeVoiceOfTextListToOssWithDuration(
                        sampleRate,
                        voiceValue,
                        volume,
                        speechRateValue,
                        textList,
                        repeatCount,
                        pauseSeconds,
                        customFileName);

                if (ttsResult == null || StringUtils.isBlank(ttsResult.getOssUrl())) {
                    throw new RuntimeException("阿里云语音合成失败，未返回音频URL");
                }

                // 更新记录状态为成功
                ttsRecord.setId(recordId);
                ttsRecord.setAudioUrl(ttsResult.getOssUrl());
                ttsRecord.setAudioDuration(ttsResult.getDurationSeconds()); // 设置音频时长
                ttsRecord.setStatus((byte) 1); // 1-成功

                // 更新记录
                ttsRecordService.updateRecord(ttsRecord);

                // 创建用户与录音关系记录
                MemoryWordsUserAndRecordRelation relation = new MemoryWordsUserAndRecordRelation();
                relation.setOpenId(openId);
                relation.setUnionId(unionId);
                relation.setRecordId(recordId);

                // 保存关系记录
                Long relationId = relationService.saveRelation(relation);
                if (relationId == null) {
                    log.warn("保存用户与录音关系记录失败，但语音合成成功。recordId={}, openId={}", recordId, openId);
                }

                return ApiResponse.success("阿里云语音合成成功", ttsResult.getOssUrl());
            } catch (Exception e) {
                // 更新记录状态为失败
                if (recordId != null) {
                    ttsRecord.setId(recordId);
                    ttsRecord.setStatus((byte) 2); // 2-失败
                    ttsRecord.setErrorMsg(StringUtils.abbreviate(e.getMessage(), 250)); // 限制错误消息长度
                    ttsRecordService.updateRecord(ttsRecord);
                }

                throw e; // 重新抛出异常，让外层捕获
            }
        } catch (Exception e) {
            log.error("阿里云语音合成失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "阿里云语音合成失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询阿里云语音合成记录
     * 通过用户与录音关系表查询用户可访问的录音记录，
     * 支持按文本内容进行模糊搜索。
     * 
     * @param searchText  文本内容搜索关键词(选填)
     * @param page        页码，从1开始
     * @param size        每页大小
     * @param httpRequest HTTP请求
     * @return 分页结果
     */
    @GetMapping("/pageRecords")
    @Operation(summary = "分页查询用户可访问的语音合成记录", description = "通过用户与录音关系表查询用户可访问的录音记录，支持按文本内容进行模糊搜索")
    public ApiResponse<PageResult<MwTtsRecordWithRelationVO>> pageRecords(
            @RequestParam(required = false) String searchText,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            log.info("用户[{}]请求分页查询阿里云语音合成记录，搜索关键词：{}", openId, searchText);

            // 通过关系表查询用户可访问的录音记录（包含关系ID）
            PageResult<MwTtsRecordWithRelationVO> pageResult = relationService
                    .getRecordsWithRelationIdByOpenIdAndContent(
                            openId, searchText, page, size);

            return ApiResponse.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("分页查询阿里云语音合成记录失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取单条阿里云语音合成记录
     * 通过用户与录音关系表验证权限后获取录音记录详情。
     * 
     * @param id          记录ID(必填)
     * @param httpRequest HTTP请求
     * @return 单条语音合成记录
     */
    @GetMapping("/getRecord")
    @Operation(summary = "获取单条用户可访问的语音合成记录", description = "通过用户与录音关系表验证权限后获取录音记录详情")
    public ApiResponse<MemoryWordsTtsRecord> getRecordById(
            @RequestParam Long id,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 验证参数
            if (id == null) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "记录ID不能为空");
            }

            log.info("用户[{}]请求获取阿里云语音合成记录，记录ID：{}", openId, id);

            // 通过关系表查询用户可访问的录音记录详情
            MemoryWordsTtsRecord record = relationService.getRecordByOpenIdAndRecordId(openId, id);

            if (record == null) {
                return ApiResponse.error(ApiResponseConstant.Code.NOT_FOUND, "记录不存在或无权限访问");
            }

            return ApiResponse.success("查询成功", record);
        } catch (Exception e) {
            log.error("获取阿里云语音合成记录失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除阿里云语音合成记录
     * 删除用户与录音的关系记录，实现真删除。
     * 注意：这只会删除用户与录音的关系，不会删除实际的录音文件。
     * 
     * @param ids         记录ID集合
     * @param httpRequest HTTP请求
     * @return 删除结果
     */
    @PostMapping("/deleteRecords")
    @Operation(summary = "删除用户与录音的关系记录", description = "删除用户与录音的关系记录，实现真删除")
    public ApiResponse<Object> deleteRecords(
            @RequestBody List<Long> ids,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 验证参数
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "记录ID集合不能为空");
            }

            log.info("用户[{}]请求删除阿里云语音合成记录，记录ID集合：{}", openId, ids);

            // 删除用户与录音的关系记录（真删除）
            int deletedCount = relationService.deleteRelationsByOpenIdAndRecordIds(openId, ids);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", deletedCount);

            return ApiResponse.success("删除成功", result);
        } catch (Exception e) {
            log.error("删除阿里云语音合成记录失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 生成自定义文件名
     * 
     * @param voiceKey      发音人Key
     * @param speechRateKey 语速Key
     * @param textList      文本列表
     * @return 自定义文件名（不包含扩展名）
     */
    private String generateCustomFileName(String voiceKey, String speechRateKey, List<String> textList) {
        StringBuilder fileName = new StringBuilder();

        // 添加voiceKey
        if (voiceKey != null && !voiceKey.trim().isEmpty()) {
            fileName.append(voiceKey.trim());
        } else {
            fileName.append("default");
        }

        fileName.append("-");

        // 添加speechRateKey
        if (speechRateKey != null && !speechRateKey.trim().isEmpty()) {
            fileName.append(speechRateKey.trim());
        } else {
            fileName.append("normal");
        }

        fileName.append("-");

        // 添加textList第一个值的前4个字符
        if (textList != null && !textList.isEmpty() && textList.get(0) != null) {
            String firstText = textList.get(0).trim();
            if (!firstText.isEmpty()) {
                // 获取前4个字符，如果不足4个字符则取全部
                String textPrefix = firstText.length() >= 4 ? firstText.substring(0, 4) : firstText;
                // 移除可能的特殊字符，只保留字母数字和中文
                textPrefix = textPrefix.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
                if (!textPrefix.isEmpty()) {
                    fileName.append(textPrefix);
                } else {
                    fileName.append("text");
                }
            } else {
                fileName.append("text");
            }
        } else {
            fileName.append("text");
        }

        // 添加时间戳确保文件名唯一性
        fileName.append("-").append(System.currentTimeMillis());

        return fileName.toString();
    }

    /**
     * 获取分享录音详情
     * 先在用户与录音关系表中绑定record_id和open_id的关系，
     * 然后通过record_id从录音记录表中获取录音详情返回
     * 
     * @param recordId    录音记录ID(必填)
     * @param httpRequest HTTP请求，用于校验token并获取用户openId
     * @return 录音详情
     */
    @GetMapping("/getSharedRecord")
    @Operation(summary = "获取分享录音详情", description = "绑定用户与录音关系并获取录音详情")
    public ApiResponse<MemoryWordsTtsRecord> getSharedRecord(
            @RequestParam Long recordId,
            HttpServletRequest httpRequest) {

        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 验证参数
            if (recordId == null) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "录音记录ID不能为空");
            }

            log.info("用户[{}]请求获取分享录音详情，录音ID：{}", openId, recordId);

            // 先在memory_words_user_and_record_relation表中绑定record_id和open_id的关系
            MemoryWordsUserAndRecordRelation relation = new MemoryWordsUserAndRecordRelation();
            relation.setOpenId(openId);
            relation.setUnionId(null);
            relation.setRecordId(recordId);

            // 保存关系记录（如果已存在则不会重复创建）
            Long relationId = relationService.saveRelation(relation);
            log.info("用户与录音关系绑定完成，关系记录ID：{}", relationId);

            // 通过record_id从memory_words_tts_record中获取录音详情
            MemoryWordsTtsRecord record = relationService.getRecordByOpenIdAndRecordId(openId, recordId);

            if (record == null) {
                return ApiResponse.error(ApiResponseConstant.Code.NOT_FOUND, "录音记录不存在");
            }

            return ApiResponse.success("获取录音详情成功", record);

        } catch (Exception e) {
            log.error("获取分享录音详情失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取录音详情失败: " + e.getMessage());
        }
    }

}
