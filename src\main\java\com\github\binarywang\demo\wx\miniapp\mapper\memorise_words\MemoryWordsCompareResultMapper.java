package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult;

/**
 * 记忆单词-比对结果Mapper接口
 */
@Mapper
public interface MemoryWordsCompareResultMapper {

        /**
         * 插入比对结果记录
         * 
         * @param record 比对结果记录实体
         * @return 影响的行数
         */
        @Insert("INSERT INTO memory_words_compare_result(open_id, union_id, relation_id, original_words, user_words, compare_result) "
                        +
                        "VALUES(#{openId}, #{unionId}, #{relationId}, #{originalWords}, #{userWords}, #{compareResult})")
        @Options(useGeneratedKeys = true, keyProperty = "id")
        int insert(MemoryWordsCompareResult record);

        /**
         * 根据relationId查询TTS记录的text_content
         * 
         * @param relationId memory_words_user_and_record_relation表的主键id
         * @return text_content
         */
        @Select("SELECT record.text_content " +
                        "FROM memory_words_user_and_record_relation relation " +
                        "LEFT JOIN memory_words_tts_record record ON relation.record_id = record.id " +
                        "WHERE relation.id = #{relationId}")
        String selectTextContentByRelationId(@Param("relationId") Long relationId);

        /**
         * 分页查询比对结果
         * 
         * @param openId     open_id
         * @param relationId relation_id，可为null
         * @param offset     偏移量
         * @param limit      每页数量
         * @return 比对结果列表
         */
        java.util.List<MemoryWordsCompareResult> selectCompareResultsByPage(@Param("openId") String openId,
                        @Param("relationId") Long relationId,
                        @Param("offset") int offset,
                        @Param("limit") int limit);

        /**
         * 查询比对结果总数
         * 
         * @param openId     open_id
         * @param relationId relation_id，可为null
         * @return 总数
         */
        long countCompareResults(@Param("openId") String openId,
                        @Param("relationId") Long relationId);

        /**
         * 根据id查询单条比对结果
         * 
         * @param id 比对结果主键id
         * @return 比对结果记录
         */
        @Select("SELECT * FROM memory_words_compare_result WHERE id = #{id}")
        MemoryWordsCompareResult selectById(@Param("id") Long id);
}