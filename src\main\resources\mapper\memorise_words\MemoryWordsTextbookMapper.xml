<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsTextbookMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTextbook" id="MemoryWordsTextbookResult">
        <id property="id" column="id"/>
        <result property="subject" column="subject"/>
        <result property="textbookVersion" column="textbook_version"/>
        <result property="grade" column="grade"/>
        <result property="term" column="term"/>
        <result property="unitInfo" column="unit_info"/>
        <result property="lessonTitle" column="lesson_title"/>
        <result property="words" column="words"/>
        <result property="lessonWords" column="lesson_words"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <resultMap id="MwTextbookTitleVOResult" type="com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTextbookTitleVO">
        <id property="id" column="id"/>
        <result property="unitSort" column="unit_sort"/>
        <result property="unitInfo" column="unit_info"/>
        <result property="lessonSort" column="lesson_sort"/>
        <result property="lessonTitle" column="lesson_title"/>
    </resultMap>

    <sql id="selectMemoryWordsTextbookVo">
        select id, subject, textbook_version, grade, term, unit_info, lesson_title, words, lesson_words, is_delete, create_time, update_time 
        from memory_words_textbook
    </sql>

    <select id="selectMemoryWordsTextbookList" resultMap="MemoryWordsTextbookResult">
        <include refid="selectMemoryWordsTextbookVo"/>
        where is_delete = '否'
    </select>

    <select id="selectMemoryWordsTextbookById" parameterType="Long" resultMap="MemoryWordsTextbookResult">
        <include refid="selectMemoryWordsTextbookVo"/>
        where id = #{id} and is_delete = '否'
    </select>

    <select id="selectMemoryWordsTextbookByCondition" resultMap="MemoryWordsTextbookResult">
        <include refid="selectMemoryWordsTextbookVo"/>
        <where>
            <if test="subject != null and subject != ''">
                AND subject = #{subject}
            </if>
            <if test="textbookVersion != null and textbookVersion != ''">
                AND textbook_version = #{textbookVersion}
            </if>
            <if test="grade != null">
                AND grade = #{grade}
            </if>
            <if test="term != null">
                AND term = #{term}
            </if>
            <if test="unitInfo != null and unitInfo != ''">
                AND unit_info = #{unitInfo}
            </if>
            <if test="lessonTitle != null and lessonTitle != ''">
                AND lesson_title = #{lessonTitle}
            </if>
            AND is_delete = '否'
        </where>
        LIMIT 1
    </select>
    
    <select id="selectLessonTitlesByCondition" resultMap="MwTextbookTitleVOResult">
        SELECT id, unit_sort, unit_info, lesson_sort, lesson_title
        FROM memory_words_textbook 
        WHERE subject = #{subject} 
        AND textbook_version = #{textbookVersion}
        AND grade = #{grade} 
        AND term = #{term} 
        AND is_delete = '否' 
        ORDER BY unit_sort ASC, lesson_sort ASC, id ASC
    </select>

    <insert id="insertMemoryWordsTextbook" parameterType="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTextbook" useGeneratedKeys="true" keyProperty="id">
        insert into memory_words_textbook
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subject != null">subject,</if>
            <if test="textbookVersion != null">textbook_version,</if>
            <if test="grade != null">grade,</if>
            <if test="term != null">term,</if>
            <if test="unitInfo != null">unit_info,</if>
            <if test="lessonTitle != null">lesson_title,</if>
            <if test="words != null">words,</if>
            <if test="lessonWords != null">lesson_words,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subject != null">#{subject},</if>
            <if test="textbookVersion != null">#{textbookVersion},</if>
            <if test="grade != null">#{grade},</if>
            <if test="term != null">#{term},</if>
            <if test="unitInfo != null">#{unitInfo},</if>
            <if test="lessonTitle != null">#{lessonTitle},</if>
            <if test="words != null">#{words},</if>
            <if test="lessonWords != null">#{lessonWords},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMemoryWordsTextbook" parameterType="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTextbook">
        update memory_words_textbook
        <trim prefix="SET" suffixOverrides=",">
            <if test="subject != null">subject = #{subject},</if>
            <if test="textbookVersion != null">textbook_version = #{textbookVersion},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="term != null">term = #{term},</if>
            <if test="unitInfo != null">unit_info = #{unitInfo},</if>
            <if test="lessonTitle != null">lesson_title = #{lessonTitle},</if>
            <if test="words != null">words = #{words},</if>
            <if test="lessonWords != null">lesson_words = #{lessonWords},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMemoryWordsTextbookById" parameterType="Long">
        update memory_words_textbook set is_delete = '是', update_time = now() where id = #{id}
    </update>

    <select id="selectMemoryWordsTextbookByIds" resultMap="MemoryWordsTextbookResult">
        SELECT *
        FROM memory_words_textbook
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_delete = '否'
    </select>

</mapper> 