<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsCompareResultMapper">

    <resultMap id="CompareResultMap" type="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="relationId" column="relation_id"/>
        <result property="originalWords" column="original_words"/>
        <result property="userWords" column="user_words"/>
        <result property="compareResult" column="compare_result"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectCompareResultsByPage" resultMap="CompareResultMap">
        SELECT * FROM memory_words_compare_result
        WHERE open_id = #{openId}
        <if test="relationId != null">
            AND relation_id = #{relationId}
        </if>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countCompareResults" resultType="long">
        SELECT COUNT(1) FROM memory_words_compare_result
        WHERE open_id = #{openId}
        <if test="relationId != null">
            AND relation_id = #{relationId}
        </if>
    </select>

</mapper> 