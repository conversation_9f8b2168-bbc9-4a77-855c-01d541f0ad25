package com.github.binarywang.demo.wx.miniapp.dto.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 记忆单词-比对结果请求参数
 */
@Data
@Schema(description = "新增比对结果请求参数")
public class MwCompareResultRequest {

    @Schema(description = "memory_words_user_and_record_relation表的主键id", required = true)
    @NotNull(message = "relationId不能为空")
    private Long relationId;

    @Schema(description = "OCR识别的用户默写内容", required = true)
    @NotNull(message = "用户默写内容不能为空")
    private String userWords;
}