package com.github.binarywang.demo.wx.miniapp.controller.stopfood;

import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.BmrRequest;
import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodBmrMapper;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.BmrVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 基础代谢控制器
 */
@RestController
@Slf4j
@RequestMapping("/wx/bmr/{appid}")
@Tag(name = "基础代谢接口")
public class BmrController {

    private final StopfoodBmrMapper bmrMapper;
    private final JwtUtils jwtUtils;

    @Autowired
    public BmrController(
            StopfoodBmrMapper bmrMapper,
            JwtUtils jwtUtils) {
        this.bmrMapper = bmrMapper;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 设置基础代谢
     */
    @Operation(summary = "设置基础代谢", description = "新增或更新用户的基础代谢，表中存在openId为更新，表中没有openId为新增", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "基础代谢设置请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = BmrRequest.class))))
    @PostMapping("/setBmr")
    public ApiResponse<BmrVO> setBmr(
            @PathVariable String appid,
            @Valid @RequestBody BmrRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户是否已有基础代谢
            StopfoodBmr existingBmr = bmrMapper.selectByOpenId(openId);

            if (existingBmr != null) {
                // 更新已有基础代谢
                existingBmr.setBmrValue(request.getBmrValue());
                existingBmr.setGenerationMode(request.getGenerationMode());
                bmrMapper.updateById(existingBmr);
                log.info("用户[{}]更新基础代谢: 基础代谢值[{}]千卡, 生成方式[{}]", openId, request.getBmrValue(),
                        request.getGenerationMode());
                return ApiResponse.success("更新基础代谢成功", BmrVO.fromEntity(existingBmr));
            } else {
                // 创建新基础代谢
                StopfoodBmr newBmr = new StopfoodBmr();
                newBmr.setOpenId(openId);
                newBmr.setBmrValue(request.getBmrValue());
                newBmr.setGenerationMode(request.getGenerationMode());
                bmrMapper.insert(newBmr);
                log.info("用户[{}]新增基础代谢: 基础代谢值[{}]千卡, 生成方式[{}]", openId, request.getBmrValue(),
                        request.getGenerationMode());
                return ApiResponse.success("设置基础代谢成功", BmrVO.fromEntity(newBmr));
            }
        } catch (Exception e) {
            log.error("设置基础代谢出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "设置基础代谢失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的基础代谢
     */
    @Operation(summary = "获取基础代谢", description = "根据用户的openId获取基础代谢", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    @PostMapping("/getBmr")
    public ApiResponse<BmrVO> getBmr(
            @PathVariable String appid,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户的基础代谢
            StopfoodBmr bmr = bmrMapper.selectByOpenId(openId);

            if (bmr != null) {
                return ApiResponse.success("获取基础代谢成功", BmrVO.fromEntity(bmr));
            } else {
                return ApiResponse.success("用户暂无基础代谢", null);
            }
        } catch (Exception e) {
            log.error("获取基础代谢出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取基础代谢失败: " + e.getMessage());
        }
    }
}