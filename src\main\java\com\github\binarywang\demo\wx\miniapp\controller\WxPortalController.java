package com.github.binarywang.demo.wx.miniapp.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaMessage;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import cn.binarywang.wx.miniapp.message.WxMaMessageRouter;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wx/portal/{appid}")
@Slf4j
@Tag(name = "微信小程序服务器认证接口")
public class WxPortalController {
    private final WxMaService wxMaService;
    private final WxMaMessageRouter wxMaMessageRouter;

    @GetMapping(produces = "text/plain;charset=utf-8")
    @Operation(summary = "服务器认证接口", description = "用于接收微信服务器的认证请求，配置服务器URL时使用")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "signature", description = "微信签名", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "timestamp", description = "时间戳", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "nonce", description = "随机数", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "echostr", description = "随机字符串", required = true, in = ParameterIn.QUERY)
    })
    public String authGet(@PathVariable String appid,
            @RequestParam(name = "signature", required = false) String signature,
            @RequestParam(name = "timestamp", required = false) String timestamp,
            @RequestParam(name = "nonce", required = false) String nonce,
            @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("\n接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (!wxMaService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        if (wxMaService.checkSignature(timestamp, nonce, signature)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return echostr;
        }
        WxMaConfigHolder.remove();// 清理ThreadLocal
        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=UTF-8")
    @Operation(summary = "消息接收接口", description = "用于接收微信服务器推送的消息")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "requestBody", description = "请求体内容", required = true, in = ParameterIn.DEFAULT),
            @Parameter(name = "msg_signature", description = "消息签名", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "encrypt_type", description = "加密类型", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "signature", description = "微信签名", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "timestamp", description = "时间戳", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "nonce", description = "随机数", required = true, in = ParameterIn.QUERY)
    })
    public String post(@PathVariable String appid,
            @RequestBody(required = false) String requestBody,
            @RequestParam(name = "msg_signature", required = false) String msgSignature,
            @RequestParam(name = "encrypt_type", required = false) String encryptType,
            @RequestParam(name = "signature", required = false) String signature,
            @RequestParam(name = "timestamp", required = false) String timestamp,
            @RequestParam(name = "nonce", required = false) String nonce) {
        log.info("\n接收微信请求：[msg_signature=[{}], encrypt_type=[{}], signature=[{}]," +
                " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                msgSignature, encryptType, signature, timestamp, nonce, requestBody);

        if (!wxMaService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        final boolean isJson = Objects.equals(wxMaService.getWxMaConfig().getMsgDataFormat(),
                WxMaConstants.MsgDataFormat.JSON);
        if (StringUtils.isBlank(encryptType)) {
            // 明文传输的消息
            WxMaMessage inMessage;
            if (isJson) {
                inMessage = WxMaMessage.fromJson(requestBody);
            } else {// xml
                inMessage = WxMaMessage.fromXml(requestBody);
            }

            this.route(inMessage);
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return "success";
        }

        if ("aes".equals(encryptType)) {
            // 是aes加密的消息
            WxMaMessage inMessage;
            if (isJson) {
                inMessage = WxMaMessage.fromEncryptedJson(requestBody, wxMaService.getWxMaConfig());
            } else {// xml
                inMessage = WxMaMessage.fromEncryptedXml(requestBody, wxMaService.getWxMaConfig(),
                        timestamp, nonce, msgSignature);
            }

            this.route(inMessage);
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return "success";
        }
        WxMaConfigHolder.remove();// 清理ThreadLocal
        throw new RuntimeException("不可识别的加密类型：" + encryptType);
    }

    private void route(WxMaMessage message) {
        try {
            wxMaMessageRouter.route(message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
