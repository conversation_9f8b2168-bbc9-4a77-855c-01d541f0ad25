package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 热量计算请求参数
 */
@Data
@Schema(description = "热量计算请求参数")
public class HeatCalculationRequest {

    /**
     * 类型：摄入热量 或 消耗热量
     */
    @NotBlank(message = "类型不能为空")
    @Schema(description = "类型，只能是'摄入热量'或'消耗热量'", required = true, example = "摄入热量")
    private String type;

    /**
     * 事件名称
     */
    @NotBlank(message = "事件名称不能为空")
    @Schema(description = "事件名称，如食物名称或运动名称", required = true, example = "苹果")
    private String eventName;

    /**
     * 单位
     */
    @NotBlank(message = "单位不能为空")
    @Schema(description = "单位，如个、克、分钟等", required = true, example = "个")
    private String unit;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Positive(message = "数量必须大于0")
    @Schema(description = "数量，必须大于0", required = true, example = "2")
    private Integer quantity;
}