# 微信小程序接口文档

## 目录

- [微信小程序接口文档](#微信小程序接口文档)
  - [目录](#目录)
  - [用户接口](#用户接口)
    - [登录接口](#登录接口)
    - [验证token](#验证token)
    - [获取用户手机号](#获取用户手机号)
    - [更新用户个人资料](#更新用户个人资料)
    - [获取用户详细信息](#获取用户详细信息)
    - [获取用户当天微信步数](#获取用户当天微信步数)
  - [热量分析接口](#热量分析接口)
    - [分析热量信息](#分析热量信息)
    - [精确计算热量及营养素](#精确计算热量及营养素)
    - [精确计算热量（已废弃）](#精确计算热量已废弃)
    - [获取今日热量记录及营养素](#获取今日热量记录及营养素)
    - [分页获取每天热量记录](#分页获取每天热量记录)
    - [删除热量记录](#删除热量记录)
    - [更新热量记录](#更新热量记录)
    - [新增/更新微信运动步数](#新增更新微信运动步数)
  - [热量记录接口](#热量记录接口)
    - [获取指定日期内用户摄入的营养素之合](#获取指定日期内用户摄入的营养素之合)
    - [分页获取每日营养素汇总](#分页获取每日营养素汇总)
  - [饮食模式接口](#饮食模式接口)
    - [设置饮食模式](#设置饮食模式)
    - [获取饮食模式](#获取饮食模式)
  - [基础代谢接口](#基础代谢接口)
    - [设置基础代谢](#设置基础代谢)
    - [获取基础代谢](#获取基础代谢)
  - [体重记录接口](#体重记录接口)
    - [新增或更新体重记录](#新增或更新体重记录)
    - [获取指定日期体重记录](#获取指定日期体重记录)
    - [分页获取体重和热量历史记录（已废弃）](#分页获取体重和热量历史记录已废弃)
    - [分页获取体重](#分页获取体重)
  - [记忆单词-阿里云语音合成](#记忆单词-阿里云语音合成)
    - [阿里云语音合成](#阿里云语音合成)
    - [分页查询用户可访问的语音合成记录](#分页查询用户可访问的语音合成记录)
    - [获取单条用户可访问的语音合成记录](#获取单条用户可访问的语音合成记录)
    - [删除用户与录音的关系记录](#删除用户与录音的关系记录)
    - [获取分享录音详情](#获取分享录音详情)
  - [记忆单词-课本单词](#记忆单词-课本单词)
    - [获取课本词语](#获取课本词语)
    - [获取课文标题列表](#获取课文标题列表)
  - [记忆单词-手写识别](#记忆单词-手写识别)
    - [识别用户手写单词](#识别用户手写单词)
    - [新增比对结果](#新增比对结果)
    - [分页查询录音历史批改结果](#分页查询录音历史批改结果)
    - [根据id查询单条比对结果](#根据id查询单条比对结果)
  - [通用接口](#通用接口)
    - [提交用户建议](#提交用户建议)
    - [语音转文字](#语音转文字)
  - [微信小程序服务器认证接口](#微信小程序服务器认证接口)
    - [服务器认证接口](#服务器认证接口)
    - [消息接收接口](#消息接收接口)
  - [小程序媒体接口](#小程序媒体接口)
    - [上传媒体文件](#上传媒体文件)
    - [获取媒体文件](#获取媒体文件)

## 用户接口

### 登录接口

**接口描述**：通过微信小程序code获取用户的openid和session_key，生成token

**请求URL**：/wx/user/{appid}/login

**请求方式**：GET

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |
| code   | String | 是       | 微信登录code    |

**请求参数示例**：
```
/wx/user/wx36d336375b1c07b1/login?code=123456
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，登录结果对象 |

**返回示例**：
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "sessionKey": "abcdefghijklmnopqrstuvwxyz",
    "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX",
    "unionId": "unionidXXXXXXXXXXXXXXXX",
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJvUnJkUXQwZ09TalhaQXBSQ2NqV05CQWc1MGxjIiwib3BlbmlkIjoib1JyZFF0MGdPU2pYWkFwUkNjaldOQkFnNTBsYyIsInNlc3Npb25LZXkiOiJNSXhLWGQ3VDBEdXNUK25ERnF4R1p3PT0iLCJleHAiOjE2ODYyMzQ5NTV9.0tRkZ_fhBZwCca9B6bH0xHQp1r2kWZbGI1Ry5kZlp8c"
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 500    | 服务器内部错误 |

### 验证token

**接口描述**：验证token是否有效且未过期

**请求URL**：/wx/user/{appid}/verifyToken

**请求方式**：GET

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Boolean | 返回数据，true表示有效 |

**返回示例**：
```json
{
  "code": 0,
  "message": "token验证成功",
  "data": true
}
```

**错误码说明**：

| 错误码 | 说明                 |
| ------ | -------------------- |
| 400    | 参数错误             |
| 401    | 未提供认证令牌       |
| 401    | 无效的认证令牌       |
| 401    | 认证令牌已过期或无效 |
| 401    | 认证令牌不匹配       |
| 401    | 认证令牌已过期       |
| 500    | 服务器内部错误       |



### 获取用户手机号

**接口描述**：获取微信小程序用户绑定的手机号信息

**请求URL**：/wx/user/{appid}/phone

**请求方式**：GET

**请求参数**：

| 参数名        | 类型   | 是否必填 | 说明               |
| ------------- | ------ | -------- | ------------------ |
| appid         | String | 是       | 微信小程序appid    |
| sessionKey    | String | 是       | 会话密钥           |
| signature     | String | 是       | 签名               |
| rawData       | String | 是       | 原始数据           |
| encryptedData | String | 是       | 加密数据           |
| iv            | String | 是       | 加密算法的初始向量 |

**请求参数示例**：
```
/wx/user/wx36d336375b1c07b1/phone?sessionKey=xxx&signature=xxx&rawData=xxx&encryptedData=xxx&iv=xxx
```

**返回参数**：

| 参数名  | 类型    | 说明                 |
| ------- | ------- | -------------------- |
| code    | Integer | 状态码，0表示成功    |
| message | String  | 提示信息             |
| data    | String  | 返回数据，用户手机号 |

**返回示例**：
```json
{
  "code": 0,
  "message": "获取用户手机号成功",
  "data": "13800138000"
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

### 更新用户个人资料

**接口描述**：更新微信用户的性别、生日、身高、体重等信息

**请求URL**：/wx/user/{appid}/update

**请求方式**：POST

**请求参数**：

| 参数名    | 类型       | 是否必填 | 说明                       |
| --------- | ---------- | -------- | -------------------------- |
| appid     | String     | 是       | 微信小程序appid            |
| gender    | Integer    | 否       | 性别（0-男，1-女，2-未知） |
| birthDate | Date       | 否       | 用户生日                   |
| height    | BigDecimal | 否       | 身高(cm)                   |
| weight    | BigDecimal | 否       | 体重(kg)                   |
| nickName  | String     | 否       | 用户昵称                   |
| avatarUrl | String     | 否       | 头像URL                    |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```json
{
  "gender": 0,
  "birthDate": "1990-01-01",
  "height": 175.5,
  "weight": 65.5,
  "nickName": "张三",
  "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132"
}
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，用户信息对象 |

**返回示例**：
```json
{
  "code": 0,
  "message": "更新用户个人资料成功",
  "data": {
    "id": 1,
    "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX",
    "unionId": "unionidXXXXXXXXXXXXXXXX",
    "phone": "13800138000",
    "nickName": "张三",
    "gender": 0,
    "birthDate": "1990-01-01",
    "height": 175.5,
    "weight": 65.5,
    "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132",
    "loginIp": "127.0.0.1",
    "loginDate": "2024-08-26 15:30:00",
    "createTime": "2023-10-16 10:00:00",
    "updateTime": "2024-08-26 15:30:00",
    "remark": null
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 404    | 用户不存在     |
| 500    | 服务器内部错误 |

### 获取用户详细信息

**接口描述**：根据openId获取用户的头像、昵称、手机号、性别、生日、身高、体重等详细信息

**请求URL**：/wx/user/{appid}/getUserInfo

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |
| openId | String | 是       | 微信用户openId  |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```json
{
  "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX"
}
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，用户信息对象 |

**data对象字段说明**：

| 字段名     | 类型       | 说明                                    |
| ---------- | ---------- | --------------------------------------- |
| id         | Long       | 用户ID                                  |
| openId     | String     | 微信openId                              |
| unionId    | String     | 微信unionId                             |
| phone      | String     | 手机号码                                |
| nickName   | String     | 用户昵称                                |
| gender     | Integer    | 性别：0-男，1-女，2-未知                |
| birthDate  | String     | 出生日期，格式：yyyy-MM-dd              |
| height     | BigDecimal | 身高(cm)                                |
| weight     | BigDecimal | 体重(kg)                                |
| avatarUrl  | String     | 头像URL                                 |
| loginIp    | String     | 最近登录IP                              |
| loginDate  | String     | 最近登录时间，格式：yyyy-MM-dd HH:mm:ss |
| createTime | String     | 创建时间，格式：yyyy-MM-dd HH:mm:ss     |
| updateTime | String     | 更新时间，格式：yyyy-MM-dd HH:mm:ss     |
| remark     | String     | 备注信息，可能为null                    |

**返回示例**：
```json
{
  "code": 0,
  "message": "获取用户详细信息成功",
  "data": {
    "id": 1,
    "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX",
    "unionId": "unionidXXXXXXXXXXXXXXXX",
    "phone": "13800138000",
    "nickName": "张三",
    "gender": 0,
    "birthDate": "1990-01-01",
    "height": 175.5,
    "weight": 65.5,
    "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132",
    "loginIp": "127.0.0.1",
    "loginDate": "2024-08-26 15:30:00",
    "createTime": "2023-10-16 10:00:00",
    "updateTime": "2024-08-26 15:30:00",
    "remark": null
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 404    | 用户不存在     |
| 500    | 服务器内部错误 |

### 获取用户当天微信步数

**接口描述**：获取用户当天的微信运动步数，需要用户授权scope.werun权限

**请求URL**：/wx/user/{appid}/weRunData

**请求方式**：GET

**请求参数**：

| 参数名        | 类型   | 是否必填 | 说明                                                              |
| ------------- | ------ | -------- | ----------------------------------------------------------------- |
| appid         | String | 是       | 微信小程序appid                                                   |
| sessionKey    | String | 是       | 会话密钥，通过wx.login和登录接口获取                              |
| encryptedData | String | 是       | 包括敏感数据在内的完整加密数据，从wx.getWeRunData的回调结果中获取 |
| iv            | String | 是       | 加密算法的初始向量，从wx.getWeRunData的回调结果中获取             |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```
/wx/user/wx36d336375b1c07b1/weRunData?sessionKey=Ab1C2d3Ef4g5H6i7J8k9L0m1N2o3P4q5&encryptedData=ey45GHhgT...&iv=r7BXXKkLb8qrv==
```

**返回参数**：

| 参数名  | 类型       | 说明                           |
| ------- | ---------- | ------------------------------ |
| code    | Integer    | 状态码，0表示成功              |
| message | String     | 提示信息                       |
| data    | JSONObject | 返回数据，已解密的微信步数数据 |

**data对象字段说明**：

| 字段名       | 类型       | 说明                         |
| ------------ | ---------- | ---------------------------- |
| stepInfoList | JSONArray  | 步数信息列表，包含多天的步数 |
| watermark    | JSONObject | 数据水印，包含时间戳和appid  |

**stepInfoList数组中对象字段说明**：

| 字段名    | 类型    | 说明                       |
| --------- | ------- | -------------------------- |
| timestamp | Long    | 时间戳，表示对应步数的日期 |
| step      | Integer | 当天步数值                 |

**返回示例**：
```json
{
  "code": 0,
  "message": "获取微信步数数据成功",
  "data": {
    "stepInfoList": [
      {
        "timestamp": 1445866601,
        "step": 6669
      },
      {
        "timestamp": 1445953001,
        "step": 8546
      },
      {
        "timestamp": 1446039401,
        "step": 6666
      }
    ],
    "watermark": {
      "timestamp": 1637824591,
      "appid": "wx36d336375b1c07b1"
    }
  }
}
```

**客户端使用方法**：

1. 小程序端需要先调用 `wx.login` 获取code
2. 使用code调用后端登录接口获取sessionKey
3. 调用 `wx.getWeRunData()` 获取加密的微信运动数据
4. 将sessionKey、encryptedData和iv发送到此接口
5. 接口会直接返回解密后的步数数据，前端可直接使用

**错误码说明**：

| 错误码 | 说明               |
| ------ | ------------------ |
| 400    | 参数错误           |
| 401    | 未授权或会话已过期 |
| 500    | 服务器内部错误     |

## 热量分析接口

### 分析热量信息

**接口描述**：解析文本中包含的热量摄入和消耗信息，并保存到数据库。支持解析营养素信息（碳水化合物、蛋白质、脂肪）和体重信息，并自动保存体重记录。

**请求URL**：/wx/heat/{appid}/analyze

**请求方式**：POST

**注意事项**：
- 使用V2版本提示词进行热量分析，提高解析准确性
- 支持解析营养素信息：碳水化合物、蛋白质、脂肪
- 支持解析体重信息并自动保存每日体重记录
- 优化了JSON解析逻辑，支持解析带有Markdown代码块标记的返回结果
- 修复了中文引号导致的解析错误问题
- 添加了更详细的错误日志记录

**请求参数**：

| 参数名  | 类型   | 是否必填 | 说明               |
| ------- | ------ | -------- | ------------------ |
| appid   | String | 是       | 微信小程序appid    |
| content | String | 是       | 需要分析的文本内容 |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```json
{
  "content": "今天早上跑步半小时，消耗了300千卡热量，中午吃了一份炒饭和一个鸡腿，摄入了800千卡热量，今天体重65.5公斤"
}
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Array   | 返回数据，热量分析结果列表 |

**data数组中单个对象的字段说明**：

| 字段名        | 类型     | 说明                                   | 示例值                |
| ------------- | -------- | -------------------------------------- | --------------------- |
| eventName     | String   | 事件名称，描述摄入或消耗热量的具体活动 | "跑步"                |
| type          | String   | 类型，取值为"摄入热量"或"消耗热量"     | "消耗热量"            |
| unit          | String   | 单位，如克、分钟、小时等               | "分钟"                |
| quantity      | Integer  | 单位对应的数量，如30(分钟)、200(克)等  | 30                    |
| heatValue     | Integer  | 热量值，单位：千卡(kcal)               | 300                   |
| carbohydrate  | Integer  | 碳水化合物含量，单位：克（仅摄入热量） | 45                    |
| protein       | Integer  | 蛋白质含量，单位：克（仅摄入热量）     | 18                    |
| fat           | Integer  | 脂肪含量，单位：克（仅摄入热量）       | 12                    |
| weight        | Double   | 体重信息，单位：千克(kg)               | 65.5                  |
| createTime    | DateTime | 创建时间，格式：yyyy-MM-dd HH:mm:ss    | "2024-08-27 08:30:00" |
| updateTime    | DateTime | 更新时间，格式：yyyy-MM-dd HH:mm:ss    | "2024-08-27 08:30:00" |

**返回示例**：
```json
{
  "code": 0,
  "message": "热量分析成功",
  "data": [
    {
      "eventName": "跑步",
      "type": "消耗热量",
      "unit": "分钟",
      "quantity": 30,
      "heatValue": 300,
      "carbohydrate": null,
      "protein": null,
      "fat": null,
      "weight": 65.5,
      "createTime": "2024-08-27 15:30:00",
      "updateTime": "2024-08-27 15:30:00"
    },
    {
      "eventName": "炒饭",
      "type": "摄入热量",
      "unit": "克",
      "quantity": 200,
      "heatValue": 500,
      "carbohydrate": 75,
      "protein": 12,
      "fat": 8,
      "weight": null,
      "createTime": "2024-08-27 15:30:00",
      "updateTime": "2024-08-27 15:30:00"
    },
    {
      "eventName": "鸡腿",
      "type": "摄入热量",
      "unit": "个",
      "quantity": 1,
      "heatValue": 300,
      "carbohydrate": 0,
      "protein": 25,
      "fat": 20,
      "weight": null,
      "createTime": "2024-08-27 15:30:00",
      "updateTime": "2024-08-27 15:30:00"
    }
  ]
}
```

**接口特性说明**：

1. **智能文本解析**：使用V2版本提示词，能够准确识别文本中的热量摄入和消耗信息
2. **营养素分析**：对于摄入热量类型的事件，会分析并返回碳水化合物、蛋白质、脂肪含量
3. **体重记录管理**：如果文本中包含体重信息，会自动保存或更新当天的体重记录
4. **批量数据保存**：所有解析出的热量事件会批量保存到数据库中
5. **数据完整性**：返回的数据包含详细的单位、数量信息，便于后续编辑和管理

**错误码说明**：

| 错误码 | 说明           | 可能原因                   |
| ------ | -------------- | -------------------------- |
| 400    | 参数错误       | 请求内容为空               |
| 401    | 未授权         | 令牌无效、已过期或权限不足 |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常   |

### 精确计算热量及营养素

**接口描述**：根据事件类型、名称、单位和数量精确计算热量值及营养素信息，不保存到数据库，仅返回计算结果。支持食物摄入热量的营养素分析（碳水化合物、蛋白质、脂肪）。

**请求URL**：/wx/heat/{appid}/accurateCalculateHeatAndNutrient

**请求方式**：POST

**请求参数**：

| 参数名    | 类型    | 是否必填 | 说明                               | 示例值     |
| --------- | ------- | -------- | ---------------------------------- | ---------- |
| appid     | String  | 是       | 微信小程序appid，路径参数          | wx1234abcd |
| type      | String  | 是       | 类型，只能是"摄入热量"或"消耗热量" | "摄入热量" |
| eventName | String  | 是       | 事件名称，如食物名称或运动名称     | "苹果"     |
| unit      | String  | 是       | 单位，如个、克、分钟、小时等       | "个"       |
| quantity  | Integer | 是       | 数量，必须大于0                    | 2          |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数示例**：

**食物摄入热量计算**：
```json
{
  "type": "摄入热量",
  "eventName": "苹果",
  "unit": "个",
  "quantity": 2
}
```

**运动消耗热量计算**：
```json
{
  "type": "消耗热量",
  "eventName": "跑步",
  "unit": "分钟",
  "quantity": 30
}
```

**食物重量计算**：
```json
{
  "type": "摄入热量",
  "eventName": "米饭",
  "unit": "克",
  "quantity": 150
}
```

**返回参数**：

| 参数名  | 类型    | 说明                                     | 示例值         |
| ------- | ------- | ---------------------------------------- | -------------- |
| code    | Integer | 状态码，0表示成功                        | 0              |
| message | String  | 提示信息                                 | "热量计算成功" |
| data    | Object  | 返回数据，包含热量值和营养素的详细信息   | {...}          |

**data对象字段说明**：

| 字段名        | 类型     | 说明                                                   | 示例值                |
| ------------- | -------- | ------------------------------------------------------ | --------------------- |
| id            | Long     | 记录ID（计算接口中通常为null）                         | null                  |
| eventName     | String   | 事件名称，与请求参数一致                               | "苹果"                |
| type          | String   | 类型，与请求参数一致                                   | "摄入热量"            |
| unit          | String   | 单位，与请求参数一致                                   | "个"                  |
| quantity      | Integer  | 数量，与请求参数一致                                   | 2                     |
| heatValue     | Integer  | 计算得出的热量值，单位：千卡(kcal)                     | 104                   |
| carbohydrate  | Integer  | 碳水化合物含量，单位：克（仅摄入热量类型有值）         | 25                    |
| protein       | Integer  | 蛋白质含量，单位：克（仅摄入热量类型有值）             | 1                     |
| fat           | Integer  | 脂肪含量，单位：克（仅摄入热量类型有值）               | 0                     |
| weight        | Double   | 体重信息，单位：千克（计算接口中通常为null）           | null                  |
| createTime    | DateTime | 创建时间（计算接口中通常为null）                       | null                  |
| updateTime    | DateTime | 更新时间（计算接口中通常为null）                       | null                  |

**返回示例**：

**成功计算食物热量及营养素**：
```json
{
  "code": 0,
  "message": "热量计算成功",
  "data": {
    "id": null,
    "eventName": "苹果",
    "type": "摄入热量",
    "unit": "个",
    "quantity": 2,
    "heatValue": 104,
    "carbohydrate": 25,
    "protein": 1,
    "fat": 0,
    "weight": null,
    "createTime": null,
    "updateTime": null
  }
}
```

**成功计算运动热量**：
```json
{
  "code": 0,
  "message": "热量计算成功",
  "data": {
    "id": null,
    "eventName": "跑步",
    "type": "消耗热量",
    "unit": "分钟",
    "quantity": 30,
    "heatValue": 300,
    "carbohydrate": null,
    "protein": null,
    "fat": null,
    "weight": null,
    "createTime": null,
    "updateTime": null
  }
}
```

**计算失败**：
```json
{
  "code": 500,
  "message": "热量计算失败: ",
  "data": null
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "类型不能为空",
  "data": null
}
```

**类型参数错误**：
```json
{
  "code": 400,
  "message": "类型只能是'摄入热量'或'消耗热量'",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "热量计算失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                  |
| ------ | -------------- | ----------------------------------------- |
| 400    | 参数错误       | 必填参数为空、类型参数错误、数量不大于0等 |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败  |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常  |

**接口特性说明**：

1. **智能营养素分析**：对于"摄入热量"类型的食物，会自动计算并返回营养素信息
2. **运动热量计算**：对于"消耗热量"类型的运动，营养素字段为null
3. **精确计算**：基于内置的食物和运动热量数据库进行精确计算
4. **不保存数据**：此接口仅用于计算预览，不会保存到数据库
5. **实时计算**：每次调用都会重新计算，确保结果准确性

**常见使用场景**：
- 用户输入食物信息前预览热量和营养素
- 运动前估算消耗热量
- 制定饮食计划时的热量和营养素计算
- 热量记录的实时计算和验证

### 精确计算热量（已废弃）

**接口描述**：根据事件类型、名称、单位和数量精确计算热量值，不保存到数据库，仅返回计算结果

**请求URL**：/wx/heat/{appid}/calculateHeat

**请求方式**：POST

**接口状态**：已废弃（@Deprecated）

**请求参数**：

| 参数名    | 类型    | 是否必填 | 说明                               | 示例值     |
| --------- | ------- | -------- | ---------------------------------- | ---------- |
| appid     | String  | 是       | 微信小程序appid，路径参数          | wx1234abcd |
| type      | String  | 是       | 类型，只能是"摄入热量"或"消耗热量" | "摄入热量" |
| eventName | String  | 是       | 事件名称，如食物名称或运动名称     | "苹果"     |
| unit      | String  | 是       | 单位，如个、克、分钟、小时等       | "个"       |
| quantity  | Integer | 是       | 数量，必须大于0                    | 2          |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数示例**：

**食物摄入热量计算**：
```json
{
  "type": "摄入热量",
  "eventName": "苹果",
  "unit": "个",
  "quantity": 2
}
```

**运动消耗热量计算**：
```json
{
  "type": "消耗热量",
  "eventName": "跑步",
  "unit": "分钟",
  "quantity": 30
}
```

**食物重量计算**：
```json
{
  "type": "摄入热量",
  "eventName": "米饭",
  "unit": "克",
  "quantity": 150
}
```

**返回参数**：

| 参数名  | 类型    | 说明                             | 示例值         |
| ------- | ------- | -------------------------------- | -------------- |
| code    | Integer | 状态码，0表示成功                | 0              |
| message | String  | 提示信息                         | "热量计算成功" |
| data    | Object  | 返回数据，包含计算结果的详细信息 | {...}          |

**data对象字段说明**：

| 字段名    | 类型    | 说明                               | 示例值     |
| --------- | ------- | ---------------------------------- | ---------- |
| type      | String  | 类型，与请求参数一致               | "摄入热量" |
| eventName | String  | 事件名称，与请求参数一致           | "苹果"     |
| unit      | String  | 单位，与请求参数一致               | "个"       |
| quantity  | Integer | 数量，与请求参数一致               | 2          |
| heatValue | Integer | 计算得出的热量值，单位：千卡(kcal) | 104        |

**返回示例**：

**成功计算食物热量**：
```json
{
  "code": 0,
  "message": "热量计算成功",
  "data": {
    "type": "摄入热量",
    "eventName": "苹果",
    "unit": "个",
    "quantity": 2,
    "heatValue": 104
  }
}
```

**成功计算运动热量**：
```json
{
  "code": 0,
  "message": "热量计算成功",
  "data": {
    "type": "消耗热量",
    "eventName": "跑步",
    "unit": "分钟",
    "quantity": 30,
    "heatValue": 300
  }
}
```

**未找到匹配数据**：
```json
{
  "code": 0,
  "message": "未找到匹配的热量数据，返回默认值0",
  "data": {
    "type": "摄入热量",
    "eventName": "未知食物",
    "unit": "个",
    "quantity": 1,
    "heatValue": 0
  }
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "类型不能为空",
  "data": null
}
```

**类型参数错误**：
```json
{
  "code": 400,
  "message": "类型只能是'摄入热量'或'消耗热量'",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "热量计算失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                  |
| ------ | -------------- | ----------------------------------------- |
| 400    | 参数错误       | 必填参数为空、类型参数错误、数量不大于0等 |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败  |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常  |

**注意事项**：
1. 此接口已废弃，建议使用新的热量分析接口
2. 此接口仅计算热量值，不会保存到数据库中
3. 系统会根据内置的热量数据库进行精确计算
4. 如果找不到匹配的热量数据，会返回默认值0
5. 支持多种单位：个、克、分钟、小时等
6. 常见食物和运动都有对应的热量数据
7. 计算结果可用于预览，确认后可通过其他接口保存

**常见使用场景**：
- 用户输入食物信息前预览热量
- 运动前估算消耗热量
- 制定饮食计划时的热量计算
- 热量记录的实时计算和验证

### 获取今日热量记录及营养素

**接口描述**：获取用户今日的所有热量摄入和消耗记录，包括具体的数量单位信息和营养素信息（碳水化合物、蛋白质、脂肪）。支持区分摄入热量和消耗热量类型记录的营养素展示。

**请求URL**：/wx/heat/{appid}/today

**请求方式**：GET

**功能特性**：
- 获取当日所有热量记录，按创建时间倒序排列
- 包含完整的营养素信息（碳水化合物、蛋白质、脂肪）
- 自动区分摄入热量和消耗热量类型记录
- 支持null值处理，确保数据完整性

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**返回参数**：

| 参数名  | 类型    | 说明                                         | 示例值       |
| ------- | ------- | -------------------------------------------- | ------------ |
| code    | Integer | 状态码，0表示成功                            | 0            |
| message | String  | 提示信息                                     | "查询成功"   |
| data    | Array   | 返回数据，热量记录及营养素列表，详见下方说明 | [{...}, ...] |

**data数组中单个对象的字段说明**：

| 字段名        | 类型     | 说明                                                     | 示例值                |
| ------------- | -------- | -------------------------------------------------------- | --------------------- |
| id            | Long     | 记录ID，唯一标识                                         | 15                    |
| eventName     | String   | 事件名称，描述摄入或消耗热量的具体活动                   | "跑步"                |
| type          | String   | 类型，取值为"摄入热量"或"消耗热量"                       | "消耗热量"            |
| unit          | String   | 单位，如克、个、分钟、小时等                             | "分钟"                |
| quantity      | Integer  | 单位对应的数量，如30(分钟)、200(克)等                    | 30                    |
| heatValue     | Integer  | 热量值，单位：千卡(kcal)                                 | 300                   |
| carbohydrate  | Integer  | 碳水化合物含量，单位：克（摄入热量类型有值，消耗热量为null） | 45                    |
| protein       | Integer  | 蛋白质含量，单位：克（摄入热量类型有值，消耗热量为null）     | 18                    |
| fat           | Integer  | 脂肪含量，单位：克（摄入热量类型有值，消耗热量为null）       | 12                    |
| createTime    | DateTime | 创建时间，格式：yyyy-MM-dd HH:mm:ss                      | "2024-08-27 08:30:00" |
| updateTime    | DateTime | 更新时间，格式：yyyy-MM-dd HH:mm:ss                      | "2024-08-27 08:30:00" |

**营养素字段说明**：
- **摄入热量类型记录**：carbohydrate、protein、fat字段包含具体数值（单位：克）
- **消耗热量类型记录**：carbohydrate、protein、fat字段为null
- **数据类型**：所有营养素字段均为Integer类型，允许null值
- **数据来源**：营养素数据基于食物成分数据库计算得出

**返回示例**：

**成功获取今日热量记录及营养素**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": [
    {
      "id": 15,
      "eventName": "跑步",
      "type": "消耗热量",
      "unit": "分钟",
      "quantity": 30,
      "heatValue": 300,
      "carbohydrate": null,
      "protein": null,
      "fat": null,
      "createTime": "2024-12-27 08:30:00",
      "updateTime": "2024-12-27 08:30:00"
    },
    {
      "id": 16,
      "eventName": "苹果",
      "type": "摄入热量",
      "unit": "个",
      "quantity": 2,
      "heatValue": 104,
      "carbohydrate": 25,
      "protein": 1,
      "fat": 0,
      "createTime": "2024-12-27 09:15:00",
      "updateTime": "2024-12-27 09:15:00"
    },
    {
      "id": 17,
      "eventName": "炒饭",
      "type": "摄入热量",
      "unit": "克",
      "quantity": 200,
      "heatValue": 500,
      "carbohydrate": 75,
      "protein": 12,
      "fat": 8,
      "createTime": "2024-12-27 12:00:00",
      "updateTime": "2024-12-27 12:00:00"
    },
    {
      "id": 18,
      "eventName": "鸡腿",
      "type": "摄入热量",
      "unit": "个",
      "quantity": 1,
      "heatValue": 250,
      "carbohydrate": 0,
      "protein": 25,
      "fat": 15,
      "createTime": "2024-12-27 12:30:00",
      "updateTime": "2024-12-27 12:30:00"
    },
    {
      "id": 19,
      "eventName": "游泳",
      "type": "消耗热量",
      "unit": "小时",
      "quantity": 1,
      "heatValue": 400,
      "carbohydrate": null,
      "protein": null,
      "fat": null,
      "createTime": "2024-12-27 18:30:00",
      "updateTime": "2024-12-27 18:30:00"
    },
    {
      "id": 20,
      "eventName": "自动-微信运动步数",
      "type": "消耗热量",
      "unit": "步",
      "quantity": 8000,
      "heatValue": 240,
      "carbohydrate": null,
      "protein": null,
      "fat": null,
      "createTime": "2024-12-27 20:00:00",
      "updateTime": "2024-12-27 20:00:00"
    }
  ]
}
```

**今日无热量记录**：
```json
{
  "code": 0,
  "message": "今日还没有热量记录",
  "data": []
}
```

**错误返回示例**：

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "查询失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                    |
| ------ | -------------- | ------------------------------------------- |
| 400    | 参数错误       | appid不合法或格式错误                       |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败    |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常    |

**接口特性说明**：

1. **数据完整性**：返回的记录包含完整的营养素信息，确保前端能够准确展示
2. **类型区分**：
   - **摄入热量记录**：包含具体的营养素数值（carbohydrate、protein、fat）
   - **消耗热量记录**：营养素字段为null，符合业务逻辑
3. **时间排序**：记录按创建时间倒序排列，最新记录在前
4. **null值处理**：营养素字段支持null值，前端需要进行相应的null值判断
5. **数据类型**：所有营养素字段均为Integer类型，单位为克

**注意事项**：

1. **营养素字段使用**：
   - 仅"摄入热量"类型的记录包含营养素信息
   - "消耗热量"类型的记录营养素字段为null
2. **数据展示建议**：
   - 前端可根据type字段判断是否显示营养素信息
   - 建议对null值进行友好的UI处理
3. **性能考虑**：接口返回当日所有记录，数据量通常较小，性能良好
4. **时区处理**：查询基于服务器时区的当日时间范围
5. **权限验证**：只能查询当前用户的热量记录，确保数据安全

**常见使用场景**：
- 用户查看今日饮食和运动记录
- 计算今日总热量摄入和消耗
- 分析今日营养素摄入情况
- 制定明日饮食和运动计划
- 热量记录的统计和分析



### 分页获取每天热量记录

**接口描述**：分页获取用户每天的热量摄入和消耗记录汇总，按日期倒序返回

**请求URL**：/wx/heat/{appid}/dailyRecordsByPage

**请求方式**：GET

**请求参数**：

| 参数名   | 类型    | 是否必填 | 说明                                  |
| -------- | ------- | -------- | ------------------------------------- |
| appid    | String  | 是       | 微信小程序appid，路径参数             |
| pageNum  | Integer | 否       | 页码，从1开始（1表示第一页），默认为1 |
| pageSize | Integer | 否       | 每页大小，默认为10                    |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，分页结果信息 |

**返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "formattedDate": "2024-08-27",
        "totalIntake": 1500.0,
        "totalConsumption": 500.0,
        "netHeat": 1000.0,
        "details": [
          {
            "eventName": "早餐",
            "type": "摄入热量",
            "heatValue": 500.0,
            "recordTime": "2024-08-27 08:00:00"
          },
          {
            "eventName": "午餐",
            "type": "摄入热量",
            "heatValue": 800.0,
            "recordTime": "2024-08-27 12:30:00"
          },
          {
            "eventName": "跑步",
            "type": "消耗热量",
            "heatValue": 300.0,
            "recordTime": "2024-08-27 18:00:00"
          },
          {
            "eventName": "晚餐",
            "type": "摄入热量",
            "heatValue": 200.0,
            "recordTime": "2024-08-27 19:00:00"
          }
        ]
      },
      {
        "formattedDate": "2024-08-26",
        "totalIntake": 1600.0,
        "totalConsumption": 400.0,
        "netHeat": 1200.0,
        "details": [
          {
            "eventName": "早餐",
            "type": "摄入热量",
            "heatValue": 600.0,
            "recordTime": "2024-08-26 08:30:00"
          },
          {
            "eventName": "午餐",
            "type": "摄入热量",
            "heatValue": 700.0,
            "recordTime": "2024-08-26 12:00:00"
          },
          {
            "eventName": "健身",
            "type": "消耗热量",
            "heatValue": 400.0,
            "recordTime": "2024-08-26 17:30:00"
          }
        ]
      }
    ],
    "total": 25,
    "pages": 3,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**无记录时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**无记录时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

### 删除热量记录

**接口描述**：根据记录ID集合删除用户的热量记录

**请求URL**：/wx/heat/{appid}/deleteRecords

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |
| ids    | Array  | 是       | 记录ID集合      |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```json
{
  "ids": [15, 16, 17]
}
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，删除结果信息 |

**返回示例**：
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "deletedCount": 3
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |



### 更新热量记录

**接口描述**：根据记录ID更新热量记录的事件名称、单位、数量、热量值和营养素信息

**请求URL**：/wx/heat/{appid}/updateHeatRecord

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体示例**：

**完整更新食物记录**：
```json
{
  "id": 15,
  "eventName": "苹果",
  "unit": "个",
  "quantity": 2,
  "heatValue": 104,
  "carbohydrate": 25,
  "protein": 1,
  "fat": 0
}
```

**完整更新运动记录**：
```json
{
  "id": 16,
  "eventName": "跑步半小时",
  "unit": "分钟",
  "quantity": 30,
  "heatValue": 350,
  "carbohydrate": null,
  "protein": null,
  "fat": null
}
```

**仅更新营养素信息**：
```json
{
  "id": 15,
  "carbohydrate": 30,
  "protein": 2,
  "fat": 1
}
```

**部分字段更新**：
```json
{
  "id": 15,
  "eventName": "大苹果",
  "heatValue": 120,
  "carbohydrate": 28
}
```

**请求体字段说明**：

| 字段名        | 类型    | 是否必填 | 说明                                                 | 示例值       |
| ------------- | ------- | -------- | ---------------------------------------------------- | ------------ |
| id            | Long    | 是       | 记录ID，必须是当前用户的记录                         | 15           |
| eventName     | String  | 否       | 事件名称，如食物名称或运动名称                       | "跑步半小时" |
| unit          | String  | 否       | 单位：克、个、分钟、小时等                           | "分钟"       |
| quantity      | Integer | 否       | 单位对应的数量，必须大于0                            | 30           |
| heatValue     | Integer | 否       | 热量值，单位：千卡(kcal)                             | 350          |
| carbohydrate  | Integer | 否       | 碳水化合物含量，单位：克（通常用于摄入热量类型记录） | 45           |
| protein       | Integer | 否       | 蛋白质含量，单位：克（通常用于摄入热量类型记录）     | 18           |
| fat           | Integer | 否       | 脂肪含量，单位：克（通常用于摄入热量类型记录）       | 12           |

**字段更新规则**：
- 只有在请求体中包含的字段且值不为null时才会进行更新
- 未包含在请求体中的字段保持原值不变
- 营养素字段（carbohydrate、protein、fat）通常用于"摄入热量"类型的记录
- 对于"消耗热量"类型的记录，营养素字段可以设置为null或0

**返回参数**：

| 参数名  | 类型    | 说明                                              | 示例值     |
| ------- | ------- | ------------------------------------------------- | ---------- |
| code    | Integer | 状态码，0表示成功                                 | 0          |
| message | String  | 提示信息                                          | "更新成功" |
| data    | Object  | 返回数据，包含updated字段和更新后的record记录对象 | {...}      |

**返回参数**：

| 参数名  | 类型    | 说明                     | 示例值     |
| ------- | ------- | ------------------------ | ---------- |
| code    | Integer | 状态码，0表示成功        | 0          |
| message | String  | 提示信息                 | "更新成功" |
| data    | Object  | 返回数据，包含更新结果   | {...}      |

**data对象字段说明**：

| 字段名  | 类型    | 说明                           | 示例值 |
| ------- | ------- | ------------------------------ | ------ |
| updated | Boolean | 是否更新成功                   | true   |
| record  | Object  | 更新后的完整记录对象           | {...}  |

**record对象字段说明**：

| 字段名        | 类型     | 说明                                   | 示例值                |
| ------------- | -------- | -------------------------------------- | --------------------- |
| id            | Long     | 记录ID                                 | 15                    |
| openId        | String   | 用户微信openid                         | "wx_123456789abcdef"  |
| unionId       | String   | 用户微信unionid                        | "unionid_123456789"   |
| eventName     | String   | 事件名称                               | "苹果"                |
| type          | String   | 类型，取值为"摄入热量"或"消耗热量"     | "摄入热量"            |
| unit          | String   | 单位                                   | "个"                  |
| quantity      | Integer  | 数量                                   | 2                     |
| heatValue     | Integer  | 热量值，单位：千卡(kcal)               | 104                   |
| carbohydrate  | Integer  | 碳水化合物含量，单位：克               | 25                    |
| protein       | Integer  | 蛋白质含量，单位：克                   | 1                     |
| fat           | Integer  | 脂肪含量，单位：克                     | 0                     |
| createTime    | DateTime | 创建时间，格式：yyyy-MM-dd HH:mm:ss    | "2024-08-27 08:30:00" |
| updateTime    | DateTime | 更新时间，格式：yyyy-MM-dd HH:mm:ss    | "2024-12-27 10:15:30" |

**返回示例**：

**成功更新食物记录**：
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "updated": true,
    "record": {
      "id": 15,
      "openId": "wx_123456789abcdef",
      "unionId": "unionid_123456789",
      "eventName": "苹果",
      "type": "摄入热量",
      "unit": "个",
      "quantity": 2,
      "heatValue": 104,
      "carbohydrate": 25,
      "protein": 1,
      "fat": 0,
      "createTime": "2024-08-27 08:30:00",
      "updateTime": "2024-12-27 10:15:30"
    }
  }
}
```

**成功更新运动记录**：
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "updated": true,
    "record": {
      "id": 16,
      "openId": "wx_123456789abcdef",
      "unionId": "unionid_123456789",
      "eventName": "跑步半小时",
      "type": "消耗热量",
      "unit": "分钟",
      "quantity": 30,
      "heatValue": 350,
      "carbohydrate": null,
      "protein": null,
      "fat": null,
      "createTime": "2024-08-27 08:30:00",
      "updateTime": "2024-12-27 10:15:30"
    }
  }
}
```

**错误返回示例**：

**记录ID为空**：
```json
{
  "code": 400,
  "message": "记录ID不能为空",
  "data": null
}
```

**记录不存在**：
```json
{
  "code": 404,
  "message": "记录不存在",
  "data": null
}
```

**无权操作记录**：
```json
{
  "code": 403,
  "message": "无权操作此记录",
  "data": null
}
```

**参数格式错误**：
```json
{
  "code": 400,
  "message": "参数格式错误",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**更新失败**：
```json
{
  "code": 500,
  "message": "更新失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                    |
| ------ | -------------- | ------------------------------------------- |
| 400    | 参数错误       | 记录ID为空、参数格式错误、数值类型转换失败  |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败    |
| 403    | 禁止访问       | 尝试操作不属于当前用户的记录                |
| 404    | 记录不存在     | 指定ID的记录在数据库中不存在                |
| 500    | 服务器内部错误 | 数据库更新失败、网络异常或其他服务器处理异常|

**注意事项**：

1. **权限验证**：只能更新属于当前用户的热量记录，不能操作其他用户的记录
2. **字段选择性更新**：只有在请求体中包含的字段且值不为null时才会进行更新
3. **营养素字段使用**：
   - 对于"摄入热量"类型的记录，建议设置营养素字段
   - 对于"消耗热量"类型的记录，营养素字段通常为null
4. **数据类型**：确保传入的数值类型正确，避免类型转换错误
5. **数值范围**：建议对数值进行合理性验证，避免传入负数或过大的值
6. **更新时间**：成功更新后，系统会自动更新记录的 `updateTime` 字段
7. **事务安全**：更新操作在数据库事务中执行，确保数据一致性

**常见使用场景**：
- 修正录入错误的热量记录
- 更新食物的营养素信息
- 调整运动时间和消耗热量
- 批量更新记录的某些字段

### 新增/更新微信运动步数

**接口描述**：新增或更新当天的微信运动步数所消耗的热量。如果是今天的第一条"自动-微信运动步数"事件，则新增记录；如果今天已经有了"自动-微信运动步数"记录，则更新最新的一条记录。系统只处理事件名称为"自动-微信运动步数"且单位为"步"的记录。

**请求URL**：/wx/heat/{appid}/weixinSteps

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**：
```json
{
  "steps": 8500,
  "weight": 65.5
}
```

**请求体字段说明**：

| 字段名 | 类型    | 是否必填 | 说明                                 | 示例值 |
| ------ | ------- | -------- | ------------------------------------ | ------ |
| steps  | Integer | 是       | 当天的微信运动步数                   | 8500   |
| weight | Double  | 否       | 体重（kg），如果没有传入则默认为60kg | 65.5   |

**返回参数**：

| 参数名  | 类型    | 说明                                                 | 示例值                     |
| ------- | ------- | ---------------------------------------------------- | -------------------------- |
| code    | Integer | 状态码，0表示成功                                    | 0                          |
| message | String  | 提示信息                                             | "新增微信运动步数记录成功" |
| data    | Object  | 返回数据，包含操作类型、记录ID、步数和消耗的热量信息 | {...}                      |

**返回data字段说明**：

| 字段名    | 类型    | 说明                         | 示例值 |
| --------- | ------- | ---------------------------- | ------ |
| action    | String  | 操作类型："新增"或"更新"     | "新增" |
| recordId  | Long    | 热量记录的ID                 | 123    |
| steps     | Integer | 步数                         | 8500   |
| weight    | Double  | 体重（kg）                   | 65.5   |
| heatValue | Integer | 消耗的热量，单位：千卡(kcal) | 278    |

**返回示例**：

**新增记录时**：
```json
{
  "code": 0,
  "message": "新增微信运动步数记录成功",
  "data": {
    "action": "新增",
    "recordId": 123,
    "steps": 8500,
    "weight": 65.5,
    "heatValue": 278
  }
}
```

**更新记录时**：
```json
{
  "code": 0,
  "message": "更新微信运动步数记录成功",
  "data": {
    "action": "更新",
    "recordId": 123,
    "steps": 10000,
    "weight": 65.5,
    "heatValue": 328
  }
}
```

**接口特性说明**：

1. **智能判断新增/更新**：系统会自动判断当天是否已存在微信运动步数记录
   - 如果不存在，则新增一条记录
   - 如果已存在一条或多条，则更新最新的一条记录

2. **热量自动计算**：系统使用公式 `体重（kg）× 步数 × 0.0005` 自动计算步数对应的消耗热量

3. **数据标准化**：所有微信运动步数记录统一使用以下标准：
   - `eventName`: "自动-微信运动步数"
   - `type`: "消耗热量"
   - `unit`: "步"

**注意事项**：

1. **步数范围**：建议步数在合理范围内（0-100000步），避免异常数据
2. **热量计算**：系统会根据步数和体重自动计算消耗的热量值
3. **数据覆盖**：同一天多次调用会更新最新的步数和热量值
4. **权限验证**：只能更新当前用户的微信运动步数记录
5. **时区处理**：基于服务器时区判断"今天"的范围
6. **事件名称标准**：系统只处理事件名称为"自动-微信运动步数"的记录
7. **单位标准**：系统只处理单位为"步"的记录
8. **数据唯一性**：每个用户每天只能有一条微信运动步数记录

**常见使用场景**：
- 微信小程序获取用户运动数据后同步到系统
- 定时任务自动更新用户的每日运动步数
- 用户手动刷新当日运动数据
- 运动数据的统计和分析

**错误码说明**：

| 错误码 | 说明           | 可能原因                                         |
| ------ | -------------- | ------------------------------------------------ |
| 400    | 参数错误       | 步数参数为空、格式错误或小于0，体重参数小于等于0 |
| 401    | 未授权         | 令牌无效、已过期或权限不足                       |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常                         |

## 热量记录接口

### 获取指定日期内用户摄入的营养素之合

**接口描述**：获取用户在指定日期范围内摄入的碳水化合物、蛋白质和脂肪的总量。如果不传入日期参数，默认查询今天的数据。

**请求URL**：/wx/heat-record/{appid}/nutrient-summary

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**（可选）：
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-07"
}
```

**请求体字段说明**：

| 字段名    | 类型   | 是否必填 | 说明                                   | 示例值       |
| --------- | ------ | -------- | -------------------------------------- | ------------ |
| startDate | String | 否       | 开始日期，格式：YYYY-MM-DD，默认为今天 | "2024-01-01" |
| endDate   | String | 否       | 结束日期，格式：YYYY-MM-DD，默认为今天 | "2024-01-07" |

**请求说明**：
- 如果不传入请求体，系统默认查询今天的营养素摄入数据
- 开始日期不能晚于结束日期
- 只统计类型为"摄入热量"的记录中的营养素数据

**返回参数**：

| 参数名  | 类型    | 说明                                             | 示例值     |
| ------- | ------- | ------------------------------------------------ | ---------- |
| code    | Integer | 状态码，0表示成功                                | 0          |
| message | String  | 提示信息                                         | "查询成功" |
| data    | Object  | 返回数据，包含指定日期范围内用户摄入的营养素总量 | {...}      |

**返回data字段说明**：

| 字段名            | 类型    | 说明                     | 示例值       |
| ----------------- | ------- | ------------------------ | ------------ |
| totalCarbohydrate | Integer | 碳水化合物总量，单位：克 | 300          |
| totalProtein      | Integer | 蛋白质总量，单位：克     | 120          |
| totalFat          | Integer | 脂肪总量，单位：克       | 80           |
| startDate         | String  | 查询的开始日期           | "2024-01-01" |
| endDate           | String  | 查询的结束日期           | "2024-01-07" |

**返回示例**：

**查询成功**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "totalCarbohydrate": 300,
    "totalProtein": 120,
    "totalFat": 80,
    "startDate": "2024-01-01",
    "endDate": "2024-01-07"
  }
}
```

**查询今天数据（默认）**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "totalCarbohydrate": 45,
    "totalProtein": 18,
    "totalFat": 12,
    "startDate": "2024-12-27",
    "endDate": "2024-12-27"
  }
}
```

**空请求体示例**：
```bash
curl -X POST "https://your-domain.com/wx/heat-record/wx1234567890abcd/nutrient-summary" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json"
```

**接口特性说明**：

1. **智能默认值**：如果不传入日期参数，系统自动使用今天作为开始和结束日期
2. **日期范围查询**：支持查询任意日期范围内的营养素摄入总量
3. **营养素分类统计**：分别统计碳水化合物、蛋白质和脂肪的总量
4. **类型过滤**：只统计"摄入热量"类型的记录，不包含"消耗热量"记录

**错误码说明**：

| 错误码 | 说明           | 可能原因                           |
| ------ | -------------- | ---------------------------------- |
| 400    | 参数错误       | 日期格式错误或开始日期晚于结束日期 |
| 401    | 未授权         | 令牌无效、已过期或权限不足         |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常           |

### 分页获取每日营养素汇总

**接口描述**：分页获取用户每日的热量摄入、消耗和营养素数据，包括热量总摄入、热量总消耗、总摄入的碳水化合物、总摄入的蛋白质、总摄入的脂肪。默认查询最近7天（包含今天），最多支持21天的数据查询。按日期倒序返回，适合营养趋势分析。

**请求URL**：/wx/heat-record/{appid}/daily-nutrient-summary

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**（可选）：
```json
{
  "pageNum": 1,
  "pageSize": 7
}
```

**请求体字段说明**：

| 字段名   | 类型    | 是否必填 | 说明                                       | 示例值 |
| -------- | ------- | -------- | ------------------------------------------ | ------ |
| pageNum  | Integer | 否       | 页码，从1开始，默认为1                     | 1      |
| pageSize | Integer | 否       | 每页记录数，默认为7（最近7天），最大为21天 | 7      |

**成功响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "pageNum": 1,
    "pageSize": 7,
    "total": 14,
    "pages": 2,
    "list": [
      {
        "recordDate": "2024-01-15",
        "totalIntakeCalories": 1800,
        "totalConsumeCalories": 500,
        "netCalories": 1300,
        "totalCarbohydrate": 300,
        "totalProtein": 120,
        "totalFat": 80
      },
      {
        "recordDate": "2024-01-14",
        "totalIntakeCalories": 1650,
        "totalConsumeCalories": 450,
        "netCalories": 1200,
        "totalCarbohydrate": 280,
        "totalProtein": 110,
        "totalFat": 75
      }
    ]
  }
}
```

**响应字段说明**：

| 字段名               | 类型    | 说明                            | 示例值     |
| -------------------- | ------- | ------------------------------- | ---------- |
| pageNum              | Integer | 当前页码                        | 1          |
| pageSize             | Integer | 每页记录数                      | 7          |
| total                | Long    | 总记录数                        | 14         |
| pages                | Integer | 总页数                          | 2          |
| list                 | Array   | 每日营养素汇总数据列表          | -          |
| recordDate           | String  | 记录日期，格式：YYYY-MM-DD      | 2024-01-15 |
| totalIntakeCalories  | Integer | 热量总摄入，单位：千卡(kcal)    | 1800       |
| totalConsumeCalories | Integer | 热量总消耗，单位：千卡(kcal)    | 500        |
| netCalories          | Integer | 净热量（摄入-消耗），单位：千卡 | 1300       |
| totalCarbohydrate    | Integer | 总摄入的碳水化合物，单位：克    | 300        |
| totalProtein         | Integer | 总摄入的蛋白质，单位：克        | 120        |
| totalFat             | Integer | 总摄入的脂肪，单位：克          | 80         |

**错误响应示例**：
```json
{
  "code": 400,
  "message": "每页记录数不能超过21天",
  "timestamp": "2024-01-15T10:30:00",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                             |
| ------ | -------------- | ------------------------------------ |
| 400    | 参数错误       | 页码小于1，每页记录数超过21天或小于1 |
| 401    | 未授权         | 令牌无效、已过期或权限不足           |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常             |

**使用场景**：
- 每日营养摄入趋势图表展示
- 热量平衡分析（摄入vs消耗）
- 营养素摄入模式分析
- 健康数据周报/月报生成
- 饮食习惯跟踪和改进建议

## 饮食模式接口

### 设置饮食模式

**接口描述**：新增或更新用户的饮食模式，如果表中存在对应openId则更新，不存在则新增

**请求URL**：/wx/dietaryMode/{appid}/setDietaryMode

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求体**：
```json
{
  "mode": "平稳减重",
  "coefficient": 1.2
}
```

**请求体字段说明**：

| 字段名      | 类型   | 是否必填 | 说明                                                       | 示例值     |
| ----------- | ------ | -------- | ---------------------------------------------------------- | ---------- |
| mode        | String | 是       | 饮食模式：快速减重、平稳减重、体重维持、自定义             | "平稳减重" |
| coefficient | Double | 是       | 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数） | 1.2        |

**返回参数**：

| 参数名  | 类型    | 说明                       |
| ------- | ------- | -------------------------- |
| code    | Integer | 状态码，0表示成功          |
| message | String  | 提示信息                   |
| data    | Object  | 返回数据，饮食模式信息对象 |

**data对象字段说明**：

| 字段名      | 类型   | 说明                                                       | 示例值                |
| ----------- | ------ | ---------------------------------------------------------- | --------------------- |
| id          | Long   | 主键ID                                                     | 1                     |
| openId      | String | 微信openid                                                 | "wx_123456789abcdef"  |
| unionId     | String | 微信unionid，可为空                                        | "unionid_123456789"   |
| mode        | String | 饮食模式：快速减重、平稳减重、体重维持、自定义             | "平稳减重"            |
| coefficient | Double | 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数） | 1.2                   |
| createTime  | Date   | 创建时间                                                   | "2024-12-27 10:00:00" |
| updateTime  | Date   | 更新时间                                                   | "2024-12-27 10:00:00" |

**返回示例**：

**新增饮食模式成功**：
```json
{
  "code": 0,
  "message": "设置饮食模式成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "mode": "平稳减重",
    "coefficient": 1.2,
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 10:00:00"
  }
}
```

**更新饮食模式成功**：
```json
{
  "code": 0,
  "message": "更新饮食模式成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "mode": "快速减重",
    "coefficient": 0.9,
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 15:30:00"
  }
}
```

**快速减重模式请求示例**：
```json
{
  "mode": "快速减重",
  "coefficient": 0.9
}
```

**体重维持模式请求示例**：
```json
{
  "mode": "体重维持",
  "coefficient": 1.3
}
```

**自定义模式请求示例**：
```json
{
  "mode": "自定义",
  "coefficient": 1.1
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                   |
| ------ | -------------- | -------------------------- |
| 400    | 参数错误       | 饮食模式为空或系数为空     |
| 401    | 未授权         | 令牌无效、已过期或权限不足 |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常   |

### 获取饮食模式

**接口描述**：根据用户的openId获取饮食模式信息

**请求URL**：/wx/dietaryMode/{appid}/getDietaryMode

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型    | 说明                       |
| ------- | ------- | -------------------------- |
| code    | Integer | 状态码，0表示成功          |
| message | String  | 提示信息                   |
| data    | Object  | 返回数据，饮食模式信息对象 |

**data对象字段说明**：

| 字段名      | 类型   | 说明                                                       | 示例值                |
| ----------- | ------ | ---------------------------------------------------------- | --------------------- |
| id          | Long   | 主键ID                                                     | 1                     |
| openId      | String | 微信openid                                                 | "wx_123456789abcdef"  |
| unionId     | String | 微信unionid，可为空                                        | "unionid_123456789"   |
| mode        | String | 饮食模式：快速减重、平稳减重、体重维持、自定义             | "平稳减重"            |
| coefficient | Double | 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数） | 1.2                   |
| createTime  | Date   | 创建时间                                                   | "2024-12-27 10:00:00" |
| updateTime  | Date   | 更新时间                                                   | "2024-12-27 10:00:00" |

**返回示例**：

**获取饮食模式成功**：
```json
{
  "code": 0,
  "message": "获取饮食模式成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "mode": "平稳减重",
    "coefficient": 1.2,
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 10:00:00"
  }
}
```

**无饮食模式记录时的返回示例**：
```json
{
  "code": 0,
  "message": "用户暂无饮食模式",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                   |
| ------ | -------------- | -------------------------- |
| 401    | 未授权         | 令牌无效、已过期或权限不足 |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常   |

**注意事项**：
1. 饮食模式支持四种类型：快速减重、平稳减重、体重维持、自定义
2. 系数为两位小数，通过公式"用户每日计划热量=用户基础代谢热量*系数"来计算用户的每日计划热量
3. 每个用户只能有一条饮食模式记录，重复设置会更新已有记录
4. 饮食模式是制定热量预算和减重计划的重要参考数据

## 基础代谢接口

### 设置基础代谢

**接口描述**：新增或更新用户的基础代谢，如果表中存在对应openId则更新，不存在则新增

**请求URL**：/wx/bmr/{appid}/setBmr

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求体**：
```json
{
  "bmrValue": 1500,
  "generationMode": "公式计算"
}
```

**请求体字段说明**：

| 字段名         | 类型    | 是否必填 | 说明                                 | 示例值     |
| -------------- | ------- | -------- | ------------------------------------ | ---------- |
| bmrValue       | Integer | 是       | 用户的基础代谢，单位：千卡(kcal)     | 1500       |
| generationMode | String  | 是       | 基础代谢的生成方式：公式计算、自定义 | "公式计算" |

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，基础代谢信息 |

**data对象字段说明**：

| 字段名         | 类型    | 说明                                 | 示例值                |
| -------------- | ------- | ------------------------------------ | --------------------- |
| id             | Long    | 主键ID                               | 1                     |
| openId         | String  | 微信openid                           | "wx_123456789abcdef"  |
| unionId        | String  | 微信unionid，可为空                  | "unionid_123456789"   |
| bmrValue       | Integer | 用户的基础代谢，单位：千卡(kcal)     | 1500                  |
| generationMode | String  | 基础代谢的生成方式：公式计算、自定义 | "公式计算"            |
| createTime     | Date    | 创建时间                             | "2024-12-27 10:00:00" |
| updateTime     | Date    | 更新时间                             | "2024-12-27 10:00:00" |

**返回示例**：

**新增基础代谢成功**：
```json
{
  "code": 0,
  "message": "设置基础代谢成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "bmrValue": 1500,
    "generationMode": "公式计算",
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 10:00:00"
  }
}
```

**更新基础代谢成功**：
```json
{
  "code": 0,
  "message": "更新基础代谢成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "bmrValue": 1600,
    "generationMode": "自定义",
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 15:30:00"
  }
}
```

**使用自定义方式的请求示例**：
```json
{
  "bmrValue": 1650,
  "generationMode": "自定义"
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                     |
| ------ | -------------- | ---------------------------- |
| 400    | 参数错误       | 基础代谢值为空或生成方式为空 |
| 401    | 未授权         | 令牌无效、已过期或权限不足   |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常     |

### 获取基础代谢

**接口描述**：根据用户的openId获取基础代谢信息

**请求URL**：/wx/bmr/{appid}/getBmr

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明            |
| ------ | ------ | -------- | --------------- |
| appid  | String | 是       | 微信小程序appid |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，基础代谢信息 |

**data对象字段说明**：

| 字段名         | 类型    | 说明                                 | 示例值                |
| -------------- | ------- | ------------------------------------ | --------------------- |
| id             | Long    | 主键ID                               | 1                     |
| openId         | String  | 微信openid                           | "wx_123456789abcdef"  |
| unionId        | String  | 微信unionid，可为空                  | "unionid_123456789"   |
| bmrValue       | Integer | 用户的基础代谢，单位：千卡(kcal)     | 1500                  |
| generationMode | String  | 基础代谢的生成方式：公式计算、自定义 | "公式计算"            |
| createTime     | Date    | 创建时间                             | "2024-12-27 10:00:00" |
| updateTime     | Date    | 更新时间                             | "2024-12-27 10:00:00" |

**返回示例**：

**获取基础代谢成功**：
```json
{
  "code": 0,
  "message": "获取基础代谢成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "bmrValue": 1500,
    "generationMode": "公式计算",
    "createTime": "2024-12-27 10:00:00",
    "updateTime": "2024-12-27 10:00:00"
  }
}
```

**无基础代谢记录时的返回示例**：
```json
{
  "code": 0,
  "message": "用户暂无基础代谢",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                   |
| ------ | -------------- | -------------------------- |
| 401    | 未授权         | 令牌无效、已过期或权限不足 |
| 500    | 服务器内部错误 | 服务器处理请求时发生异常   |

**注意事项**：
1. 基础代谢值的单位为千卡(kcal)，通常成年人的基础代谢在1200-2000千卡之间
2. 生成方式分为"公式计算"和"自定义"两种：
   - "公式计算"：通过用户的性别、年龄、身高、体重等信息计算得出
   - "自定义"：用户手动输入的基础代谢值
3. 每个用户只能有一条基础代谢记录，重复设置会更新已有记录
4. 基础代谢是制定热量预算和减重计划的重要参考数据

## 体重记录接口

### 新增或更新体重记录

**接口描述**：新增或更新用户的每日体重记录。如果用户在指定日期已有体重记录则更新，否则新增新记录。每个用户每天只能有一条体重记录

**请求URL**：/wx/weight-record/{appid}/saveWeightRecord

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**：
```json
{
  "recordDate": "2024-01-15",
  "weight": 65.5
}
```

**请求体字段说明**：

| 字段名     | 类型   | 是否必填 | 说明                                     | 示例值       |
| ---------- | ------ | -------- | ---------------------------------------- | ------------ |
| recordDate | String | 是       | 记录日期，格式：yyyy-MM-dd               | "2024-01-15" |
| weight     | Double | 是       | 体重，单位：千克(kg)，范围：0.1-999.99kg | 65.5         |

**返回参数**：

| 参数名  | 类型    | 说明                                         | 示例值             |
| ------- | ------- | -------------------------------------------- | ------------------ |
| code    | Integer | 状态码，0表示成功                            | 0                  |
| message | String  | 提示信息                                     | "新增体重记录成功" |
| data    | Object  | 返回数据，体重记录信息对象，详见下方字段说明 | {...}              |

**data对象字段说明**：

| 字段名     | 类型   | 说明                 | 示例值                |
| ---------- | ------ | -------------------- | --------------------- |
| id         | Long   | 主键ID               | 1                     |
| openId     | String | 微信openid           | "wx_123456789abcdef"  |
| unionId    | String | 微信unionid，可为空  | "unionid_123456789"   |
| recordDate | String | 记录日期             | "2024-01-15"          |
| weight     | Double | 体重，单位：千克(kg) | 65.5                  |
| createTime | Date   | 创建时间             | "2024-01-15 10:00:00" |
| updateTime | Date   | 更新时间             | "2024-01-15 10:00:00" |

**返回示例**：

**新增体重记录成功**：
```json
{
  "code": 0,
  "message": "新增体重记录成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "recordDate": "2024-01-15",
    "weight": 65.5,
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:00"
  }
}
```

**更新体重记录成功**：
```json
{
  "code": 0,
  "message": "更新体重记录成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "recordDate": "2024-01-15",
    "weight": 66.2,
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 15:30:00"
  }
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "记录日期不能为空",
  "data": null
}
```

**体重超出范围**：
```json
{
  "code": 400,
  "message": "体重必须大于0.1kg",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "保存体重记录失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 参数错误       | 必填参数为空、体重超出范围或日期格式错误 |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败 |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常 |

**注意事项**：
1. 每个用户每天只能有一条体重记录，重复提交同一天的记录会更新已有记录
2. 体重值支持两位小数，范围在0.1kg到999.99kg之间
3. 记录日期必须为有效的日期格式(yyyy-MM-dd)
4. 系统会根据open_id和record_date的组合来判断是新增还是更新操作
5. 更新操作只会修改体重值，其他字段保持不变

### 获取指定日期体重记录

**接口描述**：根据用户openId和指定日期获取体重记录信息

**请求URL**：/wx/weight-record/{appid}/getWeightRecord

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**：
```json
{
  "recordDate": "2024-01-15"
}
```

**请求体字段说明**：

| 字段名     | 类型   | 是否必填 | 说明                       | 示例值       |
| ---------- | ------ | -------- | -------------------------- | ------------ |
| recordDate | String | 是       | 记录日期，格式：yyyy-MM-dd | "2024-01-15" |

**返回参数**：

| 参数名  | 类型    | 说明                                         | 示例值             |
| ------- | ------- | -------------------------------------------- | ------------------ |
| code    | Integer | 状态码，0表示成功                            | 0                  |
| message | String  | 提示信息                                     | "获取体重记录成功" |
| data    | Object  | 返回数据，体重记录信息对象，详见下方字段说明 | {...}              |

**data对象字段说明**：

| 字段名     | 类型   | 说明                 | 示例值                |
| ---------- | ------ | -------------------- | --------------------- |
| id         | Long   | 主键ID               | 1                     |
| openId     | String | 微信openid           | "wx_123456789abcdef"  |
| unionId    | String | 微信unionid，可为空  | "unionid_123456789"   |
| recordDate | String | 记录日期             | "2024-01-15"          |
| weight     | Double | 体重，单位：千克(kg) | 65.5                  |
| createTime | Date   | 创建时间             | "2024-01-15 10:00:00" |
| updateTime | Date   | 更新时间             | "2024-01-15 10:00:00" |

**返回示例**：

**成功获取体重记录**：
```json
{
  "code": 0,
  "message": "获取体重记录成功",
  "data": {
    "id": 1,
    "openId": "wx_123456789abcdef",
    "unionId": "unionid_123456789",
    "recordDate": "2024-01-15",
    "weight": 65.5,
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:00"
  }
}
```

**指定日期无体重记录**：
```json
{
  "code": 0,
  "message": "该日期暂无体重记录",
  "data": null
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "记录日期不能为空",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "获取体重记录失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 参数错误       | 记录日期为空或日期格式错误               |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败 |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常 |

**注意事项**：
1. 如果指定日期没有体重记录，返回的data字段为null，但状态码仍为0
2. 只能查询当前用户自己的体重记录，无法查询其他用户的记录
3. 记录日期必须为有效的日期格式(yyyy-MM-dd)

### 分页获取体重和热量历史记录（已废弃）

> **⚠️ 废弃通知**：此接口已废弃，请使用新的[分页获取体重](#分页获取体重)接口。

**接口描述**：分页获取用户的体重记录和热量统计信息，按记录日期倒序返回。每条记录包含体重信息以及对应日期的摄入热量、消耗热量和净热量统计

**请求URL**：/wx/weight-record/{appid}/getWeightHeatHistoryByPage

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**：
```json
{
  "pageNum": 1,
  "pageSize": 10
}
```

**请求体字段说明**：

| 字段名   | 类型    | 是否必填 | 说明                                  | 示例值 |
| -------- | ------- | -------- | ------------------------------------- | ------ |
| pageNum  | Integer | 否       | 页码，从1开始（1表示第一页），默认为1 | 1      |
| pageSize | Integer | 否       | 每页记录数，范围：1-100条，默认为10   | 10     |

**返回参数**：

| 参数名  | 类型    | 说明                                     | 示例值     |
| ------- | ------- | ---------------------------------------- | ---------- |
| code    | Integer | 状态码，0表示成功                        | 0          |
| message | String  | 提示信息                                 | "查询成功" |
| data    | Object  | 返回数据，分页结果对象，详见下方字段说明 | {...}      |

**data对象字段说明**：

| 字段名   | 类型    | 说明              | 示例值 |
| -------- | ------- | ----------------- | ------ |
| list     | Array   | 当前页数据列表    | [...]  |
| total    | Long    | 总记录数          | 25     |
| pages    | Integer | 总页数            | 3      |
| pageNum  | Integer | 当前页码(从1开始) | 1      |
| pageSize | Integer | 每页记录数        | 10     |

**list数组中对象字段说明**：

| 字段名               | 类型    | 说明                                    | 示例值       |
| -------------------- | ------- | --------------------------------------- | ------------ |
| recordDate           | String  | 记录日期，格式：yyyy-MM-dd              | "2024-01-15" |
| weightRecord         | Object  | 体重记录信息对象                        | {...}        |
| totalIntakeCalories  | Integer | 摄入热量总和，单位：千卡(kcal)          | 1800         |
| totalConsumeCalories | Integer | 消耗热量总和，单位：千卡(kcal)          | 500          |
| netCalories          | Integer | 净热量（摄入 - 消耗），单位：千卡(kcal) | 1300         |

**weightRecord对象字段说明**：

| 字段名     | 类型   | 说明                 | 示例值                |
| ---------- | ------ | -------------------- | --------------------- |
| id         | Long   | 主键ID               | 1                     |
| openId     | String | 微信openid           | "wx_123456789abcdef"  |
| unionId    | String | 微信unionid，可为空  | "unionid_123456789"   |
| recordDate | String | 记录日期             | "2024-01-15"          |
| weight     | Double | 体重，单位：千克(kg) | 65.5                  |
| createTime | Date   | 创建时间             | "2024-01-15 10:00:00" |
| updateTime | Date   | 更新时间             | "2024-01-15 10:00:00" |

**返回示例**：

**成功获取分页记录**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "recordDate": "2024-01-15",
        "weightRecord": {
          "id": 1,
          "openId": "wx_123456789abcdef",
          "unionId": "unionid_123456789",
          "recordDate": "2024-01-15",
          "weight": 65.5,
          "createTime": "2024-01-15 10:00:00",
          "updateTime": "2024-01-15 10:00:00"
        },
        "totalIntakeCalories": 1800,
        "totalConsumeCalories": 500,
        "netCalories": 1300
      },
      {
        "recordDate": "2024-01-14",
        "weightRecord": {
          "id": 2,
          "openId": "wx_123456789abcdef",
          "unionId": "unionid_123456789",
          "recordDate": "2024-01-14",
          "weight": 65.8,
          "createTime": "2024-01-14 09:30:00",
          "updateTime": "2024-01-14 09:30:00"
        },
        "totalIntakeCalories": 1600,
        "totalConsumeCalories": 400,
        "netCalories": 1200
      }
    ],
    "total": 25,
    "pages": 3,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**无记录时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "页码必须大于0",
  "data": null
}
```

**每页记录数超出限制**：
```json
{
  "code": 400,
  "message": "每页记录数不能超过100条",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "获取历史记录失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                      |
| ------ | -------------- | --------------------------------------------- |
| 400    | 参数错误       | 页码小于等于0、每页记录数小于等于0或超过100条 |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败      |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常      |

**业务逻辑说明**：
1. **分页查询**：系统首先查询用户的体重记录总数，然后按页码和每页记录数进行分页查询
2. **热量统计**：为每个体重记录对应的日期查询该日的热量统计：
   - 摄入热量总和：该日所有"摄入热量"类型记录的热量值总和
   - 消耗热量总和：该日所有"消耗热量"类型记录的热量值总和
   - 净热量：摄入热量 - 消耗热量
3. **排序规则**：按记录日期倒序排列，最新的记录在前
4. **空值处理**：如果某日没有热量记录，对应的摄入和消耗热量均为0

**注意事项**：
1. 只返回有体重记录的日期，没有体重记录的日期不会出现在结果中
2. 热量统计是实时计算的，确保数据的准确性和一致性
3. 分页参数会自动设置默认值：pageNum默认为1，pageSize默认为10
4. 每页最多返回100条记录，超出限制会返回400错误
5. 净热量为正值表示热量盈余，负值表示热量亏损
6. 此接口适合用于体重趋势分析和热量管理的历史数据展示

**使用场景**：
- 体重变化趋势图表展示
- 热量摄入和消耗的历史分析
- 减重效果评估
- 饮食和运动计划的调整参考

### 分页获取体重

**接口描述**：分页获取用户的体重记录，默认查询最近7天（包含今天），最多支持21天的数据查询。按记录日期倒序返回，适合体重趋势展示。

**请求URL**：/wx/weight-record/{appid}/getWeightByPage

**请求方式**：POST

**请求参数**：

| 参数名 | 类型   | 是否必填 | 说明                                          | 示例值           |
| ------ | ------ | -------- | --------------------------------------------- | ---------------- |
| appid  | String | 是       | 微信小程序appid，用于识别请求来源的小程序应用 | wx1234567890abcd |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求体**（可选）：
```json
{
  "pageNum": 1,
  "pageSize": 7
}
```

**请求体字段说明**：

| 字段名   | 类型    | 是否必填 | 说明                                   | 示例值 |
| -------- | ------- | -------- | -------------------------------------- | ------ |
| pageNum  | Integer | 否       | 页码，从1开始，默认为1                 | 1      |
| pageSize | Integer | 否       | 每页记录数，默认为7（最近7天），最大21 | 7      |

**请求说明**：
- 如果不传入请求体，系统默认查询第1页，每页7条记录（最近7天）
- pageSize最大为21天，超过会返回参数错误
- 按记录日期倒序返回，最新的记录在前

**返回参数**：

| 参数名  | 类型    | 说明                                     | 示例值     |
| ------- | ------- | ---------------------------------------- | ---------- |
| code    | Integer | 状态码，0表示成功                        | 0          |
| message | String  | 提示信息                                 | "查询成功" |
| data    | Object  | 返回数据，分页结果对象，详见下方字段说明 | {...}      |

**data对象字段说明**：

| 字段名   | 类型    | 说明              | 示例值 |
| -------- | ------- | ----------------- | ------ |
| list     | Array   | 当前页数据列表    | [...]  |
| total    | Long    | 总记录数          | 25     |
| pages    | Integer | 总页数            | 4      |
| pageNum  | Integer | 当前页码(从1开始) | 1      |
| pageSize | Integer | 每页记录数        | 7      |

**list数组中对象字段说明**：

| 字段名     | 类型   | 说明                 | 示例值                |
| ---------- | ------ | -------------------- | --------------------- |
| id         | Long   | 主键ID               | 1                     |
| openId     | String | 微信openid           | "wx_123456789abcdef"  |
| unionId    | String | 微信unionid，可为空  | "unionid_123456789"   |
| recordDate | String | 记录日期             | "2024-01-15"          |
| weight     | Double | 体重，单位：千克(kg) | 65.5                  |
| createTime | Date   | 创建时间             | "2024-01-15 10:00:00" |
| updateTime | Date   | 更新时间             | "2024-01-15 10:00:00" |

**返回示例**：

**成功获取分页记录**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 7,
        "openId": "wx_123456789abcdef",
        "unionId": "unionid_123456789",
        "recordDate": "2024-01-22",
        "weight": 64.8,
        "createTime": "2024-01-22 08:00:00",
        "updateTime": "2024-01-22 08:00:00"
      },
      {
        "id": 6,
        "openId": "wx_123456789abcdef",
        "unionId": "unionid_123456789",
        "recordDate": "2024-01-21",
        "weight": 65.1,
        "createTime": "2024-01-21 08:00:00",
        "updateTime": "2024-01-21 08:00:00"
      },
      {
        "id": 5,
        "openId": "wx_123456789abcdef",
        "unionId": "unionid_123456789",
        "recordDate": "2024-01-20",
        "weight": 65.3,
        "createTime": "2024-01-20 08:00:00",
        "updateTime": "2024-01-20 08:00:00"
      }
    ],
    "total": 25,
    "pages": 4,
    "pageNum": 1,
    "pageSize": 7
  }
}
```

**无记录时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 7
  }
}
```

**空请求体示例**：
```bash
curl -X POST "https://your-domain.com/wx/weight-record/wx1234567890abcd/getWeightByPage" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json"
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "每页记录数不能超过21天",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "获取体重记录失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                   |
| ------ | -------------- | ------------------------------------------ |
| 400    | 参数错误       | 页码小于等于0、每页记录数小于等于0或超过21 |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败   |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常   |

**接口特性说明**：

1. **智能默认值**：如果不传入请求体，系统自动使用默认分页参数（第1页，每页7条记录）
2. **适度分页**：默认7天的数据量适合移动端展示，最大21天满足更长期的趋势查看需求
3. **纯净数据**：只返回体重记录，不包含热量统计，接口响应更快
4. **倒序排列**：按记录日期倒序返回，最新的体重记录在前，便于查看最近趋势

**注意事项**：
1. 只返回用户自己的体重记录，无法查询其他用户的记录
2. 分页参数会自动设置默认值和边界限制
3. 每页最多返回21条记录，适合体重趋势图的展示需求
4. 此接口专注于体重数据，如需要热量统计请使用其他相关接口
5. 返回结果按记录日期倒序排列，便于前端直接用于趋势图表

**使用场景**：
- 体重变化趋势图表（7天/14天/21天视图）
- 简洁的体重记录列表展示
- 移动端体重管理界面
- 体重数据的快速查询和展示

## 记忆单词-阿里云语音合成

### 阿里云语音合成

**接口描述**：将文本转换为语音，使用阿里云TTS服务，支持多种发音人和语速配置。成功后会在memory_words_tts_record表中创建录音记录，并在memory_words_user_and_record_relation表中创建用户与录音的关系记录

**请求URL**：/memoriseWords/aliTts/synthesize

**请求方式**：POST

**请求参数**：

| 参数名        | 类型    | 是否必填 | 说明                                                                                |
| ------------- | ------- | -------- | ----------------------------------------------------------------------------------- |
| environment   | String  | 否       | 环境，可选值为"中文"或"英文"，默认为"中文"                                          |
| voiceKey      | String  | 否       | 发音人Key，可选值为"普通话"（中文环境默认）、"英式英语"、"美式英语"，默认为"普通话" |
| speechRateKey | String  | 否       | 语速Key，可选值为"慢速"、"中速"、"快速"，默认为"慢速"                               |
| textContent   | String  | 是       | 要合成的文本内容                                                                    |
| repeatCount   | Integer | 否       | 重复次数，用于指定文本重复播放的次数，默认为1                                       |
| pauseSeconds  | Float   | 否       | 停顿秒数，用于指定停顿的时长，单位为秒，默认为1.0                                   |
| sampleRate    | Integer | 否       | 采样率，可选值为8000或16000，默认为16000（此参数会被数据库配置覆盖）                |
| volume        | Integer | 否       | 音量，范围0-100，默认为50（此参数会被数据库配置覆盖）                               |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：

**中文环境示例**：
```json
{
  "environment": "中文",
  "voiceKey": "普通话",
  "speechRateKey": "慢速",
  "textContent": "欢迎使用阿里云语音合成服务，这是一个测试音频。",
  "repeatCount": 2,
  "pauseSeconds": 1.5
}
```

**英文环境示例**：
```json
{
  "environment": "英文",
  "voiceKey": "英式英语",
  "speechRateKey": "快速",
  "textContent": "Welcome to Alibaba Cloud Text-to-Speech service. This is a test audio.",
  "repeatCount": 1,
  "pauseSeconds": 1.0
}
```

**多行文本示例**：
```json
{
  "environment": "中文",
  "voiceKey": "普通话",
  "speechRateKey": "慢速",
  "textContent": "苹果\n香蕉\n橙子\n葡萄\n草莓",
  "repeatCount": 3,
  "pauseSeconds": 2.0
}
```

**返回参数**：

| 参数名  | 类型    | 说明                     |
| ------- | ------- | ------------------------ |
| code    | Integer | 状态码，0表示成功        |
| message | String  | 提示信息                 |
| data    | String  | 返回数据，OSS音频文件URL |

**返回示例**：
```json
{
  "code": 0,
  "message": "阿里云语音合成成功",
  "data": "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_1703123456789.mp3"
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

**配置说明**：
- 系统会根据 `environment`、`voiceKey`、`speechRateKey` 从 `memory_words_ali_tones` 表中查询对应的配置
- 如果数据库中有配置，会使用数据库中的 `sample_rate`、`voice_value`、`volume`、`speech_rate_value`
- 如果数据库中没有配置，会使用请求参数中的默认值或系统默认值

### 分页查询用户可访问的语音合成记录

**接口描述**：通过用户与录音关系表查询用户可访问的录音记录，支持按文本内容进行模糊搜索。用户身份通过JWT token自动获取，确保只能查看自己有权限访问的录音记录

**请求URL**：/memoriseWords/aliTts/pageRecords

**请求方式**：GET

**请求参数**：

| 参数名     | 类型    | 是否必填 | 说明                                  |
| ---------- | ------- | -------- | ------------------------------------- |
| searchText | String  | 否       | 文本内容搜索关键词(模糊匹配)          |
| page       | Integer | 否       | 页码，从1开始（1表示第一页），默认为1 |
| size       | Integer | 否       | 每页大小，默认为10                    |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```
GET /memoriseWords/aliTts/pageRecords?searchText=阿里云&page=1&size=10
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，分页结果对象 |

**data对象字段说明**：

| 字段名   | 类型    | 说明              |
| -------- | ------- | ----------------- |
| list     | Array   | 当前页数据列表    |
| total    | Long    | 总记录数          |
| pages    | Integer | 总页数            |
| pageNum  | Integer | 当前页码(从1开始) |
| pageSize | Integer | 每页大小          |

**list数组中对象字段说明**：

| 字段名        | 类型    | 说明                                  |
| ------------- | ------- | ------------------------------------- |
| id            | Long    | 记录ID                                |
| relationId    | Long    | 关系表主键ID（relation_id）           |
| openId        | String  | 录音创建人-微信openId                 |
| unionId       | String  | 录音创建人-微信unionId                |
| apiType       | String  | 接口类型，如"火山引擎"、"阿里云"      |
| environment   | String  | 语音环境，如"中文"、"英文"            |
| voiceType     | String  | 音色类型-key，如"普通话"、"英式英语"  |
| voiceValue    | String  | 音色类型-value，如"xiaoyun"、"siqi"   |
| textContent   | String  | 合成的文本内容                        |
| textType      | String  | 文本类型，固定为"ssml"                |
| speedKey      | String  | 语速-key，如"慢速"、"中速"、"快速"    |
| speedValue    | Double  | 语速-value，实际值                    |
| repeatCount   | Integer | 重复次数                              |
| pauseSeconds  | Double  | 暂停秒数                              |
| audioUrl      | String  | 语音文件URL                           |
| audioDuration | Integer | 语音时长(秒)                          |
| status        | Integer | 状态：0-处理中，1-成功，2-失败        |
| errorMsg      | String  | 错误信息，仅在状态为失败时有值        |
| createTime    | String  | 创建时间，格式："yyyy-MM-dd HH:mm:ss" |
| updateTime    | String  | 更新时间，格式："yyyy-MM-dd HH:mm:ss" |

**返回示例**：

**成功查询示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 25,
        "relationId": 15,
        "openId": "oCreator123456789abcdef",
        "unionId": "unionidCreator123456789",
        "apiType": "阿里云",
        "environment": "中文",
        "voiceType": "普通话",
        "voiceValue": "xiaoyun",
        "textContent": "欢迎使用阿里云语音合成服务，这是一个测试音频。",
        "textType": "ssml",
        "speedKey": "慢速",
        "speedValue": -100,
        "repeatCount": 2,
        "pauseSeconds": 1.5,
        "audioUrl": "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_1703123456789.mp3",
        "audioDuration": 8,
        "status": 1,
        "errorMsg": null,
        "createTime": "2024-12-30 15:30:00",
        "updateTime": "2024-12-30 15:30:00"
      },
      {
        "id": 24,
        "relationId": 14,
        "openId": "oCreator987654321fedcba",
        "unionId": "unionidCreator987654321",
        "apiType": "阿里云",
        "environment": "英文",
        "voiceType": "英式英语",
        "voiceValue": "siqi",
        "textContent": "Welcome to Alibaba Cloud Text-to-Speech service.",
        "textType": "ssml",
        "speedKey": "快速",
        "speedValue": 1.5,
        "repeatCount": 1,
        "pauseSeconds": 1.0,
        "audioUrl": "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_1703123456790.mp3",
        "audioDuration": 5,
        "status": 1,
        "errorMsg": null,
        "createTime": "2024-12-30 14:30:00",
        "updateTime": "2024-12-30 14:30:00"
      }
    ],
    "total": 15,
    "pages": 2,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**无权限访问的录音时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

### 获取单条用户可访问的语音合成记录

**接口描述**：通过用户与录音关系表验证权限后获取录音记录详情。用户身份通过JWT token自动获取，确保只能获取用户有权限访问的录音记录

**请求URL**：/memoriseWords/aliTts/getRecord

**请求方式**：GET

**请求参数**：

| 参数名 | 类型 | 是否必填 | 说明       |
| ------ | ---- | -------- | ---------- |
| id     | Long | 是       | 录音记录ID |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```
GET /memoriseWords/aliTts/getRecord?id=25
```

**返回参数**：

| 参数名  | 类型    | 说明                         |
| ------- | ------- | ---------------------------- |
| code    | Integer | 状态码，0表示成功            |
| message | String  | 提示信息                     |
| data    | Object  | 返回数据，阿里云语音合成记录 |

**data对象字段说明**：

| 字段名        | 类型    | 说明                                  |
| ------------- | ------- | ------------------------------------- |
| id            | Long    | 记录ID                                |
| openId        | String  | 录音创建人-微信openId                 |
| unionId       | String  | 录音创建人-微信unionId                |
| apiType       | String  | 接口类型，如"火山引擎"、"阿里云"      |
| environment   | String  | 语音环境，如"中文"、"英文"            |
| voiceType     | String  | 音色类型-key，如"普通话"、"英式英语"  |
| voiceValue    | String  | 音色类型-value，如"xiaoyun"、"siqi"   |
| textContent   | String  | 合成的文本内容                        |
| textType      | String  | 文本类型，固定为"ssml"                |
| speedKey      | String  | 语速-key，如"慢速"、"中速"、"快速"    |
| speedValue    | Double  | 语速-value，实际值                    |
| repeatCount   | Integer | 重复次数                              |
| pauseSeconds  | Double  | 暂停秒数                              |
| audioUrl      | String  | 语音文件URL                           |
| audioDuration | Integer | 语音时长(秒)                          |
| status        | Integer | 状态：0-处理中，1-成功，2-失败        |
| errorMsg      | String  | 错误信息，仅在状态为失败时有值        |
| createTime    | String  | 创建时间，格式："yyyy-MM-dd HH:mm:ss" |
| updateTime    | String  | 更新时间，格式："yyyy-MM-dd HH:mm:ss" |

**返回示例**：

**成功获取记录示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "id": 25,
    "openId": "oCreator123456789abcdef",
    "unionId": "unionidCreator123456789",
    "apiType": "阿里云",
    "environment": "中文",
    "voiceType": "普通话",
    "voiceValue": "xiaoyun",
    "textContent": "欢迎使用阿里云语音合成服务，这是一个测试音频。",
    "textType": "ssml",
    "speedKey": "慢速",
    "speedValue": -100,
    "repeatCount": 2,
    "pauseSeconds": 1.5,
    "audioUrl": "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_1703123456789.mp3",
    "audioDuration": 8,
    "status": 1,
    "errorMsg": null,
    "createTime": "2024-12-30 15:30:00",
    "updateTime": "2024-12-30 15:30:00"
  }
}
```

**记录不存在或无权限访问示例**：
```json
{
  "code": 404,
  "message": "记录不存在或无权限访问",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明                   |
| ------ | ---------------------- |
| 400    | 参数错误               |
| 401    | 未授权                 |
| 404    | 记录不存在或无权限访问 |
| 500    | 服务器内部错误         |

### 删除用户与录音的关系记录

**接口描述**：删除用户与录音的关系记录，实现真删除。注意：这只会删除用户与录音的关系，不会删除实际的录音文件，其他有权限的用户仍可访问该录音

**请求URL**：/memoriseWords/aliTts/deleteRecords

**请求方式**：POST

**请求参数**：

| 参数名 | 类型       | 是否必填 | 说明           |
| ------ | ---------- | -------- | -------------- |
| ids    | List<Long> | 是       | 录音记录ID集合 |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求参数示例**：
```json
[25, 24, 23]
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，删除结果信息 |

**data对象字段说明**：

| 字段名       | 类型    | 说明               |
| ------------ | ------- | ------------------ |
| deletedCount | Integer | 删除的关系记录数量 |

**返回示例**：

**成功删除关系记录示例**：
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "deletedCount": 3
  }
}
```

**无权限访问的录音或记录不存在时的返回示例**：
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "deletedCount": 0
  }
}
```

**部分删除成功示例（如：请求删除3个，但只有2个有权限）**：
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "deletedCount": 2
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

**注意事项**：
1. 只能删除当前用户与录音的关系记录，系统会自动根据token验证用户身份
2. 如果ID集合中包含用户无权限访问的录音ID，这些关系记录不会被删除
3. 删除操作不可逆，请谨慎操作
4. 返回的deletedCount表示实际删除的关系记录数量
5. 删除关系记录不会影响录音文件本身，其他有权限的用户仍可访问该录音

### 获取分享录音详情

**接口描述**：先在用户与录音关系表中绑定record_id和open_id的关系，然后通过record_id从录音记录表中获取录音详情返回。此接口结合了权限绑定和内容获取的功能，适用于分享场景

**请求URL**：/memoriseWords/aliTts/getSharedRecord

**请求方式**：GET

**请求参数**：

| 参数名   | 类型 | 是否必填 | 说明                                     | 示例值 |
| -------- | ---- | -------- | ---------------------------------------- | ------ |
| recordId | Long | 是       | 录音记录ID，要获取详情的录音记录唯一标识 | 25     |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份并获取用户的openId。

**请求参数示例**：

**基本获取示例**：
```
GET /memoriseWords/aliTts/getSharedRecord?recordId=25
```

**返回参数**：

| 参数名  | 类型    | 说明                         | 示例值             |
| ------- | ------- | ---------------------------- | ------------------ |
| code    | Integer | 状态码，0表示成功            | 0                  |
| message | String  | 提示信息                     | "获取录音详情成功" |
| data    | Object  | 返回数据，阿里云语音合成记录 | {...}              |

**data对象字段说明**：

| 字段名        | 类型    | 说明                                  | 示例值                                                                                |
| ------------- | ------- | ------------------------------------- | ------------------------------------------------------------------------------------- |
| id            | Long    | 记录ID                                | 25                                                                                    |
| openId        | String  | 录音创建人-微信openId                 | "oCreator123456789abcdef"                                                             |
| unionId       | String  | 录音创建人-微信unionId                | "unionidCreator123456789"                                                             |
| apiType       | String  | 接口类型，如"火山引擎"、"阿里云"      | "阿里云"                                                                              |
| environment   | String  | 语音环境，如"中文"、"英文"            | "中文"                                                                                |
| voiceType     | String  | 音色类型-key，如"普通话"、"英式英语"  | "普通话"                                                                              |
| voiceValue    | String  | 音色类型-value，如"xiaoyun"、"siqi"   | "xiaoyun"                                                                             |
| textContent   | String  | 合成的文本内容                        | "欢迎使用阿里云语音合成服务，这是一个测试音频。"                                      |
| textType      | String  | 文本类型，固定为"ssml"                | "ssml"                                                                                |
| speedKey      | String  | 语速-key，如"慢速"、"中速"、"快速"    | "慢速"                                                                                |
| speedValue    | Double  | 语速-value，实际值                    | -100                                                                                  |
| repeatCount   | Integer | 重复次数                              | 2                                                                                     |
| pauseSeconds  | Double  | 暂停秒数                              | 1.5                                                                                   |
| audioUrl      | String  | 语音文件URL                           | "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_xxx.mp3" |
| audioDuration | Integer | 语音时长(秒)                          | 8                                                                                     |
| status        | Integer | 状态：0-处理中，1-成功，2-失败        | 1                                                                                     |
| errorMsg      | String  | 错误信息，仅在状态为失败时有值        | null                                                                                  |
| createTime    | String  | 创建时间，格式："yyyy-MM-dd HH:mm:ss" | "2024-12-30 15:30:00"                                                                 |
| updateTime    | String  | 更新时间，格式："yyyy-MM-dd HH:mm:ss" | "2024-12-30 15:30:00"                                                                 |

**返回示例**：

**成功获取录音详情示例**：
```json
{
  "code": 0,
  "message": "获取录音详情成功",
  "data": {
    "id": 25,
    "openId": "oCreator123456789abcdef",
    "unionId": "unionidCreator123456789",
    "apiType": "阿里云",
    "environment": "中文",
    "voiceType": "普通话",
    "voiceValue": "xiaoyun",
    "textContent": "欢迎使用阿里云语音合成服务，这是一个测试音频。",
    "textType": "ssml",
    "speedKey": "慢速",
    "speedValue": -100,
    "repeatCount": 2,
    "pauseSeconds": 1.5,
    "audioUrl": "https://example-bucket.oss-cn-hangzhou.aliyuncs.com/memory_words/ali_speech_1703123456789.mp3",
    "audioDuration": 8,
    "status": 1,
    "errorMsg": null,
    "createTime": "2024-12-30 15:30:00",
    "updateTime": "2024-12-30 15:30:00"
  }
}
```

**录音记录不存在示例**：
```json
{
  "code": 404,
  "message": "录音记录不存在",
  "data": null
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "录音记录ID不能为空",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "获取录音详情失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 参数错误       | 录音记录ID为空或参数格式错误             |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败 |
| 404    | 资源不存在     | 指定ID的录音记录不存在                   |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常 |

**业务逻辑说明**：
1. **Token验证**：验证请求头中的JWT token并获取用户openId
2. **参数验证**：验证recordId参数不为空
3. **绑定关系**：在 `memory_words_user_and_record_relation` 表中创建/绑定 `record_id` 和 `open_id` 的关系
4. **获取详情**：通过 `record_id` 从 `memory_words_tts_record` 表中获取录音详情
5. **返回结果**：返回完整的录音记录信息

**注意事项**：
1. 此接口会自动建立用户与录音的访问关系，如果关系已存在则不会重复创建
2. 接口结合了权限绑定和内容获取功能，简化了客户端调用流程
3. 返回的录音详情包含完整的语音合成参数和音频文件信息
4. 适用于分享场景，用户通过分享链接可以一步完成权限获取和内容访问
5. 绑定关系建立后，用户可以通过其他查询接口正常访问该录音

**并发安全保障**：
为了防止页面同时调用多次该接口导致重复插入数据的问题，系统采用了数据库层面的唯一性约束：

1. **数据库唯一索引**：在 `memory_words_user_and_record_relation` 表上创建了 `open_id` 和 `record_id` 的联合唯一索引
   ```sql
   ALTER TABLE memory_words_user_and_record_relation 
   ADD UNIQUE INDEX uk_open_id_record_id (open_id, record_id);
   ```

2. **重复插入处理**：当检测到唯一索引冲突时，系统会自动查询已存在的记录并返回其ID，确保不会产生重复数据

3. **并发场景处理**：即使多个请求同时执行，数据库层面的唯一性约束也能保证 `open_id` 和 `record_id` 的组合在表中保持唯一

**使用场景**：
- 用户通过分享链接访问录音
- 扫码获取录音内容
- 第三方应用集成录音分享功能
- 教学场景中的录音资源分享

## 记忆单词-课本单词

### 获取课本词语

**接口描述**：根据课本单词ID列表查询词语，支持同时获取课文划词

**请求URL**：/memoriseWords/textbook/words

**请求方式**：POST

**请求参数**：

| 参数名             | 类型       | 是否必填 | 说明                          |
| ------------------ | ---------- | -------- | ----------------------------- |
| ids                | List<Long> | 是       | 记忆单词-课本单词ID列表       |
| includeLessonWords | Boolean    | 否       | 是否包含课文划词，默认为false |

**请求参数示例**：
```json
{
  "ids": [1, 2, 3],
  "includeLessonWords": true
}
```

**返回参数**：

| 参数名  | 类型    | 说明               |
| ------- | ------- | ------------------ |
| code    | Integer | 状态码，0表示成功  |
| message | String  | 提示信息           |
| data    | Array   | 返回数据，词语列表 |

**返回示例**：
```json
{
  "code": 0,
  "message": "获取课本词语成功",
  "data": [
    "蝌蚪",
    "游泳",
    "妈妈",
    "找到",
    "中国",
    "人民",
    "春天",
    "夏天"
  ]
}
```

**未找到匹配记录时的返回示例**：
```json
{
  "code": 0,
  "message": "未找到匹配的课本词语",
  "data": []
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 500    | 服务器内部错误 |

### 获取课文标题列表

**接口描述**：根据学科、教材版本、年级、学期查询课文标题列表，按单元排序升序、课文排序升序、ID升序进行排列

**请求URL**：/memoriseWords/textbook/lessonTitles

**请求方式**：POST

**请求参数**：

| 参数名          | 类型    | 是否必填 | 说明     |
| --------------- | ------- | -------- | -------- |
| subject         | String  | 是       | 学科     |
| textbookVersion | String  | 是       | 教材版本 |
| grade           | Integer | 是       | 年级     |
| term            | Integer | 是       | 学期     |

**请求参数示例**：
```json
{
  "subject": "语文",
  "textbookVersion": "人教版",
  "grade": 1,
  "term": 1
}
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Array   | 返回数据，课文标题列表 |

**data数组中对象字段说明**：

| 字段名      | 类型    | 说明     |
| ----------- | ------- | -------- |
| id          | Long    | ID       |
| unitSort    | Integer | 单元排序 |
| unitInfo    | String  | 单元信息 |
| lessonSort  | Integer | 课文排序 |
| lessonTitle | String  | 课文标题 |

**返回示例**：
```json
{
  "code": 0,
  "message": "获取课文标题列表成功",
  "data": [
    {
      "id": 1,
      "unitSort": 1,
      "unitInfo": "第一单元",
      "lessonSort": 1,
      "lessonTitle": "小蝌蚪找妈妈"
    },
    {
      "id": 2,
      "unitSort": 1,
      "unitInfo": "第一单元",
      "lessonSort": 2,
      "lessonTitle": "我是中国人"
    },
    {
      "id": 3,
      "unitSort": 2,
      "unitInfo": "第二单元",
      "lessonSort": 1,
      "lessonTitle": "四季"
    }
  ]
}
```

**未找到匹配记录时的返回示例**：
```json
{
  "code": 0,
  "message": "未找到匹配的课文标题",
  "data": []
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 500    | 服务器内部错误 |

## 记忆单词-手写识别

### 识别用户手写单词

**接口描述**：识别用户上传的手写单词图片，通过阿里云OSS存储和百度手写文字识别API实现。支持多种图片格式，适用于记忆单词应用中的手写输入场景。

**请求URL**：/memoriseWords/compareResult/recognizeUserWords

**请求方式**：POST

**请求参数**：

| 参数名 | 类型          | 是否必填 | 说明                                                     | 示例值           |
| ------ | ------------- | -------- | -------------------------------------------------------- | ---------------- |
| file   | MultipartFile | 是       | 用户上传的手写单词图片文件，支持jpg、png、jpeg等常见格式 | 手写单词图片文件 |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
Content-Type: multipart/form-data
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数示例**：

**使用curl命令示例**：
```bash
curl -X POST "https://your-domain.com/memoriseWords/compareResult/recognizeUserWords" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: multipart/form-data" \
  -F "file=@handwriting_words.jpg"
```

**使用uniapp微信小程序示例**：
```javascript
// 选择图片
uni.chooseImage({
  count: 1,
  sizeType: ['compressed'],
  sourceType: ['camera', 'album'],
  success: function (res) {
    const tempFilePath = res.tempFilePaths[0];
    
    // 上传图片进行识别
    uni.uploadFile({
      url: 'https://your-domain.com/memoriseWords/compareResult/recognizeUserWords',
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: function (uploadRes) {
        const result = JSON.parse(uploadRes.data);
        if (result.code === 0) {
          console.log('识别结果：', result.data);
        } else {
          console.error('识别失败：', result.message);
        }
      },
      fail: function (error) {
        console.error('上传失败：', error);
      }
    });
  }
});
```

**返回参数**：

| 参数名  | 类型    | 说明                                             | 示例值          |
| ------- | ------- | ------------------------------------------------ | --------------- |
| code    | Integer | 状态码，0表示成功                                | 0               |
| message | String  | 提示信息                                         | "识别成功"      |
| data    | String  | 返回数据，识别出的文字内容，多个单词用换行符分隔 | "apple\nbanana" |

**返回示例**：

**成功识别单个单词**：
```json
{
  "code": 0,
  "message": "识别成功",
  "data": "apple"
}
```

**成功识别多个单词**：
```json
{
  "code": 0,
  "message": "识别成功",
  "data": "apple\nbanana\norange"
}
```

**成功识别中文单词**：
```json
{
  "code": 0,
  "message": "识别成功",
  "data": "苹果\n香蕉\n橙子"
}
```

**成功识别混合内容**：
```json
{
  "code": 0,
  "message": "识别成功",
  "data": "apple\n苹果\nbanana\n香蕉"
}
```

**错误返回示例**：

**文件为空**：
```json
{
  "code": 400,
  "message": "请选择要识别的图片文件",
  "data": null
}
```

**文件格式不支持**：
```json
{
  "code": 400,
  "message": "不支持的文件格式，请上传jpg、png、jpeg等图片格式",
  "data": null
}
```

**图片上传OSS失败**：
```json
{
  "code": 500,
  "message": "图片上传到OSS失败",
  "data": null
}
```

**OCR识别失败**：
```json
{
  "code": 500,
  "message": "文字识别失败：图片质量过低，无法识别",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "识别失败: 服务器内部错误",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 参数错误       | 文件为空、格式不支持或文件大小超限       |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败 |
| 500    | 服务器内部错误 | OSS上传失败、OCR识别失败或服务器处理异常 |

**业务逻辑说明**：

1. **文件验证**：验证上传的文件不为空，格式为支持的图片格式
2. **图片上传OSS**：将图片上传到阿里云OSS的"memory_words"目录，生成唯一文件名
3. **OCR识别**：调用百度手写文字识别API，传入OSS图片地址进行在线识别
4. **结果解析**：解析OCR识别结果，提取所有识别出的文字内容
5. **返回结果**：将识别出的文字内容返回给客户端

**技术特性**：

- **支持格式**：jpg、png、jpeg等常见图片格式
- **文件命名**：自动生成唯一文件名，格式为`handwriting_{用户ID后8位}_{时间戳}.{扩展名}`
- **存储位置**：阿里云OSS的"memory_words"目录
- **识别引擎**：百度手写文字识别API
- **并发安全**：支持多用户同时使用，文件命名确保唯一性

**使用场景**：

- **单词学习**：用户手写英文单词进行识别和练习
- **中文识字**：识别手写的中文字符和词语
- **作业批改**：教师识别学生手写作业内容
- **笔记整理**：将手写笔记转换为电子文本
- **多语言支持**：支持中英文混合识别

**注意事项**：

1. **图片质量**：建议上传清晰、对比度高的图片以获得更好的识别效果
2. **文件大小**：建议图片文件大小不超过10MB
3. **识别精度**：手写识别准确率受书写工整度、图片质量等因素影响
4. **网络要求**：需要稳定的网络连接以支持OSS上传和OCR识别
5. **权限控制**：只有通过身份验证的用户才能使用此接口

**最佳实践**：

- 在光线充足的环境下拍摄手写内容
- 确保手写内容与背景有足够的对比度
- 避免图片模糊、倾斜或包含过多干扰元素
- 对于重要内容，建议人工核对识别结果

### 新增比对结果

**接口描述**：根据relationId获取原始内容，与用户输入内容进行比对，并保存结果。系统会自动将原始内容和用户输入内容按"\n"分割成单词列表，按顺序逐一比对，生成详细的比对结果统计和明细信息。

**请求URL**：/memoriseWords/compareResult/addCompareResult

**请求方式**：POST

**请求参数**：

| 参数名     | 类型   | 是否必填 | 说明                                            | 示例值          |
| ---------- | ------ | -------- | ----------------------------------------------- | --------------- |
| relationId | Long   | 是       | memory_words_user_and_record_relation表的主键id | 123             |
| userWords  | String | 是       | OCR识别的用户默写内容，多个单词用换行符"\n"分隔 | "apple\nbanana" |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
Content-Type: application/json
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数示例**：

**基本比对示例**：
```json
{
  "relationId": 123,
  "userWords": "apple\nbanana\norange"
}
```

**中文单词比对示例**：
```json
{
  "relationId": 124,
  "userWords": "苹果\n香蕉\n橙子"
}
```

**混合内容比对示例**：
```json
{
  "relationId": 125,
  "userWords": "apple\n苹果\nbanana\n香蕉"
}
```

**部分空白比对示例**：
```json
{
  "relationId": 126,
  "userWords": "apple\n\norange"
}
```

**返回参数**：

| 参数名  | 类型    | 说明                                           | 示例值             |
| ------- | ------- | ---------------------------------------------- | ------------------ |
| code    | Integer | 状态码，0表示成功                              | 0                  |
| message | String  | 提示信息                                       | "比对结果保存成功" |
| data    | Object  | 返回数据，包含汇总信息和明细信息的比对结果对象 | {...}              |

**data对象字段说明**：

| 字段名  | 类型   | 说明                                         | 示例值 |
| ------- | ------ | -------------------------------------------- | ------ |
| summary | Object | 汇总信息，包含总词数、正确数、错误数、空白数 | {...}  |
| details | Array  | 明细信息，包含每个单词的比对结果             | [...]  |

**summary对象字段说明**：

| 字段名       | 类型    | 说明                       | 示例值 |
| ------------ | ------- | -------------------------- | ------ |
| totalCount   | Integer | 总词数（原始单词列表长度） | 5      |
| correctCount | Integer | 正确数（完全匹配的单词）   | 3      |
| wrongCount   | Integer | 错误数（不匹配的单词）     | 1      |
| blankCount   | Integer | 空白数（用户未写的单词）   | 1      |

**details数组中对象字段说明**：

| 字段名       | 类型   | 说明                             | 示例值  |
| ------------ | ------ | -------------------------------- | ------- |
| originalWord | String | 原始单词                         | "apple" |
| userWord     | String | 用户写的单词                     | "apple" |
| result       | String | 比对结果类型："对"、"错"、"空白" | "对"    |

**返回示例**：

**成功比对示例（全部正确）**：
```json
{
  "code": 0,
  "message": "比对结果保存成功",
  "data": {
    "summary": {
      "totalCount": 3,
      "correctCount": 3,
      "wrongCount": 0,
      "blankCount": 0
    },
    "details": [
      {
        "originalWord": "apple",
        "userWord": "apple",
        "result": "对"
      },
      {
        "originalWord": "banana",
        "userWord": "banana",
        "result": "对"
      },
      {
        "originalWord": "orange",
        "userWord": "orange",
        "result": "对"
      }
    ]
  }
}
```

**成功比对示例（部分错误）**：
```json
{
  "code": 0,
  "message": "比对结果保存成功",
  "data": {
    "summary": {
      "totalCount": 4,
      "correctCount": 2,
      "wrongCount": 1,
      "blankCount": 1
    },
    "details": [
      {
        "originalWord": "apple",
        "userWord": "apple",
        "result": "对"
      },
      {
        "originalWord": "banana",
        "userWord": "banana",
        "result": "对"
      },
      {
        "originalWord": "orange",
        "userWord": "oragne",
        "result": "错"
      },
      {
        "originalWord": "grape",
        "userWord": "",
        "result": "空白"
      }
    ]
  }
}
```

**成功比对示例（中文单词）**：
```json
{
  "code": 0,
  "message": "比对结果保存成功",
  "data": {
    "summary": {
      "totalCount": 3,
      "correctCount": 2,
      "wrongCount": 1,
      "blankCount": 0
    },
    "details": [
      {
        "originalWord": "苹果",
        "userWord": "苹果",
        "result": "对"
      },
      {
        "originalWord": "香蕉",
        "userWord": "香蕉",
        "result": "对"
      },
      {
        "originalWord": "橙子",
        "userWord": "橘子",
        "result": "错"
      }
    ]
  }
}
```

**成功比对示例（混合内容）**：
```json
{
  "code": 0,
  "message": "比对结果保存成功",
  "data": {
    "summary": {
      "totalCount": 4,
      "correctCount": 3,
      "wrongCount": 1,
      "blankCount": 0
    },
    "details": [
      {
        "originalWord": "apple",
        "userWord": "apple",
        "result": "对"
      },
      {
        "originalWord": "苹果",
        "userWord": "苹果",
        "result": "对"
      },
      {
        "originalWord": "banana",
        "userWord": "banana",
        "result": "对"
      },
      {
        "originalWord": "香蕉",
        "userWord": "香焦",
        "result": "错"
      }
    ]
  }
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "relationId不能为空",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**原始内容不存在**：
```json
{
  "code": 500,
  "message": "系统错误：未找到对应的原始内容，relationId: 999",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "系统错误：数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                       |
| ------ | -------------- | ---------------------------------------------- |
| 400    | 参数错误       | relationId为空、userWords为空或参数格式错误    |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败       |
| 500    | 服务器内部错误 | 原始内容不存在、数据库异常或其他服务器处理异常 |

**业务逻辑说明**：

1. **身份验证**：验证JWT token并获取用户openId
2. **参数验证**：验证relationId和userWords参数不为空
3. **原始内容查询**：根据relationId查询memory_words_tts_record表的text_content字段
4. **内容分割**：将原始内容和用户输入内容按"\n"分割成单词列表，并过滤空字符串
5. **顺序比对**：按列表顺序逐一比对每个单词
6. **结果统计**：
   - 总词数：原始单词列表长度
   - 正确数：完全匹配的单词数量
   - 错误数：不匹配的单词数量
   - 空白数：用户未写的单词数量
7. **数据存储**：将比对结果以JSON格式保存到memory_words_compare_result表
8. **返回结果**：返回包含汇总和明细的完整比对结果

**比对规则**：

- **对**：原始单词与用户输入单词完全一致（区分大小写）
- **错**：原始单词与用户输入单词不一致
- **空白**：用户未输入对应位置的单词（空字符串或null）

**技术特性**：

- **精确比对**：区分大小写，确保比对结果的准确性
- **空值处理**：自动过滤空字符串，避免无效比对
- **顺序匹配**：严格按照列表顺序进行比对，保持位置对应关系
- **数据持久化**：比对结果自动保存到数据库，支持历史查询
- **并发安全**：支持多用户同时使用，数据隔离

**使用场景**：

- **单词默写练习**：用户根据语音提示默写单词，系统自动比对结果
- **拼写检查**：检查用户输入的单词拼写是否正确
- **学习进度跟踪**：记录用户的单词掌握情况，生成学习报告
- **错题分析**：识别用户常犯的错误，提供针对性的学习建议
- **成绩统计**：统计正确率、错误率等学习指标

**注意事项**：

1. **relationId有效性**：relationId必须存在于memory_words_user_and_record_relation表中
2. **内容格式**：原始内容和用户输入内容都按"\n"分割，确保格式一致
3. **比对精度**：比对区分大小写，确保结果的准确性
4. **数据存储**：比对结果会自动保存，支持后续查询和分析
5. **权限控制**：只有通过身份验证的用户才能使用此接口

**最佳实践**：

- 确保relationId对应的原始内容存在且格式正确
- 用户输入内容建议与原始内容格式保持一致
- 对于重要比对结果，建议在前端进行二次确认
- 定期清理过期的比对记录，优化数据库性能

### 分页查询录音历史批改结果

**接口描述**：分页查询当前登录用户（openId由后端自动获取）、指定relationId下的录音历史批改结果，按create_time倒序返回。

**请求URL**：/memoriseWords/compareResult/pageCompareResults

**请求方式**：GET

**请求头**：
```
Authorization: Bearer <JWT token>
```

**请求参数**：

| 参数名     | 类型 | 是否必填 | 说明                              | 示例值 |
| ---------- | ---- | -------- | --------------------------------- | ------ |
| relationId | Long | 是       | 录音与用户关系表主键 id，不能为空 | 123    |
| pageNum    | Int  | 否       | 页码，从1开始，默认1              | 1      |
| pageSize   | Int  | 否       | 每页大小，默认10                  | 10     |

> openId 由后端自动从token获取，无需前端传递。

**请求参数示例**：
```
GET /memoriseWords/compareResult/pageCompareResults?relationId=123&pageNum=1&pageSize=10
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型    | 说明                   |
| ------- | ------- | ---------------------- |
| code    | Integer | 状态码，0表示成功      |
| message | String  | 提示信息               |
| data    | Object  | 返回数据，分页结果对象 |

**data对象字段说明**：

| 字段名   | 类型    | 说明              | 示例值 |
| -------- | ------- | ----------------- | ------ |
| list     | Array   | 当前页数据列表    | 见下表 |
| total    | Long    | 总记录数          | 1      |
| pages    | Integer | 总页数            | 1      |
| pageNum  | Integer | 当前页码(从1开始) | 1      |
| pageSize | Integer | 每页大小          | 10     |

**list数组中对象字段说明**：

| 字段名        | 类型   | 说明                         | 示例值                       |
| ------------- | ------ | ---------------------------- | ---------------------------- |
| id            | Long   | 记录ID                       | 101                          |
| openId        | String | 微信 openId                  | oRrdQt0gOSjXXXXXXXXXXXXXXXXX |
| unionId       | String | 微信 unionId                 | unionidXXXXXXXXXXXXXXXX      |
| relationId    | Long   | 录音与用户关系表主键 id      | 123                          |
| originalWords | String | 原始单词内容                 | apple\nbanana\norange        |
| userWords     | String | 用户默写内容                 | apple\nbanana\noragne        |
| compareResult | String | 比对结果（压缩json格式）     | {"summary":...}              |
| createTime    | String | 创建时间 yyyy-MM-dd HH:mm:ss | 2024-08-27 10:00:00          |
| updateTime    | String | 更新时间 yyyy-MM-dd HH:mm:ss | 2024-08-27 10:00:00          |

**返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 101,
        "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX",
        "unionId": "unionidXXXXXXXXXXXXXXXX",
        "relationId": 123,
        "originalWords": "apple\nbanana\norange",
        "userWords": "apple\nbanana\noragne",
        "compareResult": "{\"summary\":{\"totalCount\":3,\"correctCount\":2,\"wrongCount\":1,\"blankCount\":0},\"details\":[{\"originalWord\":\"apple\",\"userWord\":\"apple\",\"result\":\"对\"},{\"originalWord\":\"banana\",\"userWord\":\"banana\",\"result\":\"对\"},{\"originalWord\":\"orange\",\"userWord\":\"oragne\",\"result\":\"错\"}]}"
        ,
        "createTime": "2024-08-27 10:00:00",
        "updateTime": "2024-08-27 10:00:00"
      }
    ],
    "total": 1,
    "pages": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**无记录时的返回示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 0,
    "pages": 0,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

**错误码说明**：

| 错误码 | 说明           |
| ------ | -------------- |
| 400    | 参数错误       |
| 401    | 未授权         |
| 500    | 服务器内部错误 |

### 根据id查询单条比对结果

**接口描述**：根据比对结果主键id查询单条比对结果详情，返回结构化的比对结果VO。该接口用于查看用户某次单词听写练习的详细比对结果，包括正确、错误、空白的统计信息和每个单词的具体比对情况。

**请求URL**：/memoriseWords/compareResult/getCompareResultById

**请求方式**：GET

**请求头**：
```
Authorization: Bearer <JWT token>
Content-Type: application/x-www-form-urlencoded
```

> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数**：

| 参数名 | 类型 | 是否必填 | 说明                                                | 示例值 |
| ------ | ---- | -------- | --------------------------------------------------- | ------ |
| id     | Long | 是       | 比对结果主键id，从memory_words_compare_result表获取 | 123    |

> 说明：id参数为memory_words_compare_result表的主键，可通过分页查询接口获取。

**请求参数示例**：
```
GET /memoriseWords/compareResult/getCompareResultById?id=123
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**返回参数**：

| 参数名  | 类型   | 说明                     | 示例值     |
| ------- | ------ | ------------------------ | ---------- |
| code    | Int    | 状态码，200表示成功      | 200        |
| message | String | 状态信息                 | "查询成功" |
| data    | Object | 返回数据，比对结果VO对象 | 见下方示例 |

**data字段说明**：

| 参数名  | 类型   | 说明                                         | 示例值     |
| ------- | ------ | -------------------------------------------- | ---------- |
| summary | Object | 汇总信息，包含总词数、正确数、错误数、空白数 | 见下方示例 |
| details | Array  | 明细信息，包含每个单词的比对结果             | 见下方示例 |

**summary字段说明**：

| 参数名       | 类型 | 说明                         | 示例值 |
| ------------ | ---- | ---------------------------- | ------ |
| totalCount   | Int  | 总词数，本次练习的总单词数量 | 5      |
| correctCount | Int  | 正确数，用户写对的单词数量   | 3      |
| wrongCount   | Int  | 错误数，用户写错的单词数量   | 1      |
| blankCount   | Int  | 空白数，用户未写的单词数量   | 1      |

**details字段说明**：

| 参数名       | 类型   | 说明                             | 示例值  |
| ------------ | ------ | -------------------------------- | ------- |
| originalWord | String | 原始单词，标准答案               | "apple" |
| userWord     | String | 用户写的单词，用户实际输入的内容 | "apple" |
| result       | String | 比对结果类型：对/错/空白         | "对"    |

> 说明：result字段的可能值包括：
> - "对"：用户写的单词与原始单词完全一致
> - "错"：用户写的单词与原始单词不一致
> - "空白"：用户未输入任何内容（userWord为空字符串）

**返回示例**：

**成功返回**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "summary": {
            "totalCount": 5,
            "correctCount": 3,
            "wrongCount": 1,
            "blankCount": 1
        },
        "details": [
            {
                "originalWord": "apple",
                "userWord": "apple",
                "result": "对"
            },
            {
                "originalWord": "banana",
                "userWord": "banana",
                "result": "对"
            },
            {
                "originalWord": "orange",
                "userWord": "orange",
                "result": "对"
            },
            {
                "originalWord": "grape",
                "userWord": "grape",
                "result": "错"
            },
            {
                "originalWord": "pear",
                "userWord": "",
                "result": "空白"
            }
        ]
    }
}
```

**失败返回示例**：

**参数错误**：
```json
{
    "code": 400,
    "message": "比对结果ID不能为空",
    "data": null
}
```

**未授权**：
```json
{
    "code": 401,
    "message": "无效的用户身份",
    "data": null
}
```

**数据不存在**：
```json
{
    "code": 500,
    "message": "查询失败: 未找到对应的比对结果，ID: 999",
    "data": null
}
```

**数据格式错误**：
```json
{
    "code": 500,
    "message": "查询失败: 比对结果数据解析失败，ID: 123",
    "data": null
}
```

**错误码说明**：

| 错误码 | 说明         | 可能原因                                 |
| ------ | ------------ | ---------------------------------------- |
| 400    | 参数错误     | id参数为空或格式不正确                   |
| 401    | 未授权       | token无效或已过期                        |
| 500    | 系统内部错误 | 数据库连接异常、数据不存在、数据格式错误 |

**注意事项**：
1. id参数必须为有效的比对结果记录ID，可通过分页查询接口获取
2. 返回的比对结果按单词顺序排列，与原始练习顺序一致
3. 汇总信息中的各数量之和等于总词数：totalCount = correctCount + wrongCount + blankCount
4. 用户未输入的单词，userWord字段为空字符串，result字段为"空白"
5. 比对结果区分大小写，用户输入必须与原始单词完全一致才算正确

---

## 通用接口

### 提交用户建议

**接口描述**：用户提交意见建议，帮助改进产品功能和用户体验

**请求URL**：/common/suggestions/submit

**请求方式**：POST

**请求参数**：

| 参数名     | 类型   | 是否必填 | 说明                                                   | 示例值               |
| ---------- | ------ | -------- | ------------------------------------------------------ | -------------------- |
| appId      | String | 否       | 小程序id，用于区分不同的小程序应用                     | wx1234567890abcd     |
| suggestion | String | 是       | 用户意见建议内容，长度不能超过1000个字符，不能为空字符 | "希望能增加更多功能" |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```
> 说明：请求头中需要携带有效的JWT令牌，用于验证用户身份。令牌中包含用户的openid等身份信息。

**请求参数示例**：
```json
{
  "appId": "wx1234567890abcdef",
  "suggestion": "希望能增加更多的食物热量数据库，特别是中式菜品的热量信息。另外建议增加运动消耗热量的计算功能。"
}
```

**返回参数**：

| 参数名  | 类型    | 说明                                 | 示例值         |
| ------- | ------- | ------------------------------------ | -------------- |
| code    | Integer | 状态码，0表示成功                    | 0              |
| message | String  | 提示信息                             | "建议提交成功" |
| data    | Long    | 返回数据，建议记录的ID，用于后续追踪 | 123            |

**返回示例**：
```json
{
  "code": 0,
  "message": "建议提交成功",
  "data": 123
}
```

**错误返回示例**：

**参数验证失败**：
```json
{
  "code": 400,
  "message": "用户意见不能为空",
  "data": null
}
```

**用户身份验证失败**：
```json
{
  "code": 401,
  "message": "无效的用户身份",
  "data": null
}
```

**服务器内部错误**：
```json
{
  "code": 500,
  "message": "建议提交失败: 数据库连接异常",
  "data": null
}
```

**错误码说明**：

| 错误码 | 说明           | 可能原因                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 参数错误       | 建议内容为空、超长或其他参数格式错误     |
| 401    | 未授权         | 令牌无效、已过期、缺失或用户身份验证失败 |
| 500    | 服务器内部错误 | 数据库异常、网络异常或其他服务器处理异常 |

**注意事项**：
1. 建议内容不能为空，且长度不能超过1000个字符
2. 系统会自动记录提交时间和用户身份信息
3. 建议内容会被保存到数据库中，用于产品改进参考
4. 返回的data字段包含建议记录的唯一ID，可用于后续追踪

### 语音转文字

**接口描述**：将上传的音频文件转换为文本，支持多种音频格式

**请求URL**：/common/speechToText/baidu

**请求方式**：POST

**请求参数**：

| 参数名 | 类型          | 是否必填 | 说明                                  |
| ------ | ------------- | -------- | ------------------------------------- |
| file   | MultipartFile | 是       | 要上传的音频文件                      |
| format | String        | 否       | 音频格式，支持值：wav、pcm，默认为pcm |

**请求头**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...（JWT token）
```

**请求示例**：
```
POST /common/speechToText/baidu
Content-Type: multipart/form-data
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...

file=@speech.wav&format=wav
```

**返回参数**：

| 参数名  | 类型    | 说明                       |
| ------- | ------- | -------------------------- |
| code    | Integer | 状态码，0表示成功          |
| message | String  | 提示信息                   |
| data    | String  | 返回数据，转换后的文本内容 |

**返回示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": "今天天气真不错，我们一起去公园吧。"
}
```

**错误码说明**：

| 错误码 | 说明                   |
| ------ | ---------------------- |
| 400    | 参数错误               |
| 401    | 未授权或无效的用户身份 |
| 500    | 服务器内部错误         |

**注意事项**：
1. 音频文件大小建议不超过10MB
2. 目前支持wav和pcm两种格式，其他格式可能无法正确识别
3. 返回的文本内容为识别的最佳结果，可能存在一定的误差

## 微信小程序服务器认证接口

### 服务器认证接口

**接口描述**：用于接收微信服务器的认证请求，配置服务器URL时使用

**请求URL**：/wx/portal/{appid}

**请求方式**：GET

**请求参数**：

| 参数名    | 类型   | 是否必填 | 说明            |
| --------- | ------ | -------- | --------------- |
| appid     | String | 是       | 微信小程序appid |
| signature | String | 是       | 微信签名        |
| timestamp | String | 是       | 时间戳          |
| nonce     | String | 是       | 随机数          |
| echostr   | String | 是       | 随机字符串      |

**请求参数示例**：
```
GET /wx/portal/wx36d336375b1c07b1?signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx
```

**返回参数**：

| 参数名 | 类型   | 说明                                        |
| ------ | ------ | ------------------------------------------- |
| 返回值 | String | 验证成功返回echostr，验证失败返回"非法请求" |

**返回示例**：
```
验证成功时返回echostr的值
验证失败时返回"非法请求"
```

**错误码说明**：

| 错误码 | 说明             |
| ------ | ---------------- |
| 400    | 请求参数非法     |
| 500    | 未找到对应的配置 |

### 消息接收接口

**接口描述**：用于接收微信服务器推送的消息

**请求URL**：/wx/portal/{appid}

**请求方式**：POST

**请求参数**：

| 参数名        | 类型   | 是否必填 | 说明            |
| ------------- | ------ | -------- | --------------- |
| appid         | String | 是       | 微信小程序appid |
| requestBody   | String | 否       | 请求体内容      |
| msg_signature | String | 否       | 消息签名        |
| encrypt_type  | String | 否       | 加密类型        |
| signature     | String | 否       | 微信签名        |
| timestamp     | String | 是       | 时间戳          |
| nonce         | String | 是       | 随机数          |

**请求参数示例**：
```
POST /wx/portal/wx36d336375b1c07b1?timestamp=xxx&nonce=xxx
Content-Type: application/xml

<xml>消息内容</xml>
```

**返回参数**：

| 参数名 | 类型   | 说明                  |
| ------ | ------ | --------------------- |
| 返回值 | String | 处理成功返回"success" |

**返回示例**：
```
success
```

**错误码说明**：

| 错误码 | 说明               |
| ------ | ------------------ |
| 400    | 未找到对应的配置   |
| 500    | 不可识别的加密类型 |

## 小程序媒体接口

### 上传媒体文件

**接口描述**：上传媒体文件到微信小程序

**请求URL**：/wx/media/{appid}/upload

**请求方式**：POST

**请求参数**：

| 参数名 | 类型          | 是否必填 | 说明            |
| ------ | ------------- | -------- | --------------- |
| appid  | String        | 是       | 微信小程序appid |
| type   | String        | 是       | 媒体文件类型    |
| file   | MultipartFile | 是       | 要上传的文件    |

**请求参数示例**：
```
POST /wx/media/wx36d336375b1c07b1/upload?type=image
Content-Type: multipart/form-data

file=@image.jpg
```

**返回参数**：

| 参数名     | 类型   | 说明             |
| ---------- | ------ | ---------------- |
| type       | String | 媒体文件类型     |
| media_id   | String | 媒体文件ID       |
| created_at | Long   | 媒体文件上传时间 |

**返回示例**：
```json
{
  "type": "image",
  "media_id": "MEDIA_ID",
  "created_at": 123456789
}
```

**错误码说明**：

| 错误码 | 说明             |
| ------ | ---------------- |
| 400    | 未找到对应的配置 |
| 500    | 上传文件失败     |

### 获取媒体文件

**接口描述**：获取微信小程序媒体文件

**请求URL**：/wx/media/{appid}/download/{mediaId}

**请求方式**：GET

**请求参数**：

| 参数名  | 类型   | 是否必填 | 说明            |
| ------- | ------ | -------- | --------------- |
| appid   | String | 是       | 微信小程序appid |
| mediaId | String | 是       | 媒体文件ID      |

**请求参数示例**：
```
GET /wx/media/wx36d336375b1c07b1/download/MEDIA_ID
```

**返回参数**：

| 参数名 | 类型   | 说明         |
| ------ | ------ | ------------ |
| 返回值 | 文件流 | 媒体文件内容 |

**返回示例**：
```
返回二进制文件流，浏览器会自动下载文件
```

**错误码说明**：

| 错误码 | 说明             |
| ------ | ---------------- |
| 400    | 未找到对应的配置 |
| 500    | 下载文件失败     |