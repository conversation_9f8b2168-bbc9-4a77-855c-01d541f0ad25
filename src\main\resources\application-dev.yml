# 开发环境配置
spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: chenrui
    password: <PERSON><PERSON><PERSON>@wuxi
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 10
      # 连接池最小空闲连接数
      minimum-idle: 5
      # 连接最大空闲时间
      idle-timeout: 600000
      # 连接最大生存时间
      max-lifetime: 1800000
      # 连接超时时间
      connection-timeout: 30000
  # Redis配置
  redis:
    host: ***********
    port: 16379
    password: Xiaoxuxu@yymf
    database: 0
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

# 日志配置
logging:
  level:
    com.github.binarywang.demo.wx.miniapp: debug
    org.springframework.web: debug

# Swagger配置
swagger:
  enabled: true  # 开发环境开启Swagger 