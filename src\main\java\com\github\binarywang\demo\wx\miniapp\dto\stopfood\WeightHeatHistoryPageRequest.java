package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 体重和热量历史记录分页查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "体重和热量历史记录分页查询请求参数")
public class WeightHeatHistoryPageRequest {

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    @Min(value = 1, message = "每页记录数必须大于0")
    @Max(value = 100, message = "每页记录数不能超过100条")
    @Schema(description = "每页记录数，最大100条", example = "10")
    private Integer pageSize = 10;
}