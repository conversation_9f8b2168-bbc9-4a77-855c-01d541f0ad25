package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 体重和热量历史记录响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "体重和热量历史记录信息")
public class WeightHeatHistoryVO {

    /**
     * 每日记录列表
     */
    @Schema(description = "每日记录列表")
    private List<DailyWeightHeatRecord> dailyRecords;

    /**
     * 每日体重和热量记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "每日体重和热量记录")
    public static class DailyWeightHeatRecord {

        /**
         * 记录日期
         */
        @Schema(description = "记录日期，格式：yyyy-MM-dd")
        private String recordDate;

        /**
         * 体重记录
         */
        @Schema(description = "体重记录")
        private WeightRecordVO weightRecord;

        /**
         * 摄入热量总和
         */
        @Schema(description = "摄入热量总和，单位：千卡(kcal)")
        private Integer totalIntakeCalories;

        /**
         * 消耗热量总和
         */
        @Schema(description = "消耗热量总和，单位：千卡(kcal)")
        private Integer totalConsumeCalories;

        /**
         * 净热量（摄入 - 消耗）
         */
        @Schema(description = "净热量（摄入 - 消耗），单位：千卡(kcal)")
        private Integer netCalories;
    }
}