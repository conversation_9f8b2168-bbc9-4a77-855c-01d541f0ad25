package com.github.binarywang.demo.wx.miniapp.service.impl.memorise_words;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.ArrayList;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsTtsRecordMapper;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsTtsRecordService;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 记忆单词-TTS语音合成记录Service实现类
 */
@Service
@Slf4j
public class MemoryWordsTtsRecordServiceImpl implements MemoryWordsTtsRecordService {

    @Autowired
    private MemoryWordsTtsRecordMapper memoryWordsTtsRecordMapper;

    @Override
    public Long saveRecord(MemoryWordsTtsRecord record) {
        try {
            memoryWordsTtsRecordMapper.insert(record);
            return record.getId();
        } catch (Exception e) {
            log.error("保存TTS记录失败", e);
            return null;
        }
    }

    @Override
    public boolean updateRecord(MemoryWordsTtsRecord record) {
        try {
            int rows = memoryWordsTtsRecordMapper.update(record);
            return rows > 0;
        } catch (Exception e) {
            log.error("更新TTS记录失败", e);
            return false;
        }
    }

    @Override
    public PageResult<MemoryWordsTtsRecord> getRecordsByOpenIdAndContent(String openId, String content, Integer page,
            Integer size) {
        try {
            // 查询总记录数
            long total = memoryWordsTtsRecordMapper.countByOpenIdAndContent(openId, content);

            // 如果没有记录，直接返回空结果
            if (total == 0) {
                // 构造空结果
                PageResult<MemoryWordsTtsRecord> emptyResult = new PageResult<>();
                emptyResult.setList(new ArrayList<>());
                emptyResult.setTotal(0L);
                emptyResult.setPages(0);
                emptyResult.setPageNum(page);
                emptyResult.setPageSize(size);
                return emptyResult;
            }

            // 计算总页数
            int pages = (int) Math.ceil((double) total / size);

            // 计算偏移量
            int offset = (page - 1) * size;

            // 直接使用数据库分页查询
            List<MemoryWordsTtsRecord> pageData = memoryWordsTtsRecordMapper.selectByOpenIdAndContent(
                    openId, content, offset, size);

            // 构造分页结果对象
            PageResult<MemoryWordsTtsRecord> result = new PageResult<>();
            result.setList(pageData);
            result.setTotal(total);
            result.setPages(pages);
            result.setPageNum(page);
            result.setPageSize(size);
            return result;
        } catch (Exception e) {
            log.error("查询TTS记录失败", e);
            PageResult<MemoryWordsTtsRecord> errorResult = new PageResult<>();
            errorResult.setList(new ArrayList<>());
            errorResult.setTotal(0L);
            errorResult.setPages(0);
            errorResult.setPageNum(page);
            errorResult.setPageSize(size);
            return errorResult;
        }
    }

    @Override
    public MemoryWordsTtsRecord getRecordByIdAndOpenId(Long id, String openId) {
        try {
            // 调用Mapper查询记录
            return memoryWordsTtsRecordMapper.selectByIdAndOpenId(id, openId);
        } catch (Exception e) {
            log.error("根据ID和openId查询TTS记录失败, id={}, openId={}", id, openId, e);
            return null;
        }
    }

    @Override
    public int deleteRecordsByIdsAndOpenId(List<Long> ids, String openId) {
        try {
            // 调用Mapper删除记录
            return memoryWordsTtsRecordMapper.deleteByIdsAndOpenId(ids, openId);
        } catch (Exception e) {
            log.error("根据ID集合和openId删除TTS记录失败, ids={}, openId={}", ids, openId, e);
            return 0;
        }
    }
}