package com.github.binarywang.demo.wx.miniapp.vo.memorise_words;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "课文标题信息")
public class MwTextbookTitleVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "单元排序")
    private Integer unitSort;

    @Schema(description = "单元信息")
    private String unitInfo;

    @Schema(description = "课文排序")
    private Integer lessonSort;

    @Schema(description = "课文标题")
    private String lessonTitle;
}