package com.github.binarywang.demo.wx.miniapp.utils;

import okhttp3.*;
import com.alibaba.fastjson2.JSONObject;
import com.github.binarywang.demo.wx.miniapp.utils.ali.AliOssUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import io.netty.util.internal.StringUtil;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * 火山引擎语音合成工具类
 */
public class VolcanoEngineTTSUtil {

    // 默认参数
    private static final String DEFAULT_TOKEN = "vvmjMh5cduHtf1WWrWADExsaY8Ave6Qz";
    private static final String DEFAULT_APPID = "4435661029";
    private static final String DEFAULT_CLUSTER = "volcano_tts";
    private static final String DEFAULT_UID = "your_uid";
    private static final String DEFAULT_ENCODING = "mp3";

    public static void main(String[] args) {
        // 示例调用：只传入必要的4个参数
        String voiceType = "BV040_streaming"; // 英式英语,亲切女声Anna
        float speedRatio = 1.0f; // 语速
        // String text = "<speak>This is a text with pauses. <break
        // time=\"2.5s\"></break> This pause is 2 seconds.</speak>";
        String text = "This is a text with pauses. This pause is 2 seconds.</speak>";
        String textType = "ssml"; // 文本类型
        Integer repeatCount = 2;
        Float pauseSeconds = 2.5f;

        // 调用合成语音方法
        synthesizeVoice(voiceType, speedRatio, text, textType, repeatCount, pauseSeconds);
    }

    /**
     * 合成语音并保存为MP3文件
     * 
     * @param voiceType  音色类型
     * @param speedRatio 语速比例
     * @param text       要合成的文本内容
     * @param textType   文本类型 (普通文本或SSML)
     * @return 语音文件在OSS上的URL
     */
    public static String synthesizeVoice(String voiceType, float speedRatio, String text, String textType,
            Integer repeatCount, Float pauseSeconds) {
        return synthesizeVoice(DEFAULT_TOKEN, DEFAULT_APPID, DEFAULT_CLUSTER, DEFAULT_UID,
                voiceType, DEFAULT_ENCODING, speedRatio, text, textType, repeatCount, pauseSeconds);
    }

    /**
     * 合成语音并保存为MP3文件，上传至OSS并返回URL（同步方法）
     * 
     * @param token      认证令牌
     * @param appid      应用ID
     * @param cluster    集群
     * @param uid        用户ID
     * @param voiceType  音色类型
     * @param encoding   编码类型
     * @param speedRatio 语速比例
     * @param text       要合成的文本内容
     * @param textType   文本类型 (普通文本或SSML)
     * @return String OSS文件URL
     */
    public static String synthesizeVoice(String token, String appid, String cluster, String uid,
            String voiceType, String encoding, float speedRatio,
            String text, String textType, Integer repeatCount, Float pauseSeconds) {

        String convertText = text;
        if ("ssml".equals(textType)) {
            convertText = buildSsmlText(text, repeatCount, pauseSeconds);
        }

        // 构建请求体参数
        RequestBodyData requestBodyData = new RequestBodyData();

        requestBodyData.app = new App();
        requestBodyData.app.appid = appid;
        requestBodyData.app.token = token;
        requestBodyData.app.cluster = cluster;

        requestBodyData.user = new User();
        requestBodyData.user.uid = uid;

        requestBodyData.audio = new Audio();
        requestBodyData.audio.voice_type = voiceType;
        requestBodyData.audio.encoding = encoding;
        requestBodyData.audio.speed_ratio = speedRatio;

        requestBodyData.request = new RequestParams();
        requestBodyData.request.reqid = UUID.randomUUID().toString();
        requestBodyData.request.text_type = textType;
        requestBodyData.request.text = convertText;
        requestBodyData.request.operation = "query";

        // 转换为JSON
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String jsonString = gson.toJson(requestBodyData);
        // System.out.println(jsonString);
        JSONObject json = JSONObject.parseObject(jsonString);
        // System.out.println(json);

        // 创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient();

        // 设置请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json.toJSONString());

        // 创建请求
        okhttp3.Request httpRequest = new okhttp3.Request.Builder()
                .url("https://openspeech.bytedance.com/api/v1/tts")
                .header("Authorization", "Bearer;".concat(token))
                .post(body)
                .build();

        try {
            // 发送同步请求
            Response response = client.newCall(httpRequest).execute();

            if (response.isSuccessful()) {
                String responseData = response.body().string();
                // 解析响应JSON
                JSONObject jsonResponse = JSONObject.parseObject(responseData);

                if (jsonResponse.getInteger("code") == 3000) {
                    // 提取base64编码的音频数据
                    String audioBase64 = jsonResponse.getString("data");

                    // 解码base64
                    byte[] audioBytes = Base64.getDecoder().decode(audioBase64);
                    String fileName = "speech_" + System.currentTimeMillis() + ".mp3";

                    // 创建输入流
                    InputStream inputStream = new ByteArrayInputStream(audioBytes);

                    // 直接使用内部方法上传到OSS
                    String ossUrl = AliOssUtils.uploadFileToOss(inputStream, "memory_words", fileName);
                    System.out.println("音频文件已上传到OSS: " + ossUrl);

                    // 返回URL
                    return ossUrl;
                } else {
                    String errorMsg = "语音合成失败: " + jsonResponse.getString("message");
                    throw new RuntimeException(errorMsg);
                }
            } else {
                String errorMsg = "请求失败，状态码: " + response.code();
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("语音合成过程中发生错误: " + e.getMessage());
        }
    }

    // 请求体内部的App类
    static class App {
        String appid;
        String token;
        String cluster;
    }

    // 请求体内部的User类
    static class User {
        String uid;
    }

    // 请求体内部的Audio类
    static class Audio {
        String voice_type;
        String encoding;
        float speed_ratio;
    }

    // 请求体内部的Request类（重命名为RequestParams）
    static class RequestParams {
        String reqid;
        String text;
        String text_type;
        String operation;
    }

    // 整体请求体类
    static class RequestBodyData {
        App app;
        User user;
        Audio audio;
        RequestParams request;
    }

    /**
     * <speak>This is a text with pauses. <break time=\"2.5s\"></break> This pause
     * is 2 seconds.</speak>
     * 
     * @param text
     * @param repeatCount
     * @param pauseSeconds
     * @return
     */
    private static String buildSsmlText(String text, Integer repeatCount, Float pauseSeconds) {
        StringBuilder ssmlBuilder = new StringBuilder();
        ssmlBuilder.append("<speak>");

        for (int i = 0; i < repeatCount; i++) {
            ssmlBuilder.append(text);
            if (i < repeatCount - 1) {
                ssmlBuilder.append("<break time=\"" + pauseSeconds + "s\"/>");
            }
        }

        ssmlBuilder.append("</speak>");

        return ssmlBuilder.toString();
    }

    /**
     * <speak>This is a text with pauses. <break time=\"2.5s\"></break> This pause
     * is 2 seconds.</speak>
     * 
     * @param textList
     * @param repeatCount
     * @param pauseSeconds
     * @return
     */
    private static String buildSsmlTextList(List<String> textList, Integer repeatCount, Float pauseSeconds) {
        StringBuilder ssmlBuilder = new StringBuilder();
        ssmlBuilder.append("<speak>");

        for (String text : textList) {
            for (int i = 0; i < repeatCount; i++) {
                ssmlBuilder.append(text);
                ssmlBuilder.append("<break time=\"" + pauseSeconds + "s\"/>");
            }
        }

        ssmlBuilder.append("</speak>");

        return ssmlBuilder.toString();
    }

    /**
     * 按照换行符分割文本，兼容Windows(\r\n)、Linux(\n)和macOS(\r)的换行符
     * 
     * @param text 要分割的文本
     * @return 分割后的字符串列表
     */
    public static List<String> splitTextByNewLine(String text) {
        if (text == null || text.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        // 使用正则表达式匹配所有可能的换行符
        String[] lines = text.split("\\r\\n|\\r|\\n");

        // 转换为List并返回
        return java.util.Arrays.asList(lines);
    }

    /**
     * 合成语音并保存为MP3文件
     * 
     * @param voiceType  音色类型
     * @param speedRatio 语速比例
     * @param text       要合成的文本内容
     * @param textType   文本类型 (普通文本或SSML)
     * @return 语音文件在OSS上的URL
     */
    public static String synthesizeVoiceOfTextList(String voiceType, float speedRatio, List<String> textList,
            String textType,
            Integer repeatCount, Float pauseSeconds) {
        return synthesizeVoiceOfTextList(DEFAULT_TOKEN, DEFAULT_APPID, DEFAULT_CLUSTER, DEFAULT_UID,
                voiceType, DEFAULT_ENCODING, speedRatio, textList, textType, repeatCount, pauseSeconds);
    }

    /**
     * 合成语音并保存为MP3文件，上传至OSS并返回URL（同步方法）
     * 
     * @param token      认证令牌
     * @param appid      应用ID
     * @param cluster    集群
     * @param uid        用户ID
     * @param voiceType  音色类型
     * @param encoding   编码类型
     * @param speedRatio 语速比例
     * @param text       要合成的文本内容
     * @param textType   文本类型 (普通文本或SSML)
     * @return String OSS文件URL
     */
    public static String synthesizeVoiceOfTextList(String token, String appid, String cluster, String uid,
            String voiceType, String encoding, float speedRatio,
            List<String> textList, String textType, Integer repeatCount, Float pauseSeconds) {

        String convertText = String.join(" ", textList);
        if ("ssml".equals(textType)) {
            convertText = buildSsmlTextList(textList, repeatCount, pauseSeconds);
        }

        // 构建请求体参数
        RequestBodyData requestBodyData = new RequestBodyData();

        requestBodyData.app = new App();
        requestBodyData.app.appid = appid;
        requestBodyData.app.token = token;
        requestBodyData.app.cluster = cluster;

        requestBodyData.user = new User();
        requestBodyData.user.uid = uid;

        requestBodyData.audio = new Audio();
        requestBodyData.audio.voice_type = voiceType;
        requestBodyData.audio.encoding = encoding;
        requestBodyData.audio.speed_ratio = speedRatio;

        requestBodyData.request = new RequestParams();
        requestBodyData.request.reqid = UUID.randomUUID().toString();
        requestBodyData.request.text_type = textType;
        requestBodyData.request.text = convertText;
        requestBodyData.request.operation = "query";

        // 转换为JSON
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String jsonString = gson.toJson(requestBodyData);
        // System.out.println(jsonString);
        JSONObject json = JSONObject.parseObject(jsonString);
        // System.out.println(json);

        // 创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient();

        // 设置请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json.toJSONString());

        // 创建请求
        okhttp3.Request httpRequest = new okhttp3.Request.Builder()
                .url("https://openspeech.bytedance.com/api/v1/tts")
                .header("Authorization", "Bearer;".concat(token))
                .post(body)
                .build();

        try {
            // 发送同步请求
            Response response = client.newCall(httpRequest).execute();

            if (response.isSuccessful()) {
                String responseData = response.body().string();
                // 解析响应JSON
                JSONObject jsonResponse = JSONObject.parseObject(responseData);

                if (jsonResponse.getInteger("code") == 3000) {
                    // 提取base64编码的音频数据
                    String audioBase64 = jsonResponse.getString("data");

                    // 解码base64
                    byte[] audioBytes = Base64.getDecoder().decode(audioBase64);
                    String fileName = "speech_" + System.currentTimeMillis() + ".mp3";

                    // 创建输入流
                    InputStream inputStream = new ByteArrayInputStream(audioBytes);

                    // 直接使用内部方法上传到OSS
                    String ossUrl = AliOssUtils.uploadFileToOss(inputStream, "memory_words", fileName);
                    System.out.println("音频文件已上传到OSS: " + ossUrl);

                    // 返回URL
                    return ossUrl;
                } else {
                    String errorMsg = "语音合成失败: " + jsonResponse.getString("message");
                    throw new RuntimeException(errorMsg);
                }
            } else {
                String errorMsg = "请求失败，状态码: " + response.code();
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("语音合成过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 遍历textList，如果某个text中含有非英文字符，则将该text去掉。如果text中含有英文的标点符号空格等字符等，可以不用去掉。
     * 
     * @param textList
     * @return
     */
    public static List<String> checkEnglishTextList(List<String> textList) {
        List<String> resultList = Lists.newArrayList();
        for (String text : textList) {
            if (!text.matches(".*[^\\x00-\\x7F].*")) {
                resultList.add(text);
            }
        }
        return resultList;
    }
}
