package com.github.binarywang.demo.wx.miniapp.controller.stopfood;

import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.WeightRecordRequest;

import com.github.binarywang.demo.wx.miniapp.dto.stopfood.WeightHeatHistoryPageRequest;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.WeightPageRequest;
import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDailyWeightRecord;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodDailyWeightRecordMapper;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodHeatRecordMapper;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.WeightRecordVO;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.WeightHeatHistoryVO;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 体重记录控制器
 */
@RestController
@Slf4j
@RequestMapping("/wx/weight-record/{appid}")
@Tag(name = "体重记录接口")
public class WeightRecordController {

    private final StopfoodDailyWeightRecordMapper weightRecordMapper;
    private final StopfoodHeatRecordMapper heatRecordMapper;
    private final JwtUtils jwtUtils;

    @Autowired
    public WeightRecordController(
            StopfoodDailyWeightRecordMapper weightRecordMapper,
            StopfoodHeatRecordMapper heatRecordMapper,
            JwtUtils jwtUtils) {
        this.weightRecordMapper = weightRecordMapper;
        this.heatRecordMapper = heatRecordMapper;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 用户体重每日记录
     */
    @Operation(summary = "用户体重每日记录", description = "新增或更新用户的每日体重记录，如果open_id+record_date已存在则更新，否则新增", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "体重记录请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = WeightRecordRequest.class))))
    @PostMapping("/saveWeightRecord")
    public ApiResponse<WeightRecordVO> saveWeightRecord(
            @PathVariable String appid,
            @Valid @RequestBody WeightRecordRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户指定日期是否已有体重记录
            StopfoodDailyWeightRecord existingRecord = weightRecordMapper.selectByOpenIdAndDate(openId,
                    request.getRecordDate());

            if (existingRecord != null) {
                // 更新已有记录
                existingRecord.setWeight(request.getWeight());
                int updateResult = weightRecordMapper.updateWeightRecord(existingRecord);

                if (updateResult > 0) {
                    // 重新查询更新后的记录
                    StopfoodDailyWeightRecord updatedRecord = weightRecordMapper.selectByOpenIdAndDate(openId,
                            request.getRecordDate());
                    log.info("用户[{}]更新体重记录: 日期[{}], 体重[{}]kg", openId, request.getRecordDate(), request.getWeight());
                    return ApiResponse.success("更新体重记录成功", WeightRecordVO.fromEntity(updatedRecord));
                } else {
                    return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "更新体重记录失败");
                }
            } else {
                // 创建新记录
                StopfoodDailyWeightRecord newRecord = StopfoodDailyWeightRecord.builder()
                        .openId(openId)
                        .recordDate(request.getRecordDate())
                        .weight(request.getWeight())
                        .build();

                int insertResult = weightRecordMapper.insertWeightRecord(newRecord);

                if (insertResult > 0) {
                    log.info("用户[{}]新增体重记录: 日期[{}], 体重[{}]kg", openId, request.getRecordDate(), request.getWeight());
                    return ApiResponse.success("新增体重记录成功", WeightRecordVO.fromEntity(newRecord));
                } else {
                    return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "新增体重记录失败");
                }
            }
        } catch (Exception e) {
            log.error("保存体重记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "保存体重记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户指定日期的体重记录
     */
    @Operation(summary = "获取体重记录", description = "根据用户openId和日期获取体重记录", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    @PostMapping("/getWeightRecord")
    public ApiResponse<WeightRecordVO> getWeightRecord(
            @PathVariable String appid,
            @Valid @RequestBody WeightRecordRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户指定日期的体重记录
            StopfoodDailyWeightRecord record = weightRecordMapper.selectByOpenIdAndDate(openId,
                    request.getRecordDate());

            if (record != null) {
                return ApiResponse.success("获取体重记录成功", WeightRecordVO.fromEntity(record));
            } else {
                return ApiResponse.success("该日期暂无体重记录", null);
            }
        } catch (Exception e) {
            log.error("获取体重记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取体重记录失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取用户体重和热量历史记录
     * 
     * @deprecated 此接口已废弃，请使用新的分页获取体重接口
     */
    @Deprecated
    @Operation(summary = "分页获取体重和热量历史记录（已废弃）", description = "此接口已废弃，请使用新的分页获取体重接口。分页获取用户的体重记录和热量统计", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    @PostMapping("/getWeightHeatHistoryByPage")
    public ApiResponse<PageResult<WeightHeatHistoryVO.DailyWeightHeatRecord>> getWeightHeatHistoryByPage(
            @PathVariable String appid,
            @Valid @RequestBody WeightHeatHistoryPageRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 设置默认值
            Integer pageNum = request.getPageNum();
            Integer pageSize = request.getPageSize();
            if (pageNum == null || pageNum <= 0) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 10;
            }

            // 查询用户体重记录总数
            Long totalCount = weightRecordMapper.countWeightRecords(openId);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            // 分页查询用户体重记录
            int offset = (pageNum - 1) * pageSize;
            List<StopfoodDailyWeightRecord> weightRecords = weightRecordMapper.selectWeightRecordsByPage(openId, offset,
                    pageSize);

            List<WeightHeatHistoryVO.DailyWeightHeatRecord> dailyRecords = new ArrayList<>();

            // 为每个体重记录查询对应日期的热量统计
            for (StopfoodDailyWeightRecord weightRecord : weightRecords) {
                String recordDate = weightRecord.getRecordDate();

                // 查询摄入热量总和
                Integer totalIntakeCalories = heatRecordMapper.getHeatSumByDateAndType(openId, recordDate, "摄入热量");
                if (totalIntakeCalories == null) {
                    totalIntakeCalories = 0;
                }

                // 查询消耗热量总和
                Integer totalConsumeCalories = heatRecordMapper.getHeatSumByDateAndType(openId, recordDate, "消耗热量");
                if (totalConsumeCalories == null) {
                    totalConsumeCalories = 0;
                }

                // 计算净热量
                Integer netCalories = totalIntakeCalories - totalConsumeCalories;

                WeightHeatHistoryVO.DailyWeightHeatRecord dailyRecord = WeightHeatHistoryVO.DailyWeightHeatRecord
                        .builder()
                        .recordDate(recordDate)
                        .weightRecord(WeightRecordVO.fromEntity(weightRecord))
                        .totalIntakeCalories(totalIntakeCalories)
                        .totalConsumeCalories(totalConsumeCalories)
                        .netCalories(netCalories)
                        .build();

                dailyRecords.add(dailyRecord);
            }

            // 构建分页结果
            PageResult<WeightHeatHistoryVO.DailyWeightHeatRecord> pageResult = new PageResult<>();
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotal(totalCount);
            pageResult.setPages(totalPages);
            pageResult.setList(dailyRecords);

            log.info("用户[{}]分页查询体重和热量记录，页码：{}，每页大小：{}，总记录数：{}", openId, pageNum, pageSize, totalCount);
            return ApiResponse.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页获取体重和热量历史记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取体重记录
     */
    @Operation(summary = "分页获取体重", description = "分页获取用户的体重记录，默认最近7天（包含今天），最多21天", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "体重分页查询请求", required = false, content = @Content(mediaType = "application/json", schema = @Schema(implementation = WeightPageRequest.class))))
    @PostMapping("/getWeightByPage")
    public ApiResponse<PageResult<WeightRecordVO>> getWeightByPage(
            @PathVariable String appid,
            @Valid @RequestBody(required = false) WeightPageRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 验证token并获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 如果请求体为空，创建默认请求
            if (request == null) {
                request = new WeightPageRequest();
            }

            // 获取分页参数，使用默认值
            Integer pageNum = request.getPageNum(); // 默认1
            Integer pageSize = request.getPageSize(); // 默认7

            // 验证参数范围
            if (pageSize > 21) {
                return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "每页记录数不能超过21天");
            }

            // 查询用户体重记录总数
            Long totalCount = weightRecordMapper.countWeightRecords(openId);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            // 分页查询用户体重记录
            int offset = (pageNum - 1) * pageSize;
            List<StopfoodDailyWeightRecord> weightRecords = weightRecordMapper.selectWeightRecordsByPage(openId, offset,
                    pageSize);

            // 转换为VO对象
            List<WeightRecordVO> weightRecordVOs = new ArrayList<>();
            for (StopfoodDailyWeightRecord record : weightRecords) {
                weightRecordVOs.add(WeightRecordVO.fromEntity(record));
            }

            // 构建分页结果
            PageResult<WeightRecordVO> pageResult = new PageResult<>();
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotal(totalCount);
            pageResult.setPages(totalPages);
            pageResult.setList(weightRecordVOs);

            log.info("用户[{}]分页查询体重记录，页码：{}，每页大小：{}，总记录数：{}", openId, pageNum, pageSize, totalCount);
            return ApiResponse.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页获取体重记录出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取体重记录失败: " + e.getMessage());
        }
    }
}