#!/bin/bash

# 停食项目服务管理脚本
# 用于在Alibaba Cloud Linux release 3系统上管理停食项目服务

# 配置信息
APP_NAME="stopfood"
JAR_NAME="weixin-java-miniapp-demo-1.0.0-SNAPSHOT.jar"
APP_DIR="/home/<USER>/cursor_test/19094_stopfood"
JAR_PATH="$APP_DIR/$JAR_NAME"
PID_FILE="$APP_DIR/application.pid"
LOG_FILE="$APP_DIR/application.log"
GC_LOG_FILE="$APP_DIR/gc.log"
HEAP_DUMP_PATH="$APP_DIR/heapdump.hprof"
ENV="dev" # 默认环境为开发环境
JVM_OPTS="" # JVM参数，将根据环境动态设置

# 日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 设置JVM参数
set_jvm_opts() {
    local base_opts="-XX:+UseG1GC \
                     -XX:+HeapDumpOnOutOfMemoryError \
                     -XX:HeapDumpPath=$HEAP_DUMP_PATH \
                     -XX:+PrintGCDetails \
                     -XX:+PrintGCTimeStamps \
                     -XX:+PrintGCApplicationStoppedTime \
                     -XX:+UseGCLogFileRotation \
                     -XX:NumberOfGCLogFiles=5 \
                     -XX:GCLogFileSize=10M \
                     -Xloggc:$GC_LOG_FILE \
                     -Djava.awt.headless=true \
                     -Dfile.encoding=UTF-8 \
                     -Duser.timezone=Asia/Shanghai"

    case "$ENV" in
        dev)
            JVM_OPTS="-Xms256m \
                      -Xmx512m \
                      -XX:MetaspaceSize=64m \
                      -XX:MaxMetaspaceSize=128m \
                      $base_opts"
            log "JVM配置: 开发环境 (堆内存: 256m-512m, 元空间: 64m-128m)"
            ;;
        test)
            JVM_OPTS="-Xms512m \
                      -Xmx1024m \
                      -XX:MetaspaceSize=128m \
                      -XX:MaxMetaspaceSize=256m \
                      $base_opts"
            log "JVM配置: 测试环境 (堆内存: 512m-1024m, 元空间: 128m-256m)"
            ;;
        prod)
            JVM_OPTS="-Xms1024m \
                      -Xmx2048m \
                      -XX:MetaspaceSize=256m \
                      -XX:MaxMetaspaceSize=512m \
                      -XX:+UseStringDeduplication \
                      -XX:G1HeapRegionSize=16m \
                      -XX:MaxGCPauseMillis=200 \
                      $base_opts"
            log "JVM配置: 生产环境 (堆内存: 1024m-2048m, 元空间: 256m-512m)"
            ;;
        *)
            log "警告: 未知环境 '$ENV'，使用开发环境配置"
            ENV="dev"
            set_jvm_opts
            return
            ;;
    esac

    log "JVM参数已设置完成"
}

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null; then
            log "服务状态: 运行中 [PID: $PID, 环境: $ENV]"
            return 0
        else
            log "服务状态: 已停止 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        log "服务状态: 已停止 (PID文件不存在)"
        return 1
    fi
}

# 查找服务进程ID
find_pid() {
    PID=$(ps -ef | grep "$JAR_NAME" | grep -v grep | awk '{print $2}')
    if [ -n "$PID" ]; then
        echo "$PID"
        return 0
    else
        return 1
    fi
}

# 启动服务
start() {
    log "正在启动 $APP_NAME 服务 [环境: $ENV]..."

    if check_status > /dev/null; then
        log "服务已经在运行中，无需重复启动"
        return 0
    fi

    # 设置JVM参数
    set_jvm_opts

    cd "$APP_DIR" || { log "无法切换到应用目录: $APP_DIR"; exit 1; }

    # 确保日志目录存在
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$(dirname "$GC_LOG_FILE")"
    mkdir -p "$(dirname "$HEAP_DUMP_PATH")"

    # 清理旧的GC日志文件（保留最近5个）
    if [ -f "$GC_LOG_FILE" ]; then
        find "$APP_DIR" -name "gc.log.*" -type f | sort | head -n -4 | xargs rm -f 2>/dev/null || true
    fi

    log "启动命令: java $JVM_OPTS -jar $JAR_PATH --spring.profiles.active=$ENV"

    nohup java $JVM_OPTS -jar "$JAR_PATH" --spring.profiles.active=$ENV > "$LOG_FILE" 2>&1 &

    NEW_PID=$!
    echo $NEW_PID > "$PID_FILE"

    log "服务启动中，PID: $NEW_PID，等待启动完成..."
    sleep 8

    if ps -p $NEW_PID > /dev/null; then
        log "服务启动成功 [PID: $NEW_PID, 环境: $ENV]"
        log "应用日志: $LOG_FILE"
        log "GC日志: $GC_LOG_FILE"
        log "堆转储路径: $HEAP_DUMP_PATH"

        # 显示JVM内存信息
        log "JVM内存配置:"
        echo "$JVM_OPTS" | grep -E "(Xms|Xmx|MetaspaceSize)" | sed 's/^/  /'

        return 0
    else
        log "服务启动失败，请检查日志文件: $LOG_FILE"
        log "最后几行日志:"
        tail -n 20 "$LOG_FILE" 2>/dev/null || log "无法读取日志文件"
        return 1
    fi
}

# 停止服务
stop() {
    log "正在停止 $APP_NAME 服务..."
    
    local pid=""
    
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
    else
        pid=$(find_pid)
        if [ -z "$pid" ]; then
            log "服务未在运行"
            return 0
        fi
    fi
    
    if [ -n "$pid" ]; then
        kill $pid
        log "已发送停止信号到进程 $pid"
        
        # 等待进程停止
        for i in {1..10}; do
            if ! ps -p $pid > /dev/null; then
                break
            fi
            log "等待服务停止...$i"
            sleep 1
        done
        
        # 如果进程仍在运行，强制终止
        if ps -p $pid > /dev/null; then
            log "服务未能正常停止，正在强制终止..."
            kill -9 $pid
            sleep 1
        fi
        
        if ! ps -p $pid > /dev/null; then
            log "服务已停止"
            rm -f "$PID_FILE"
            return 0
        else
            log "无法停止服务"
            return 1
        fi
    fi
}

# 重启服务
restart() {
    log "正在重启 $APP_NAME 服务 [环境: $ENV]..."
    stop
    sleep 2
    start
}

# 查看服务状态
status() {
    log "$APP_NAME 服务状态:"
    check_status

    if [ $? -eq 0 ]; then
        PID=$(cat "$PID_FILE")
        log "运行环境: $ENV"

        # 设置JVM参数以显示当前配置
        set_jvm_opts > /dev/null 2>&1

        log "JVM配置信息:"
        echo "$JVM_OPTS" | grep -E "(Xms|Xmx|MetaspaceSize)" | sed 's/^/  /'

        log "进程信息:"
        ps -f -p $PID

        log "端口监听状态:"
        netstat -tunlp 2>/dev/null | grep $PID || log "  无端口监听信息"

        log "内存使用情况:"
        ps -o pid,user,%mem,%cpu,vsz,rss,tty,stat,start,time,command -p $PID

        log "文件信息:"
        log "  应用日志: $LOG_FILE ($([ -f "$LOG_FILE" ] && echo "存在" || echo "不存在"))"
        log "  GC日志: $GC_LOG_FILE ($([ -f "$GC_LOG_FILE" ] && echo "存在" || echo "不存在"))"
        log "  PID文件: $PID_FILE ($([ -f "$PID_FILE" ] && echo "存在" || echo "不存在"))"

        # 显示最近的GC信息（如果GC日志存在）
        if [ -f "$GC_LOG_FILE" ]; then
            log "最近GC信息:"
            tail -n 5 "$GC_LOG_FILE" 2>/dev/null | sed 's/^/  /' || log "  无法读取GC日志"
        fi

        # 显示堆转储文件信息
        if [ -f "$HEAP_DUMP_PATH" ]; then
            log "堆转储文件: $HEAP_DUMP_PATH (大小: $(du -h "$HEAP_DUMP_PATH" 2>/dev/null | cut -f1))"
        fi
    fi
}

# 查看服务日志
logs() {
    local lines=${1:-100}
    if [ -f "$LOG_FILE" ]; then
        log "显示最后 $lines 行日志:"
        tail -n $lines "$LOG_FILE"
    else
        log "日志文件不存在: $LOG_FILE"
    fi
}

# 设置环境
set_env() {
    if [ -z "$1" ]; then
        log "当前环境: $ENV"
        set_jvm_opts > /dev/null 2>&1
        log "JVM内存配置:"
        echo "$JVM_OPTS" | grep -E "(Xms|Xmx|MetaspaceSize)" | sed 's/^/  /'
        return 0
    fi

    local old_env="$ENV"

    case "$1" in
        dev|test|prod)
            ENV="$1"
            log "环境已从 '$old_env' 切换到 '$ENV'"

            # 重新设置JVM参数
            set_jvm_opts

            log "环境切换完成，新的JVM配置已生效"
            log "注意: 需要重启服务才能使新配置生效"
            ;;
        *)
            log "无效的环境: $1, 有效值: dev, test, prod"
            log "当前环境保持不变: $ENV"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status|logs|env|help} [选项]"
    echo ""
    echo "命令:"
    echo "  start           启动服务（自动应用JVM优化配置）"
    echo "  stop            停止服务"
    echo "  restart         重启服务"
    echo "  status          查看服务状态（包含JVM内存信息）"
    echo "  logs [n]        查看最后n行日志，默认100行"
    echo "  env {dev|test|prod}  设置运行环境和JVM配置"
    echo "  help            显示帮助信息"
    echo ""
    echo "例子:"
    echo "  $0 env          查看当前环境和JVM配置"
    echo "  $0 env prod     设置环境为生产环境"
    echo "  $0 start        在当前环境下启动服务"
    echo "  $0 restart      在当前环境下重启服务"
    echo "  $0 status       查看详细的服务状态和内存信息"
    echo ""
    echo "支持的环境和JVM配置:"
    echo "  dev  - 开发环境 (堆内存: 256m-512m, 元空间: 64m-128m)"
    echo "  test - 测试环境 (堆内存: 512m-1024m, 元空间: 128m-256m)"
    echo "  prod - 生产环境 (堆内存: 1024m-2048m, 元空间: 256m-512m)"
    echo ""
    echo "JVM优化特性:"
    echo "  • G1垃圾回收器"
    echo "  • OOM时自动生成堆转储文件"
    echo "  • GC日志记录和轮转"
    echo "  • 环境差异化内存配置"
    echo "  • 生产环境额外的G1调优参数"
    echo ""
    echo "日志文件:"
    echo "  应用日志: $LOG_FILE"
    echo "  GC日志: $GC_LOG_FILE"
    echo "  堆转储: $HEAP_DUMP_PATH"
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs $2
        ;;
    env)
        set_env "$2"
        ;;
    help|*)
        show_help
        ;;
esac

exit 0 