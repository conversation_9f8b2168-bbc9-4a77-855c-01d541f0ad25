package com.github.binarywang.demo.wx.miniapp.utils.baidu;

import okhttp3.*;
import org.json.JSONObject;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 百度AccessToken统一管理工具类
 * 为所有百度服务提供统一的token获取和缓存机制
 */
public class BaiduAccessTokenManager {

    // 百度OAuth2.0 token获取接口
    private static final String TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";

    // 使用ConcurrentHashMap按API_KEY缓存不同服务的token，确保线程安全
    private static final ConcurrentHashMap<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();

    /**
     * Token信息封装类
     */
    private static class TokenInfo {
        private String token;
        private long expireTime;

        public TokenInfo(String token, long expireTime) {
            this.token = token;
            this.expireTime = expireTime;
        }

        public String getToken() {
            return token;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }

        public long getExpireTime() {
            return expireTime;
        }
    }

    /**
     * 获取百度服务的AccessToken
     * 使用缓存机制，避免频繁请求token
     * 
     * @param apiKey    百度API Key
     * @param secretKey 百度Secret Key
     * @return AccessToken，获取失败返回null
     * @throws IOException IO异常
     */
    public static synchronized String getAccessToken(String apiKey, String secretKey) throws IOException {
        // 使用apiKey作为缓存键，因为不同服务有不同的apiKey
        TokenInfo tokenInfo = tokenCache.get(apiKey);

        // 检查缓存的token是否存在且未过期
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            return tokenInfo.getToken();
        }

        // 缓存中没有有效token，重新获取
        String newToken = fetchAccessTokenFromBaidu(apiKey, secretKey);
        if (newToken != null) {
            // 计算过期时间：当前时间 + 1天
            long expireTime = System.currentTimeMillis() + (24 * 60 * 60 * 1000L);

            // 更新缓存
            tokenCache.put(apiKey, new TokenInfo(newToken, expireTime));

            return newToken;
        }

        return null;
    }

    /**
     * 从百度服务器获取AccessToken
     * 
     * @param apiKey    百度API Key
     * @param secretKey 百度Secret Key
     * @return AccessToken，获取失败返回null
     * @throws IOException IO异常
     */
    private static String fetchAccessTokenFromBaidu(String apiKey, String secretKey) throws IOException {
        try {
            MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
            RequestBody body = RequestBody.create(mediaType,
                    "grant_type=client_credentials&client_id=" + apiKey + "&client_secret=" + secretKey);

            Request request = new Request.Builder()
                    .url(TOKEN_URL)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .build();

            Response response = OkHttpClientUtil.getInstance().newCall(request).execute();

            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonResponse = new JSONObject(responseBody);

                if (jsonResponse.has("access_token")) {
                    return jsonResponse.getString("access_token");
                } else {
                    System.err.println("百度AccessToken响应中没有access_token字段: " + responseBody);
                }
            } else {
                System.err.println("获取百度AccessToken失败，HTTP状态码: " + response.code());
            }
        } catch (Exception e) {
            System.err.println("获取百度AccessToken时发生异常: " + e.getMessage());
            throw new IOException("获取百度AccessToken失败", e);
        }

        return null;
    }

    /**
     * 清除指定服务的token缓存
     * 
     * @param apiKey 百度API Key
     */
    public static void clearTokenCache(String apiKey) {
        tokenCache.remove(apiKey);
    }

    /**
     * 清除所有token缓存
     */
    public static void clearAllTokenCache() {
        tokenCache.clear();
    }

    /**
     * 获取缓存中的token数量（用于监控）
     * 
     * @return 缓存的token数量
     */
    public static int getCacheSize() {
        return tokenCache.size();
    }

    /**
     * 检查指定服务的token是否存在且有效
     * 
     * @param apiKey 百度API Key
     * @return true表示token存在且有效，false表示不存在或已过期
     */
    public static boolean isTokenValid(String apiKey) {
        TokenInfo tokenInfo = tokenCache.get(apiKey);
        return tokenInfo != null && !tokenInfo.isExpired();
    }
}