package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 营养素汇总请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "营养素汇总请求")
public class NutrientSummaryRequest {

    @Schema(description = "开始日期，格式：YYYY-MM-DD，默认为今天", example = "2024-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为YYYY-MM-DD")
    private String startDate;

    @Schema(description = "结束日期，格式：YYYY-MM-DD，默认为今天", example = "2024-01-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为YYYY-MM-DD")
    private String endDate;

    /**
     * 获取开始日期，如果为空则返回今天
     */
    public String getStartDate() {
        if (startDate == null || startDate.trim().isEmpty()) {
            return LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        return startDate;
    }

    /**
     * 获取结束日期，如果为空则返回今天
     */
    public String getEndDate() {
        if (endDate == null || endDate.trim().isEmpty()) {
            return LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        return endDate;
    }
}