package com.github.binarywang.demo.wx.miniapp.controller.common;

import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.common.UserSuggestionRequest;
import com.github.binarywang.demo.wx.miniapp.service.common.CommonUserSuggestionsService;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 通用-用户建议Controller
 */
@Slf4j
@RestController
@RequestMapping("/common/suggestions")
@Tag(name = "通用-用户建议")
public class CommonUserSuggestionsController {

    @Autowired
    private CommonUserSuggestionsService commonUserSuggestionsService;

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * 提交用户建议
     */
    @PostMapping("/submit")
    @Operation(summary = "提交用户建议", description = "用户提交意见建议", responses = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "提交成功", content = @Content(mediaType = "application/json"))
    })
    public ApiResponse<Long> submitSuggestion(
            @Valid @RequestBody UserSuggestionRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // unionId暂时设为null，因为JWT中没有存储unionId
            String unionId = null;

            log.info("用户[{}]提交建议：{}", openId, request.getSuggestion());

            // 保存用户建议
            Long suggestionId = commonUserSuggestionsService.saveSuggestion(
                    openId, unionId, request.getAppId(), request.getSuggestion());

            if (suggestionId != null) {
                return ApiResponse.success("建议提交成功", suggestionId);
            } else {
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "建议提交失败");
            }
        } catch (Exception e) {
            log.error("提交用户建议失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "建议提交失败: " + e.getMessage());
        }
    }
}