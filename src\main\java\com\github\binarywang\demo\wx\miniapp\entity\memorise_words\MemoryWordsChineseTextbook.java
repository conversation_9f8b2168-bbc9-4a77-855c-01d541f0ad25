package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 记忆单词-中文课本单词表实体类
 */
@Data
public class MemoryWordsChineseTextbook {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 学科
     */
    private String subject;

    /**
     * 教材版本
     */
    private String textbookVersion;

    /**
     * 年级
     */
    private Integer grade;

    /**
     * 学期
     */
    private Integer term;

    /**
     * 单元信息
     */
    private String unitInfo;

    /**
     * 课文标题
     */
    private String lessonTitle;

    /**
     * 词语
     */
    private String words;

    /**
     * 课文划词
     */
    private String lessonWords;

    /**
     * 是否删除，默认否
     */
    private String isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}