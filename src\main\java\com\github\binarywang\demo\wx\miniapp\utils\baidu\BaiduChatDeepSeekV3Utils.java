package com.github.binarywang.demo.wx.miniapp.utils.baidu;

import okhttp3.*;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.binarywang.demo.wx.miniapp.utils.OkHttpClientUtil;

@Slf4j
@Component
public class BaiduChatDeepSeekV3Utils {

    private static final String API_ID = "ALTAKcvqsoWQFSyI718Y9ykDPG";
    private static final String Authorization = "bce-v3/ALTAK-xZpV5rHzmayBC0YXT4egb/48663f430e193832fbe1da7f35ca42f6fc58df0e";

    public static final String BAIDU_CHAT_DEEPSEEK_V3 = "https://qianfan.baidubce.com/v2/chat/completions";

    public String chat(String prompt) throws IOException {
        Map<String, Object> contentMap = getContentMap(prompt);
        return call(contentMap);
    }

    public static String call(Map<String, Object> contentMap) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        String jsonString = JSONObject.toJSONString(contentMap);
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(BAIDU_CHAT_DEEPSEEK_V3)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer " + Authorization)
                .build();
        Response response = OkHttpClientUtil.getInstance().newCall(request).execute();
        String responseString = response.body().string();

        log.debug("百度大模型返回结果：{}", responseString);
        return responseString;
    }

    public static Map<String, Object> getContentMap(String wholePrompt) {
        Map<String, Object> contentMap = new HashMap<>();

        contentMap.put("model", "deepseek-v3");

        Map<String, Object> messagesMap = new HashMap<>();
        messagesMap.put("role", "user");
        messagesMap.put("content", wholePrompt);
        log.debug("发送到百度大模型的提示词：{}", wholePrompt);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(messagesMap);
        contentMap.put("messages", list);

        Map<String, Object> webSerachMap = new HashMap<>();
        webSerachMap.put("enable", false);
        webSerachMap.put("enable_citation", false);
        webSerachMap.put("enable_trace", false);
        contentMap.put("web_search", webSerachMap);

        return contentMap;
    }
}
