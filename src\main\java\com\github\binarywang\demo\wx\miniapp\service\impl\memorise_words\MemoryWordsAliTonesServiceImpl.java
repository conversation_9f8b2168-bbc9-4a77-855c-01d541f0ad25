package com.github.binarywang.demo.wx.miniapp.service.impl.memorise_words;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsAliTones;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsAliTonesMapper;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsAliTonesService;

import lombok.extern.slf4j.Slf4j;

/**
 * 记忆单词-阿里云音色表Service实现类
 */
@Service
@Slf4j
public class MemoryWordsAliTonesServiceImpl implements MemoryWordsAliTonesService {

    @Autowired
    private MemoryWordsAliTonesMapper memoryWordsAliTonesMapper;

    @Override
    public MemoryWordsAliTones getAliTonesInfo(String environment, String voiceKey, String speechRateKey) {
        try {
            log.info("查询阿里云音色信息，环境:{}，发音人:{}，语速:{}", environment, voiceKey, speechRateKey);
            return memoryWordsAliTonesMapper.getAliTonesInfo(environment, voiceKey, speechRateKey);
        } catch (Exception e) {
            log.error("查询阿里云音色信息失败", e);
            return null;
        }
    }

    @Override
    public String getVoiceValue(String environment, String voiceKey) {
        try {
            log.info("查询阿里云发音人值，环境:{}，发音人:{}", environment, voiceKey);
            String value = memoryWordsAliTonesMapper.getVoiceValue(environment, voiceKey);

            // 如果没有查到发音人值，则使用默认值
            if (value == null) {
                if ("中文".equals(environment)) {
                    value = "xiaoyun"; // 中文环境默认值
                } else {
                    value = "siqi"; // 英文环境默认值
                }
                log.info("未找到阿里云发音人值，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询阿里云发音人值失败", e);
            // 返回默认值
            return "中文".equals(environment) ? "xiaoyun" : "siqi";
        }
    }

    @Override
    public Integer getSpeechRateValue(String speechRateKey) {
        try {
            log.info("查询阿里云语速值，语速类型:{}", speechRateKey);
            Integer value = memoryWordsAliTonesMapper.getSpeechRateValue(speechRateKey);

            // 如果没有查到语速值，则使用默认值
            if (value == null) {
                if ("慢速".equals(speechRateKey)) {
                    value = -100;
                } else if ("快速".equals(speechRateKey)) {
                    value = 100;
                } else {
                    value = 0; // "中速"或其他未知值，使用默认值
                }
                log.info("未找到阿里云语速值，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询阿里云语速值失败", e);
            // 返回默认值
            if ("慢速".equals(speechRateKey)) {
                return -100;
            } else if ("快速".equals(speechRateKey)) {
                return 100;
            } else {
                return 0; // "中速"或其他未知值，使用默认值
            }
        }
    }

    @Override
    public Integer getSampleRate(String environment, String voiceKey, String speechRateKey) {
        try {
            log.info("查询阿里云采样率，环境:{}，发音人:{}，语速:{}", environment, voiceKey, speechRateKey);
            Integer value = memoryWordsAliTonesMapper.getSampleRate(environment, voiceKey, speechRateKey);

            // 如果没有查到采样率，则使用默认值
            if (value == null) {
                value = 16000; // 默认采样率
                log.info("未找到阿里云采样率，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询阿里云采样率失败", e);
            // 返回默认值
            return 16000;
        }
    }

    @Override
    public Integer getVolume(String environment, String voiceKey, String speechRateKey) {
        try {
            log.info("查询阿里云音量，环境:{}，发音人:{}，语速:{}", environment, voiceKey, speechRateKey);
            Integer value = memoryWordsAliTonesMapper.getVolume(environment, voiceKey, speechRateKey);

            // 如果没有查到音量，则使用默认值
            if (value == null) {
                value = 50; // 默认音量
                log.info("未找到阿里云音量，使用默认值:{}", value);
            }

            return value;
        } catch (Exception e) {
            log.error("查询阿里云音量失败", e);
            // 返回默认值
            return 50;
        }
    }
}