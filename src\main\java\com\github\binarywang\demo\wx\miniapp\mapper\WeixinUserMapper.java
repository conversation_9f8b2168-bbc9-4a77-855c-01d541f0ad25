package com.github.binarywang.demo.wx.miniapp.mapper;

import com.github.binarywang.demo.wx.miniapp.entity.WeixinUser;
import org.apache.ibatis.annotations.Mapper;

/**
 * 微信用户数据访问层
 */
@Mapper
public interface WeixinUserMapper {

    /**
     * 根据ID查询微信用户
     * 
     * @param id 用户ID
     * @return 微信用户信息
     */
    WeixinUser selectWeixinUserById(Long id);

    /**
     * 根据OpenID查询微信用户
     * 
     * @param openId 微信openId
     * @return 微信用户信息
     */
    WeixinUser selectWeixinUserByOpenId(String openId);

    /**
     * 根据手机号查询微信用户
     * 
     * @param phone 手机号
     * @return 微信用户信息
     */
    WeixinUser selectWeixinUserByPhone(String phone);

    /**
     * 新增微信用户
     * 
     * @param weixinUser 微信用户信息
     * @return 结果
     */
    int insertWeixinUser(WeixinUser weixinUser);

    /**
     * 修改微信用户
     * 
     * @param weixinUser 微信用户信息
     * @return 结果
     */
    int updateWeixinUser(WeixinUser weixinUser);
}