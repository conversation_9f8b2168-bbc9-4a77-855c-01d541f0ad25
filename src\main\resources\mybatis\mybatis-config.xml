<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- 全局参数 -->
    <settings>
        <!-- 使用驼峰命名法转换字段 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 默认使用默认的执行器 -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!-- 数据库超过30秒仍未响应则超时 -->
        <setting name="defaultStatementTimeout" value="30"/>
        <!-- 打印SQL日志 -->
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
</configuration> 