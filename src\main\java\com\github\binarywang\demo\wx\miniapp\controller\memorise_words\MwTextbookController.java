package com.github.binarywang.demo.wx.miniapp.controller.memorise_words;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.MwTextbookTitlesQueryDTO;
import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.MwTextbookWordsQueryDTO;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTextbook;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsTextbookMapper;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTextbookTitleVO;
import com.google.common.collect.Lists;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 记忆单词-课本单词 Controller
 */
@Slf4j
@RestController
@RequestMapping("/memoriseWords/textbook")
@Tag(name = "记忆单词-课本单词")
public class MwTextbookController {

    @Autowired
    private MemoryWordsTextbookMapper memoryWordsTextbookMapper;

    /**
     * 获取课本词语
     *
     * @param queryDTO 查询参数
     * @return 词语列表
     */
    @PostMapping("/words")
    @Operation(summary = "获取课本词语")
    public ApiResponse<List<String>> getWords(@RequestBody MwTextbookWordsQueryDTO queryDTO) {
        if (queryDTO.getIds() == null || queryDTO.getIds().isEmpty()) {
            return ApiResponse.success("课本单词ID列表不能为空", new ArrayList<>());
        }

        // 根据ID列表查询记录
        List<MemoryWordsTextbook> textbooks = memoryWordsTextbookMapper
                .selectMemoryWordsTextbookByIds(queryDTO.getIds());

        if (textbooks == null || textbooks.isEmpty()) {
            return ApiResponse.success("未找到匹配的课本词语", new ArrayList<>());
        }

        // 存储所有词语
        LinkedHashSet<String> allWords = new LinkedHashSet<>();

        // 遍历每个课本记录，提取词语
        for (MemoryWordsTextbook textbook : textbooks) {
            // 添加词语
            if (StringUtils.isNotEmpty(textbook.getWords())) {
                allWords.addAll(Arrays.asList(StringUtils.split(textbook.getWords(), "##")));
            }

            // 如果需要同时返回课文划词
            if (queryDTO.getIncludeLessonWords() && StringUtils.isNotEmpty(textbook.getLessonWords())) {
                allWords.addAll(Arrays.asList(StringUtils.split(textbook.getLessonWords(), "##")));
            }
        }

        return ApiResponse.success("获取课本词语成功", Lists.newArrayList(allWords));
    }

    /**
     * 获取课文标题列表
     *
     * @param queryDTO 查询参数
     * @return 课文标题列表
     */
    @PostMapping("/lessonTitles")
    @Operation(summary = "获取课文标题列表")
    public ApiResponse<List<MwTextbookTitleVO>> getLessonTitles(@RequestBody MwTextbookTitlesQueryDTO queryDTO) {
        // 根据条件查询课文标题列表
        List<MwTextbookTitleVO> titleList = memoryWordsTextbookMapper.selectLessonTitlesByCondition(
                queryDTO.getSubject(),
                queryDTO.getTextbookVersion(),
                queryDTO.getGrade(),
                queryDTO.getTerm());

        if (titleList == null || titleList.isEmpty()) {
            return ApiResponse.success("未找到匹配的课文标题", new ArrayList<>());
        }

        return ApiResponse.success("获取课文标题列表成功", titleList);
    }
}