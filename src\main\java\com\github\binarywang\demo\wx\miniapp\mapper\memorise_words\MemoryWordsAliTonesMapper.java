package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsAliTones;

/**
 * 记忆单词-阿里云音色表Mapper接口
 */
@Mapper
public interface MemoryWordsAliTonesMapper {

    /**
     * 根据环境、发音人Key和语速Key获取对应的阿里云音色信息
     * 
     * @param environment   环境，如"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 阿里云音色信息对象
     */
    @Select("SELECT * FROM memory_words_ali_tones WHERE environment = #{environment} " +
            "AND voice_key = #{voiceKey} " +
            "AND speech_rate_key = #{speechRateKey} " +
            "AND is_delete = '否' LIMIT 1")
    MemoryWordsAliTones getAliTonesInfo(@Param("environment") String environment,
            @Param("voiceKey") String voiceKey,
            @Param("speechRateKey") String speechRateKey);

    /**
     * 根据环境和发音人Key获取发音人值
     * 
     * @param environment 环境，如"中文"或"英文"
     * @param voiceKey    发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @return 发音人值
     */
    @Select("SELECT voice_value FROM memory_words_ali_tones WHERE environment = #{environment} " +
            "AND voice_key = #{voiceKey} " +
            "AND is_delete = '否' LIMIT 1")
    String getVoiceValue(@Param("environment") String environment,
            @Param("voiceKey") String voiceKey);

    /**
     * 根据语速Key获取语速值
     * 
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 语速值
     */
    @Select("SELECT speech_rate_value FROM memory_words_ali_tones WHERE speech_rate_key = #{speechRateKey} " +
            "AND is_delete = '否' LIMIT 1")
    Integer getSpeechRateValue(@Param("speechRateKey") String speechRateKey);

    /**
     * 根据环境、发音人Key和语速Key获取采样率
     * 
     * @param environment   环境，如"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 采样率
     */
    @Select("SELECT sample_rate FROM memory_words_ali_tones WHERE environment = #{environment} " +
            "AND voice_key = #{voiceKey} " +
            "AND speech_rate_key = #{speechRateKey} " +
            "AND is_delete = '否' LIMIT 1")
    Integer getSampleRate(@Param("environment") String environment,
            @Param("voiceKey") String voiceKey,
            @Param("speechRateKey") String speechRateKey);

    /**
     * 根据环境、发音人Key和语速Key获取音量
     * 
     * @param environment   环境，如"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 音量
     */
    @Select("SELECT volume FROM memory_words_ali_tones WHERE environment = #{environment} " +
            "AND voice_key = #{voiceKey} " +
            "AND speech_rate_key = #{speechRateKey} " +
            "AND is_delete = '否' LIMIT 1")
    Integer getVolume(@Param("environment") String environment,
            @Param("voiceKey") String voiceKey,
            @Param("speechRateKey") String speechRateKey);
}