<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsChineseTextbookMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsChineseTextbook" id="MemoryWordsChineseTextbookResult">
        <id property="id" column="id"/>
        <result property="subject" column="subject"/>
        <result property="textbookVersion" column="textbook_version"/>
        <result property="grade" column="grade"/>
        <result property="term" column="term"/>
        <result property="unitInfo" column="unit_info"/>
        <result property="lessonTitle" column="lesson_title"/>
        <result property="words" column="words"/>
        <result property="lessonWords" column="lesson_words"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectMemoryWordsChineseTextbookVo">
        select id, subject, textbook_version, grade, term, unit_info, lesson_title, words, lesson_words, is_delete, create_time, update_time 
        from memory_words_chinese_textbook
    </sql>

    <select id="selectMemoryWordsChineseTextbookList" resultMap="MemoryWordsChineseTextbookResult">
        <include refid="selectMemoryWordsChineseTextbookVo"/>
        where is_delete = '否'
    </select>

    <select id="selectMemoryWordsChineseTextbookById" parameterType="Long" resultMap="MemoryWordsChineseTextbookResult">
        <include refid="selectMemoryWordsChineseTextbookVo"/>
        where id = #{id} and is_delete = '否'
    </select>

    <insert id="insertMemoryWordsChineseTextbook" parameterType="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsChineseTextbook" useGeneratedKeys="true" keyProperty="id">
        insert into memory_words_chinese_textbook
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subject != null">subject,</if>
            <if test="textbookVersion != null">textbook_version,</if>
            <if test="grade != null">grade,</if>
            <if test="term != null">term,</if>
            <if test="unitInfo != null">unit_info,</if>
            <if test="lessonTitle != null">lesson_title,</if>
            <if test="words != null">words,</if>
            <if test="lessonWords != null">lesson_words,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subject != null">#{subject},</if>
            <if test="textbookVersion != null">#{textbookVersion},</if>
            <if test="grade != null">#{grade},</if>
            <if test="term != null">#{term},</if>
            <if test="unitInfo != null">#{unitInfo},</if>
            <if test="lessonTitle != null">#{lessonTitle},</if>
            <if test="words != null">#{words},</if>
            <if test="lessonWords != null">#{lessonWords},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMemoryWordsChineseTextbook" parameterType="com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsChineseTextbook">
        update memory_words_chinese_textbook
        <trim prefix="SET" suffixOverrides=",">
            <if test="subject != null">subject = #{subject},</if>
            <if test="textbookVersion != null">textbook_version = #{textbookVersion},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="term != null">term = #{term},</if>
            <if test="unitInfo != null">unit_info = #{unitInfo},</if>
            <if test="lessonTitle != null">lesson_title = #{lessonTitle},</if>
            <if test="words != null">words = #{words},</if>
            <if test="lessonWords != null">lesson_words = #{lessonWords},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteMemoryWordsChineseTextbookById" parameterType="Long">
        update memory_words_chinese_textbook set is_delete = '是', update_time = now() where id = #{id}
    </update>

</mapper> 