package com.github.binarywang.demo.wx.miniapp.service.memorise_words;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsAliTones;

/**
 * 记忆单词-阿里云音色表Service接口
 */
public interface MemoryWordsAliTonesService {

    /**
     * 根据环境、发音人Key和语速Key获取对应的阿里云音色和语速值
     * 
     * @param environment   环境，可选值为"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 阿里云音色信息对象，包含voice_value、speech_rate_value、sample_rate、volume等
     */
    MemoryWordsAliTones getAliTonesInfo(String environment, String voiceKey, String speechRateKey);

    /**
     * 获取发音人值
     * 
     * @param environment 环境，可选值为"中文"或"英文"
     * @param voiceKey    发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @return 发音人值，如"xiaoyun"
     */
    String getVoiceValue(String environment, String voiceKey);

    /**
     * 获取语速值
     * 
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 语速值，如-100，0，100
     */
    Integer getSpeechRateValue(String speechRateKey);

    /**
     * 获取采样率
     * 
     * @param environment   环境，可选值为"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 采样率，如8000，16000
     */
    Integer getSampleRate(String environment, String voiceKey, String speechRateKey);

    /**
     * 获取音量
     * 
     * @param environment   环境，可选值为"中文"或"英文"
     * @param voiceKey      发音人Key，如"普通话"、"英式英语"、"美式英语"
     * @param speechRateKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 音量，如50，70，100
     */
    Integer getVolume(String environment, String voiceKey, String speechRateKey);
}