package com.github.binarywang.demo.wx.miniapp.entity.stopfood;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户基础代谢实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopfoodBmr {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionid，可为空
     */
    private String unionId;

    /**
     * 用户的基础代谢，单位：千卡(kcal)
     */
    private Integer bmrValue;

    /**
     * 基础代谢的生成方式：公式计算、自定义
     */
    private String generationMode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}