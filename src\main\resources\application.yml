logging:
  level:
    org.springframework.web: info
    com.github.binarywang.demo.wx.miniapp: debug
    cn.binarywang.wx.miniapp: debug

server:
  port: 19094

spring:
  profiles:
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# Swagger配置
swagger:
  # 是否启用Swagger
  enabled: true
  # 基本信息
  info:
    title: 停食API文档
    description: 停食项目后端API接口文档
    version: 1.0.0
    contact:
      name: RunKitty
      email: <EMAIL>

# Knife4j配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
  packages-to-scan: com.github.binarywang.demo.wx.miniapp.controller
  paths-to-exclude: /swagger-resources/**
  default-produces-media-type: application/json

# Knife4j增强配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    swagger-model-name: 实体类列表

# MyBatis配置
mybatis:
  # 搜索指定包别名
  type-aliases-package: com.github.binarywang.demo.wx.miniapp.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath:mapper/**/*.xml
  # 加载全局的配置文件
  config-location: classpath:mybatis/mybatis-config.xml

# JWT配置
jwt:
  # JWT加解密使用的密钥
  secret: weixinxiaochengxuJwtSecret123456
  # JWT的超期限时间(60*60*24*7)
  expiration: 604800

# Token配置
token:
  # HTTP请求头中的token名称
  header: Authorization
  # token前缀
  prefix: Bearer

# 微信小程序配置
wx:
  miniapp:
    configs:
        - appid: wx36d336375b1c07b1
          secret: b4bd2ebe7f61b705a241d648c1539f01
          token: #微信小程序消息服务器配置的token
          aesKey: #微信小程序消息服务器配置的EncodingAESKey
          msgDataFormat: JSON
        - appid: wx8ab9f9a4b6f17c93
          secret: 631b7e491e490f22c16288fbf9426fd8
          token: #微信小程序消息服务器配置的token
          aesKey: #微信小程序消息服务器配置的EncodingAESKey
          msgDataFormat: JSON
        - appid: wx1b9f9d8f129e626a
          secret: a3216da3da1aaf3a8020d2ac48b6149a
          token: #微信小程序消息服务器配置的token
          aesKey: #微信小程序消息服务器配置的EncodingAESKey
          msgDataFormat: JSON
