<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodHeatRecordMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord" id="StopfoodHeatRecordResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="eventName" column="event_name"/>
        <result property="type" column="type"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
        <result property="heatValue" column="heat_value"/>
        <result property="carbohydrate" column="carbohydrate"/>
        <result property="protein" column="protein"/>
        <result property="fat" column="fat"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectStopfoodHeatRecordVo">
        select id, open_id, union_id, event_name, type, unit, quantity, heat_value, carbohydrate, protein, fat, create_time, update_time
        from stopfood_heat_record
    </sql>

    <insert id="insertHeatRecord" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord" useGeneratedKeys="true" keyProperty="id">
        insert into stopfood_heat_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="eventName != null">event_name,</if>
            <if test="type != null">type,</if>
            <if test="unit != null">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="heatValue != null">heat_value,</if>
            <if test="carbohydrate != null">carbohydrate,</if>
            <if test="protein != null">protein,</if>
            <if test="fat != null">fat,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="eventName != null">#{eventName},</if>
            <if test="type != null">#{type},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="heatValue != null">#{heatValue},</if>
            <if test="carbohydrate != null">#{carbohydrate},</if>
            <if test="protein != null">#{protein},</if>
            <if test="fat != null">#{fat},</if>
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP()
        </trim>
    </insert>
    
    <insert id="batchInsertHeatRecords" parameterType="java.util.List">
        insert into stopfood_heat_record (open_id, union_id, event_name, type, unit, quantity, heat_value, carbohydrate, protein, fat, create_time, update_time)
        values
        <foreach collection="list" item="record" separator=",">
            (
            #{record.openId},
            #{record.unionId},
            #{record.eventName},
            #{record.type},
            #{record.unit},
            #{record.quantity},
            #{record.heatValue},
            #{record.carbohydrate},
            #{record.protein},
            #{record.fat},
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP()
            )
        </foreach>
    </insert>

    <select id="selectHeatRecordsByOpenId" parameterType="String" resultMap="StopfoodHeatRecordResult">
        <include refid="selectStopfoodHeatRecordVo"/>
        where open_id = #{openId}
        order by create_time desc
    </select>

    <delete id="deleteHeatRecordsByIds">
        delete from stopfood_heat_record
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and open_id = #{openId}
    </delete>

    <!-- 更新热量记录详细信息 -->
    <update id="updateHeatRecord" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord">
        UPDATE stopfood_heat_record
        <set>
            <if test="eventName != null">event_name = #{eventName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="heatValue != null">heat_value = #{heatValue},</if>
            <if test="carbohydrate != null">carbohydrate = #{carbohydrate},</if>
            <if test="protein != null">protein = #{protein},</if>
            <if test="fat != null">fat = #{fat},</if>
            update_time = CURRENT_TIMESTAMP()
        </set>
        WHERE id = #{id} AND open_id = #{openId}
    </update>

</mapper> 