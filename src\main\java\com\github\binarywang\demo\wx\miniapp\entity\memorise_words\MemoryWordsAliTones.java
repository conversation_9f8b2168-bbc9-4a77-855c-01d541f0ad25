package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 记忆单词-阿里云音色表实体类
 */
@Data
public class MemoryWordsAliTones {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 环境，可选值为"中文"或"英文"
     */
    private String environment;

    /**
     * 采样率: 8000, 16000
     */
    private Integer sampleRate;

    /**
     * 发音人-key，如普通话、英式英语、美式英语
     */
    private String voiceKey;

    /**
     * 发音人-value
     */
    private String voiceValue;

    /**
     * 音量0-100
     */
    private Integer volume;

    /**
     * 语速-key，慢速，中速，快速
     */
    private String speechRateKey;

    /**
     * 语速-value
     */
    private Integer speechRateValue;

    /**
     * 语调-key
     */
    private Integer pitchRateKey;

    /**
     * 语调-value
     */
    private Integer pitchRateValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除，默认是否
     */
    private String isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}