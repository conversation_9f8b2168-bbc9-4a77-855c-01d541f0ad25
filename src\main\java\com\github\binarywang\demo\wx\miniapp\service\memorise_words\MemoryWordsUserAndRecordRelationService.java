package com.github.binarywang.demo.wx.miniapp.service.memorise_words;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsUserAndRecordRelation;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTtsRecordWithRelationVO;
import java.util.List;

/**
 * 记忆单词-用户与录音关系表Service接口
 */
public interface MemoryWordsUserAndRecordRelationService {

    /**
     * 保存用户与录音关系记录
     * 如果openId和recordId的绑定关系已存在，则不会重复插入，直接返回已存在记录的ID
     * 
     * @param relation 关系记录
     * @return 保存后的记录ID，如果已存在则返回已存在记录的ID
     */
    Long saveRelation(MemoryWordsUserAndRecordRelation relation);

    /**
     * 检查用户是否有权限访问指定录音
     * 
     * @param openId   用户openId
     * @param recordId 录音记录ID
     * @return 是否有权限
     */
    boolean hasPermission(String openId, Long recordId);

    /**
     * 根据openId和文本内容分页查询用户可访问的录音记录
     * 
     * @param openId  用户openId
     * @param content 文本内容关键词(可为null)
     * @param page    页码(从1开始)
     * @param size    每页大小
     * @return 分页结果
     */
    PageResult<MemoryWordsTtsRecord> getRecordsByOpenIdAndContent(String openId, String content, Integer page,
            Integer size);

    /**
     * 根据openId和文本内容分页查询用户可访问的录音记录（包含关系ID）
     * 
     * @param openId  用户openId
     * @param content 文本内容关键词(可为null)
     * @param page    页码(从1开始)
     * @param size    每页大小
     * @return 分页结果（包含关系ID）
     */
    PageResult<MwTtsRecordWithRelationVO> getRecordsWithRelationIdByOpenIdAndContent(String openId, String content,
            Integer page,
            Integer size);

    /**
     * 根据openId和recordId查询录音记录详情
     * 
     * @param openId   用户openId
     * @param recordId 录音记录ID
     * @return 录音记录实体，如果用户无权限或记录不存在则返回null
     */
    MemoryWordsTtsRecord getRecordByOpenIdAndRecordId(String openId, Long recordId);

    /**
     * 根据openId和recordId集合删除用户与录音的关系记录
     * 
     * @param openId    用户openId
     * @param recordIds 录音记录ID集合
     * @return 删除的记录数量
     */
    int deleteRelationsByOpenIdAndRecordIds(String openId, List<Long> recordIds);
}