package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 饮食模式视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "饮食模式信息")
public class DietaryModeVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 微信openid
     */
    @Schema(description = "微信openid")
    private String openId;

    /**
     * 微信unionid，可为空
     */
    @Schema(description = "微信unionid，可为空")
    private String unionId;

    /**
     * 饮食模式：快速减重、平稳减重、体重维持、自定义
     */
    @Schema(description = "饮食模式：快速减重、平稳减重、体重维持、自定义")
    private String mode;

    /**
     * 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）
     */
    @Schema(description = "系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）")
    private Double coefficient;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 从实体类转换为VO
     */
    public static DietaryModeVO fromEntity(StopfoodDietaryMode entity) {
        if (entity == null) {
            return null;
        }
        return DietaryModeVO.builder()
                .id(entity.getId())
                .openId(entity.getOpenId())
                .unionId(entity.getUnionId())
                .mode(entity.getMode())
                .coefficient(entity.getCoefficient())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }
}