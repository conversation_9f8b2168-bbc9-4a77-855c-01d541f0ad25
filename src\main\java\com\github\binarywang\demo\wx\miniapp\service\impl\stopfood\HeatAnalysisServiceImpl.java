package com.github.binarywang.demo.wx.miniapp.service.impl.stopfood;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodHeatRecordMapper;
import com.github.binarywang.demo.wx.miniapp.service.stopfood.HeatAnalysisService;
import com.github.binarywang.demo.wx.miniapp.utils.baidu.BaiduChatDeepSeekV3Utils;
import com.github.binarywang.demo.wx.miniapp.utils.stopfood.StopfoodPromptUtils;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.DailyHeatSummary;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.HeatAnalysisResult;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.io.IOException;

/**
 * 热量分析服务实现类
 */
@Slf4j
@Service
public class HeatAnalysisServiceImpl implements HeatAnalysisService {

        @Autowired
        private StopfoodHeatRecordMapper heatRecordMapper;

        private final BaiduChatDeepSeekV3Utils baiduChatDeepSeekV3Utils;

        public HeatAnalysisServiceImpl(BaiduChatDeepSeekV3Utils baiduChatDeepSeekV3Utils) {
                this.baiduChatDeepSeekV3Utils = baiduChatDeepSeekV3Utils;
        }

        @Deprecated
        @Override
        public List<HeatAnalysisResult> analyzeHeatFromText(String content) {
                log.info("开始分析文本中的热量信息: {}", content);

                List<HeatAnalysisResult> results = new ArrayList<>();
                Date now = new Date();

                try {
                        // 获取提示词
                        String prompt = StopfoodPromptUtils.getBatchCalorieCalculationPrompt(content);

                        // 调用大模型获取分析结果
                        String response = baiduChatDeepSeekV3Utils.chat(prompt);

                        // 解析结果
                        JSONArray events = StopfoodPromptUtils.parseBatchCalorieCalculationResponse(response);

                        if (events != null && !events.isEmpty()) {
                                for (int i = 0; i < events.size(); i++) {
                                        JSONObject event = events.getJSONObject(i);
                                        HeatAnalysisResult result = HeatAnalysisResult.builder()
                                                        .eventName(event.getString("事件名称"))
                                                        .type(event.getString("类型"))
                                                        .unit(event.getString("单位"))
                                                        .quantity(event.getInteger("数量"))
                                                        .heatValue(event.getInteger("热量（千卡）"))
                                                        .createTime(now)
                                                        .updateTime(now)
                                                        .build();
                                        results.add(result);
                                }
                        }
                } catch (Exception e) {
                        log.error("分析热量信息出错", e);
                        throw new RuntimeException("分析热量信息失败", e);
                }

                return results;
        }

        @Override
        public List<HeatAnalysisResult> analyzeHeatFromTextV2(String content) {
                List<HeatAnalysisResult> results = new ArrayList<>();

                try {
                        // 获取V2版本的提示词
                        String prompt = StopfoodPromptUtils.getBatchCalorieCalculationPromptV2(content);

                        // 调用大模型获取分析结果
                        String response = baiduChatDeepSeekV3Utils.chat(prompt);

                        // 使用V2版本的解析方法解析结果
                        JSONObject parsedResult = StopfoodPromptUtils.parseBatchCalorieCalculationResponseV2(response);

                        // 获取体重信息
                        Double weight = null;
                        if (parsedResult.containsKey("体重_kg")) {
                                weight = parsedResult.getDouble("体重_kg");
                        }

                        // 获取事件列表
                        JSONArray events = parsedResult.getJSONArray("事件");
                        if (events != null && !events.isEmpty()) {
                                for (int i = 0; i < events.size(); i++) {
                                        JSONObject event = events.getJSONObject(i);
                                        HeatAnalysisResult result = HeatAnalysisResult.builder()
                                                        .eventName(event.getString("事件名称"))
                                                        .type(event.getString("类型"))
                                                        .unit(event.getString("单位"))
                                                        .quantity(event.getInteger("数量"))
                                                        .heatValue(event.getInteger("热量（千卡）"))
                                                        .weight(weight) // 将体重信息添加到每个结果中
                                                        .build();

                                        // 仅当事件类型为"摄入热量"时，才设置营养素信息
                                        if ("摄入热量".equals(result.getType())) {
                                                result.setCarbohydrate(event.getInteger("碳水化合物_g"));
                                                result.setProtein(event.getInteger("蛋白质_g"));
                                                result.setFat(event.getInteger("脂肪_g"));
                                        }

                                        results.add(result);
                                }
                        } else if (weight != null && weight > 0) {
                                // 当没有热量事件但有体重信息时，创建一个只包含体重信息的结果
                                HeatAnalysisResult weightOnlyResult = HeatAnalysisResult.builder()
                                                .weight(weight)
                                                .build();
                                results.add(weightOnlyResult);
                                log.info("解析出纯体重信息：{}kg", weight);
                        }
                } catch (Exception e) {
                        log.error("分析热量信息出错", e);
                        throw new RuntimeException("分析热量信息失败", e);
                }

                return results;
        }

        @Deprecated
        @Override
        public List<HeatAnalysisResult> getTodayHeatRecords(String openId) {
                log.info("获取用户[{}]今日的热量记录（已废弃方法）", openId);

                // 从数据库获取用户今日的热量记录
                List<StopfoodHeatRecord> todayRecords = heatRecordMapper.getTodayHeatRecords(openId);

                // 将数据库记录转换为HeatAnalysisResult返回（不包含营养素信息）
                return todayRecords.stream()
                                .map(record -> HeatAnalysisResult.builder()
                                                .id(record.getId())
                                                .eventName(record.getEventName())
                                                .type(record.getType())
                                                .unit(record.getUnit())
                                                .quantity(record.getQuantity())
                                                .heatValue(record.getHeatValue())
                                                .createTime(record.getCreateTime())
                                                .updateTime(record.getUpdateTime())
                                                .build())
                                .collect(Collectors.toList());
        }

        @Override
        public List<HeatAnalysisResult> getTodayHeatRecordsAndNutrients(String openId) {
                log.info("获取用户[{}]今日的热量记录及营养素", openId);

                // 从数据库获取用户今日的热量记录
                List<StopfoodHeatRecord> todayRecords = heatRecordMapper.getTodayHeatRecords(openId);

                // 将数据库记录转换为HeatAnalysisResult返回（包含营养素信息）
                return todayRecords.stream()
                                .map(record -> {
                                        HeatAnalysisResult result = HeatAnalysisResult.builder()
                                                        .id(record.getId())
                                                        .eventName(record.getEventName())
                                                        .type(record.getType())
                                                        .unit(record.getUnit())
                                                        .quantity(record.getQuantity())
                                                        .heatValue(record.getHeatValue())
                                                        .carbohydrate(record.getCarbohydrate())
                                                        .protein(record.getProtein())
                                                        .fat(record.getFat())
                                                        .createTime(record.getCreateTime())
                                                        .updateTime(record.getUpdateTime())
                                                        .build();

                                        log.debug("记录[{}]：类型={}, 热量={}kcal, 碳水={}g, 蛋白质={}g, 脂肪={}g",
                                                record.getId(), record.getType(), record.getHeatValue(),
                                                record.getCarbohydrate(), record.getProtein(), record.getFat());

                                        return result;
                                })
                                .collect(Collectors.toList());
        }

        @Override
        public PageResult<DailyHeatSummary> getDailyHeatSummaryByPage(String openId, Integer pageNum,
                        Integer pageSize) {
                log.info("分页获取用户[{}]的每天热量记录，页码：{}，每页大小：{}", openId, pageNum, pageSize);

                // 1. 获取总的不重复日期数量
                Long totalDays = heatRecordMapper.countDistinctDays(openId);

                // 2. 分页获取日期列表
                List<String> dateList = heatRecordMapper.getDistinctDaysPaged(openId, (pageNum - 1) * pageSize,
                                pageSize);

                if (dateList == null || dateList.isEmpty()) {
                        // 没有记录，返回空结果
                        PageResult<DailyHeatSummary> emptyResult = new PageResult<>();
                        emptyResult.setPageNum(pageNum);
                        emptyResult.setPageSize(pageSize);
                        emptyResult.setTotal(0L);
                        emptyResult.setPages(0);
                        emptyResult.setList(new ArrayList<>());
                        return emptyResult;
                }

                // 3. 使用日期列表获取具体数据
                List<DailyHeatSummary> summaryList = new ArrayList<>();

                for (String dateStr : dateList) {
                        // 获取指定日期的热量记录
                        List<StopfoodHeatRecord> dailyRecords = heatRecordMapper.getHeatRecordsByDate(openId, dateStr);

                        if (dailyRecords != null && !dailyRecords.isEmpty()) {
                                DailyHeatSummary summary = new DailyHeatSummary();

                                try {
                                        // 设置日期
                                        summary.setFormattedDate(dateStr);

                                        // 计算总摄入热量和总消耗热量
                                        double totalIntake = 0.0;
                                        double totalConsumption = 0.0;

                                        // 创建明细列表
                                        List<DailyHeatSummary.HeatRecordDetail> details = new ArrayList<>();

                                        for (StopfoodHeatRecord record : dailyRecords) {
                                                // 创建明细
                                                DailyHeatSummary.HeatRecordDetail detail = new DailyHeatSummary.HeatRecordDetail();
                                                detail.setEventName(record.getEventName());
                                                detail.setType(record.getType());
                                                detail.setHeatValue(record.getHeatValue().doubleValue());
                                                detail.setRecordTime(record.getCreateTime());
                                                details.add(detail);

                                                // 计算总热量
                                                if ("摄入热量".equals(record.getType())) {
                                                        totalIntake += record.getHeatValue();
                                                } else if ("消耗热量".equals(record.getType())) {
                                                        totalConsumption += record.getHeatValue();
                                                }
                                        }

                                        // 设置总热量数据
                                        summary.setTotalIntake(totalIntake);
                                        summary.setTotalConsumption(totalConsumption);
                                        summary.setNetHeat(totalIntake - totalConsumption);
                                        summary.setDetails(details);

                                        summaryList.add(summary);
                                } catch (Exception e) {
                                        log.error("处理日期[{}]的热量记录出错", dateStr, e);
                                }
                        }
                }

                // 4. 创建并返回分页结果
                PageResult<DailyHeatSummary> result = new PageResult<>();
                result.setPageNum(pageNum);
                result.setPageSize(pageSize);
                result.setTotal(totalDays);

                // 计算总页数
                int pages = (int) Math.ceil((double) totalDays / pageSize);
                result.setPages(pages);

                // 设置数据列表
                result.setList(summaryList);

                return result;
        }

        @Deprecated
        @Override
        public Integer accurateCalculationOfHeat(String type, String eventName, String unit,
                        Integer quantity) {
                StringBuffer sb = new StringBuffer();
                if (type.contains("摄入热量")) {
                        // sb.append(StopfoodPromptUtils.getFoodCalorieIntakePrompt(type, eventName,
                        // unit, quantity));

                        sb.append(StopfoodPromptUtils.getBatchCalorieCalculationPrompt(
                                        "食物名称为" + eventName + "，单位为" + unit + "，数量为" + quantity));
                } else if (type.contains("消耗热量")) {
                        // sb.append(StopfoodPromptUtils.getSportsCalorieBurnPrompt(type, eventName,
                        // unit, quantity));
                        sb.append(StopfoodPromptUtils.getBatchCalorieCalculationPrompt(
                                        "运动名称为" + eventName + "，单位为" + unit + "，数量为" + quantity));
                } else {
                        throw new IllegalArgumentException("type参数错误");
                }

                String prompt = sb.toString();
                Map<String, Object> contentMap = BaiduChatDeepSeekV3Utils.getContentMap(prompt);
                String response = null;
                try {
                        // {"id":"as-ittgdi6tzp","object":"chat.completion","created":1749832411,"model":"deepseek-v3","choices":[{"index":0,"message":{"role":"assistant","content":"90"},"finish_reason":"stop","flag":0}],"usage":{"prompt_tokens":43,"completion_tokens":1,"total_tokens":44}}
                        response = BaiduChatDeepSeekV3Utils.call(contentMap);
                } catch (IOException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                }

                // // 使用工具类解析单条热量计算的返回结果
                // String content =
                // StopfoodPromptUtils.parseSingleCalorieCalculationResponse(response);
                // if (content != null && !content.trim().isEmpty()) {
                // try {
                // // 尝试将content转换为整数
                // return Integer.parseInt(content.trim());
                // } catch (NumberFormatException e) {
                // log.error("无法将响应内容转换为数字: {}", content);
                // return 0;
                // }
                // }
                // return 0;

                JSONArray jieguoArray = StopfoodPromptUtils.parseBatchCalorieCalculationResponse(response);
                if (jieguoArray != null && jieguoArray.size() > 0) {
                        JSONObject item = jieguoArray.getJSONObject(0);
                        return item.getInteger("热量（千卡）");
                }
                return 0;
        }

        

        @Override
        public HeatAnalysisResult accurateCalculateHeatAndNutrient(String type, String eventName, String unit,
                        Integer quantity) {
                List<HeatAnalysisResult> results = new ArrayList<>();

                try {
                        StringBuffer sb = new StringBuffer();
                        if (type.contains("摄入热量")) {
                                sb.append(StopfoodPromptUtils.getBatchCalorieCalculationPromptV2(
                                                "食物名称为" + eventName + "，单位为" + unit + "，数量为" + quantity));
                        } else if (type.contains("消耗热量")) {
                                sb.append(StopfoodPromptUtils.getBatchCalorieCalculationPromptV2(
                                                "运动名称为" + eventName + "，单位为" + unit + "，数量为" + quantity));
                        } else {
                                throw new IllegalArgumentException("type参数错误");
                        }

                        String prompt = sb.toString();
                        log.info("精确计算热量及营养素，发送提示词：{}", prompt);

                        // 调用大模型获取分析结果
                        String response = baiduChatDeepSeekV3Utils.chat(prompt);
                        log.info("精确计算热量及营养素，收到响应：{}", response);

                        // 使用V2版本的解析方法解析结果
                        JSONObject parsedResult = StopfoodPromptUtils.parseBatchCalorieCalculationResponseV2(response);

                // 获取事件列表
                JSONArray events = parsedResult.getJSONArray("事件");
                if (events != null && !events.isEmpty()) {
                        for (int i = 0; i < events.size(); i++) {
                                JSONObject event = events.getJSONObject(i);
                                HeatAnalysisResult result = HeatAnalysisResult.builder()
                                                .eventName(event.getString("事件名称"))
                                                .type(event.getString("类型"))
                                                .unit(event.getString("单位"))
                                                .quantity(event.getInteger("数量"))
                                                .heatValue(event.getInteger("热量（千卡）"))
                                                .build();

                                // 仅当事件类型为"摄入热量"时，才设置营养素信息
                                if ("摄入热量".equals(result.getType())) {
                                        result.setCarbohydrate(event.getInteger("碳水化合物_g"));
                                        result.setProtein(event.getInteger("蛋白质_g"));
                                        result.setFat(event.getInteger("脂肪_g"));
                                }

                                results.add(result);
                        }
                }

                if (CollectionUtils.isEmpty(results)) {
                        return HeatAnalysisResult.builder()
                                                .eventName(eventName)
                                                .type(type)
                                                .unit(unit)
                                                .quantity(quantity)
                                                .heatValue(0)
                                                .build();
                } else {
                        return results.get(0);
                }

        } catch (Exception e) {
                log.error("精确计算热量及营养素出错", e);
                throw new RuntimeException("精确计算热量及营养素失败", e);
        }
}
        
}