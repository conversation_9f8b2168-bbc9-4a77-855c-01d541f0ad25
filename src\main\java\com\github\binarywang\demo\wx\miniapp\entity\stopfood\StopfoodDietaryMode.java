package com.github.binarywang.demo.wx.miniapp.entity.stopfood;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户饮食模式实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopfoodDietaryMode {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionid，可为空
     */
    private String unionId;

    /**
     * 饮食模式：快速减重、平稳减重、体重维持、自定义
     */
    private String mode;

    /**
     * 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）
     */
    private Double coefficient;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}