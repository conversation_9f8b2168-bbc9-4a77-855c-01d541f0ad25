package com.github.binarywang.demo.wx.miniapp.mapper.stopfood;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户基础代谢数据库操作接口
 */
@Mapper
public interface StopfoodBmrMapper {

    /**
     * 根据openId查询基础代谢
     * 
     * @param openId 微信openId
     * @return 基础代谢对象，不存在则返回null
     */
    StopfoodBmr selectByOpenId(@Param("openId") String openId);

    /**
     * 插入新的基础代谢记录
     * 
     * @param bmr 基础代谢对象
     * @return 影响的行数
     */
    int insert(StopfoodBmr bmr);

    /**
     * 根据ID更新基础代谢
     * 
     * @param bmr 基础代谢对象
     * @return 影响的行数
     */
    int updateById(StopfoodBmr bmr);
}