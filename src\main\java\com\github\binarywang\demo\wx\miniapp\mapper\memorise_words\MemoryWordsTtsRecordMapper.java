package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;

/**
 * 记忆单词-TTS语音合成记录Mapper接口
 */
@Mapper
public interface MemoryWordsTtsRecordMapper {

        /**
         * 插入TTS记录
         * 
         * @param record TTS记录实体
         * @return 影响的行数
         */
        @Insert("INSERT INTO memory_words_tts_record(open_id, union_id, api_type, environment, voice_type, voice_value, "
                        +
                        "text_content, text_type, speed_key, speed_value, repeat_count, pause_seconds, status) " +
                        "VALUES(#{openId}, #{unionId}, #{apiType}, #{environment}, #{voiceType}, #{voiceValue}, " +
                        "#{textContent}, #{textType}, #{speedKey}, #{speedValue}, #{repeatCount}, #{pauseSeconds}, #{status})")
        @Options(useGeneratedKeys = true, keyProperty = "id")
        int insert(MemoryWordsTtsRecord record);

        /**
         * 更新TTS记录
         * 
         * @param record TTS记录实体
         * @return 影响的行数
         */
        @Update("UPDATE memory_words_tts_record SET audio_url = #{audioUrl}, audio_duration = #{audioDuration}, " +
                        "status = #{status}, error_msg = #{errorMsg} WHERE id = #{id}")
        int update(MemoryWordsTtsRecord record);

        /**
         * 根据openId和文本内容分页查询TTS记录
         * 
         * @param openId  用户openId
         * @param content 文本内容关键词(可为null)
         * @param offset  偏移量
         * @param limit   限制数量
         * @return 记录列表
         */
        @Select("<script>" +
                        "SELECT * FROM memory_words_tts_record " +
                        "WHERE open_id = #{openId} " +
                        "<if test='content != null and content != \"\"'>" +
                        "AND text_content LIKE CONCAT('%', #{content}, '%') " +
                        "</if>" +
                        "ORDER BY create_time DESC " +
                        "LIMIT #{limit} OFFSET #{offset}" +
                        "</script>")
        List<MemoryWordsTtsRecord> selectByOpenIdAndContent(@Param("openId") String openId,
                        @Param("content") String content,
                        @Param("offset") int offset,
                        @Param("limit") int limit);

        /**
         * 根据openId和文本内容查询总记录数
         * 
         * @param openId  用户openId
         * @param content 文本内容关键词(可为null)
         * @return 总记录数
         */
        @Select("<script>" +
                        "SELECT COUNT(1) FROM memory_words_tts_record " +
                        "WHERE open_id = #{openId} " +
                        "<if test='content != null and content != \"\"'>" +
                        "AND text_content LIKE CONCAT('%', #{content}, '%') " +
                        "</if>" +
                        "</script>")
        long countByOpenIdAndContent(@Param("openId") String openId, @Param("content") String content);

        /**
         * 根据ID和openId查询单条TTS记录
         * 
         * @param id     记录ID
         * @param openId 用户openId
         * @return TTS记录实体
         */
        @Select("SELECT * FROM memory_words_tts_record WHERE id = #{id} AND open_id = #{openId}")
        MemoryWordsTtsRecord selectByIdAndOpenId(@Param("id") Long id, @Param("openId") String openId);

        /**
         * 根据ID集合和openId删除TTS记录
         * 
         * @param ids    记录ID集合
         * @param openId 用户openId
         * @return 删除的记录数量
         */
        @Delete("<script>" +
                        "DELETE FROM memory_words_tts_record " +
                        "WHERE open_id = #{openId} " +
                        "AND id IN " +
                        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
                        "#{id}" +
                        "</foreach>" +
                        "</script>")
        int deleteByIdsAndOpenId(@Param("ids") List<Long> ids, @Param("openId") String openId);
}