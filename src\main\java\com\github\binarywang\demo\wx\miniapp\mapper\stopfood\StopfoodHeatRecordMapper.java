package com.github.binarywang.demo.wx.miniapp.mapper.stopfood;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodHeatRecord;

import java.util.List;
import java.util.Map;

/**
 * 热量记录表Mapper接口
 */
@Mapper
public interface StopfoodHeatRecordMapper {

        /**
         * 新增热量记录
         *
         * @param record 热量记录信息
         * @return 结果
         */
        int insertHeatRecord(StopfoodHeatRecord record);

        /**
         * 批量新增热量记录
         *
         * @param records 热量记录信息列表
         * @return 结果
         */
        int batchInsertHeatRecords(List<StopfoodHeatRecord> records);

        /**
         * 根据openid查询热量记录
         *
         * @param openId 微信openid
         * @return 热量记录列表
         */
        List<StopfoodHeatRecord> selectHeatRecordsByOpenId(@Param("openId") String openId);

        /**
         * 统计用户每日记录总数
         */
        @Select("SELECT COUNT(DISTINCT DATE(create_time)) FROM stopfood_heat_record WHERE open_id = #{openId}")
        Long countDailyRecords(@Param("openId") String openId);

        /**
         * 分页查询用户每日热量记录
         */
        @Select("SELECT * FROM stopfood_heat_record WHERE open_id = #{openId} ORDER BY create_time DESC LIMIT #{offset}, #{pageSize}")
        List<StopfoodHeatRecord> getDailyHeatRecords(@Param("openId") String openId, @Param("offset") Integer offset,
                        @Param("pageSize") Integer pageSize);

        /**
         * 查询用户今日的热量记录
         *
         * @param openId 微信openid
         * @return 今日热量记录列表
         */
        @Select("SELECT * FROM stopfood_heat_record WHERE open_id = #{openId} AND DATE(create_time) = CURDATE() ORDER BY create_time DESC")
        List<StopfoodHeatRecord> getTodayHeatRecords(@Param("openId") String openId);

        /**
         * 统计用户不重复的记录日期总数
         *
         * @param openId 微信openid
         * @return 不重复的日期总数
         */
        @Select("SELECT COUNT(DISTINCT DATE(create_time)) FROM stopfood_heat_record WHERE open_id = #{openId}")
        Long countDistinctDays(@Param("openId") String openId);

        /**
         * 分页获取用户不重复的记录日期
         *
         * @param openId   微信openid
         * @param offset   分页偏移量
         * @param pageSize 每页记录数
         * @return 不重复的日期列表（格式：yyyy-MM-dd）
         */
        @Select("SELECT DISTINCT DATE_FORMAT(create_time, '%Y-%m-%d') as date_str FROM stopfood_heat_record WHERE open_id = #{openId} ORDER BY date_str DESC LIMIT #{offset}, #{pageSize}")
        List<String> getDistinctDaysPaged(@Param("openId") String openId, @Param("offset") Integer offset,
                        @Param("pageSize") Integer pageSize);

        /**
         * 根据日期获取用户热量记录
         *
         * @param openId  微信openid
         * @param dateStr 日期字符串，格式：yyyy-MM-dd
         * @return 指定日期的热量记录列表
         */
        @Select("SELECT * FROM stopfood_heat_record WHERE open_id = #{openId} AND DATE(create_time) = #{dateStr} ORDER BY create_time DESC")
        List<StopfoodHeatRecord> getHeatRecordsByDate(@Param("openId") String openId, @Param("dateStr") String dateStr);

        /**
         * 根据ID集合删除热量记录
         *
         * @param ids    ID集合
         * @param openId 微信openid（用于验证权限）
         * @return 删除的记录数
         */
        int deleteHeatRecordsByIds(@Param("ids") List<Long> ids, @Param("openId") String openId);

        /**
         * 根据ID查询热量记录
         *
         * @param id 记录ID
         * @return 热量记录
         */
        @Select("SELECT * FROM stopfood_heat_record WHERE id = #{id}")
        StopfoodHeatRecord selectHeatRecordById(@Param("id") Long id);

        /**
         * 更新热量记录详细信息
         *
         * @param record 热量记录信息
         * @return 更新的记录数
         */
        int updateHeatRecord(StopfoodHeatRecord record);

        /**
         * 查询用户今日的微信运动步数记录
         *
         * @param openId 微信openid
         * @return 今日微信运动步数记录列表，按创建时间倒序排列
         */
        @Select("SELECT * FROM stopfood_heat_record WHERE open_id = #{openId} AND DATE(create_time) = CURDATE() AND event_name = '自动-微信运动步数' AND type = '消耗热量' AND unit = '步' ORDER BY create_time DESC")
        List<StopfoodHeatRecord> getTodayWeixinStepsRecords(@Param("openId") String openId);

        /**
         * 查询用户指定日期的热量统计
         *
         * @param openId     微信openid
         * @param recordDate 记录日期，格式：yyyy-MM-dd
         * @param type       类型：摄入热量、消耗热量
         * @return 热量总和
         */
        @Select("SELECT COALESCE(SUM(heat_value), 0) FROM stopfood_heat_record WHERE open_id = #{openId} AND DATE(create_time) = #{recordDate} AND type = #{type}")
        Integer getHeatSumByDateAndType(@Param("openId") String openId, @Param("recordDate") String recordDate,
                        @Param("type") String type);

        /**
         * 查询用户指定日期范围内摄入营养素的总和
         *
         * @param openId    微信openid
         * @param startDate 开始日期，格式：yyyy-MM-dd
         * @param endDate   结束日期，格式：yyyy-MM-dd
         * @return 营养素总和，包含碳水化合物、蛋白质、脂肪
         */
        @Select("SELECT " +
                        "COALESCE(SUM(carbohydrate), 0) as totalCarbohydrate, " +
                        "COALESCE(SUM(protein), 0) as totalProtein, " +
                        "COALESCE(SUM(fat), 0) as totalFat " +
                        "FROM stopfood_heat_record " +
                        "WHERE open_id = #{openId} " +
                        "AND DATE(create_time) BETWEEN #{startDate} AND #{endDate} " +
                        "AND type = '摄入热量'")
        Map<String, Object> getNutrientSumByDateRange(@Param("openId") String openId,
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 分页查询每日营养素汇总
         *
         * @param openId 微信openid
         * @param offset 偏移量
         * @param limit  查询条数
         * @return 每日营养素汇总列表
         */
        @Select("SELECT " +
                        "DATE(create_time) as recordDate, " +
                        "COALESCE(SUM(CASE WHEN type = '摄入热量' THEN heat_value ELSE 0 END), 0) as totalIntakeCalories, "
                        +
                        "COALESCE(SUM(CASE WHEN type = '消耗热量' THEN heat_value ELSE 0 END), 0) as totalConsumeCalories, "
                        +
                        "COALESCE(SUM(CASE WHEN type = '摄入热量' THEN carbohydrate ELSE 0 END), 0) as totalCarbohydrate, "
                        +
                        "COALESCE(SUM(CASE WHEN type = '摄入热量' THEN protein ELSE 0 END), 0) as totalProtein, " +
                        "COALESCE(SUM(CASE WHEN type = '摄入热量' THEN fat ELSE 0 END), 0) as totalFat " +
                        "FROM stopfood_heat_record " +
                        "WHERE open_id = #{openId} " +
                        "GROUP BY DATE(create_time) " +
                        "ORDER BY DATE(create_time) DESC " +
                        "LIMIT #{offset}, #{limit}")
        List<Map<String, Object>> getDailyNutrientSummaryByPage(@Param("openId") String openId,
                        @Param("offset") int offset, @Param("limit") int limit);

        /**
         * 查询用户每日营养素汇总记录总数
         *
         * @param openId 微信openid
         * @return 记录总数
         */
        @Select("SELECT COUNT(DISTINCT DATE(create_time)) FROM stopfood_heat_record WHERE open_id = #{openId}")
        int getDailyNutrientSummaryCount(@Param("openId") String openId);
}