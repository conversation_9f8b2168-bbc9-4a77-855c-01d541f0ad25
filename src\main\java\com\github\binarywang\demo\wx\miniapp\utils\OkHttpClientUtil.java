package com.github.binarywang.demo.wx.miniapp.utils;

import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;

public class OkHttpClientUtil {

    private static OkHttpClient okHttpClient = null;

    private OkHttpClientUtil() {
        // 私有构造方法，防止外部实例化
    }

    public static OkHttpClient getInstance() {
        if (okHttpClient == null) {
            synchronized (OkHttpClientUtil.class) {
                if (okHttpClient == null) {
                    // 设置超时时间、拦截器等配置
                    okHttpClient = new OkHttpClient.Builder()
                            .connectTimeout(60, TimeUnit.SECONDS) // 连接超时时间
                            .readTimeout(60, TimeUnit.SECONDS) // 读取超时时间
                            .writeTimeout(60, TimeUnit.SECONDS) // 写入超时时间
                            // .addInterceptor(new HttpLoggingInterceptor()) // 添加拦截器，例如用于日志打印
                            .build();
                }
            }
        }
        return okHttpClient;
    }
}
