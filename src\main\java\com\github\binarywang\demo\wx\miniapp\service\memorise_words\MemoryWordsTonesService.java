package com.github.binarywang.demo.wx.miniapp.service.memorise_words;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTones;

/**
 * 记忆单词-音色表Service接口
 */
public interface MemoryWordsTonesService {

    /**
     * 根据环境、音色类型Key和语速Key获取对应的音色和语速值
     * 
     * @param environment   环境，可选值为"中文"或"英文"
     * @param voiceTypeKey  音色类型Key，如"普通话"、"英式英语"、"美式英语"
     * @param speedRatioKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 音色信息对象，包含voice_type_value和speed_ratio_value
     */
    MemoryWordsTones getTonesInfo(String environment, String voiceTypeKey, String speedRatioKey);

    /**
     * 获取音色类型值
     * 
     * @param environment  环境，可选值为"中文"或"英文"
     * @param voiceTypeKey 音色类型Key，如"普通话"、"英式英语"、"美式英语"
     * @return 音色类型值，如"BV700_streaming"
     */
    String getVoiceTypeValue(String environment, String voiceTypeKey);

    /**
     * 获取语速值
     * 
     * @param speedRatioKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 语速值，如0.8，1.0，1.5
     */
    Float getSpeedRatioValue(String speedRatioKey);
}