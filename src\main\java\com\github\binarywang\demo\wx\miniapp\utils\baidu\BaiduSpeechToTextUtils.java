package com.github.binarywang.demo.wx.miniapp.utils.baidu;

import okhttp3.*;
import org.json.JSONObject;

import cn.hutool.core.lang.Tuple;

import java.util.concurrent.TimeUnit;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.net.URLEncoder;

public class BaiduSpeechToTextUtils {
    public static final String API_KEY = "hVyXps99zmiDJtJkRUiYm1q9";
    public static final String SECRET_KEY = "CujQSWbA8hL8xHxJJsEdkk8LmA14y5CD";

    public static final String SPEECH_TO_TEXT_API = "https://vop.baidu.com/pro_api";

    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder()
            .readTimeout(300, TimeUnit.SECONDS)
            .build();

    /**
     * 将语音转换为文字
     *
     * @param base64String 语音文件的base64编码
     * @param len          语音文件的长度
     * @return 转换结果的JSON字符串
     * @throws IOException IO异常
     */
    public static String soundToWord(String format, String cuid, String base64String, int len) throws IOException {
        // return soundToWord("pcm", 16000, 1, "oVfdr0GiHPnBL2NYFsX4943T1ECJGasr",
        // 80001, base64String, len,
        // getAccessToken());
        return soundToWord(format, 16000, 1, cuid, 80001, base64String, len,
                getAccessToken());
    }

    /**
     * 将语音转换为文字（带参数版本）
     *
     * @param format  音频格式
     * @param rate    采样率
     * @param channel 声道数
     * @param cuid    用户唯一标识
     * @param devPid  语言模型ID
     * @param speech  语音文件的base64编码
     * @param len     语音文件的长度
     * @param token   访问令牌
     * @return 转换结果的JSON字符串
     * @throws IOException IO异常
     */
    public static String soundToWord(String format, int rate, int channel, String cuid,
            int devPid, String speech, int len, String token) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");

        JSONObject params = new JSONObject();
        params.put("format", format);
        params.put("rate", rate);
        params.put("channel", channel);
        params.put("cuid", cuid);
        params.put("dev_pid", devPid);
        params.put("speech", speech);
        params.put("len", len);
        params.put("token", token);

        RequestBody body = RequestBody.create(mediaType, params.toString());
        Request request = new Request.Builder()
                .url(SPEECH_TO_TEXT_API)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String jsonString = response.body().string();

        // {"corpus_no":"7508799889293979918","err_msg":"success.","err_no":0,"result":["今天早上吃了一杯牛奶，两个包子，中午吃了一碗鸡蛋盖饭，然后走了一小时。"],"sn":"493582499351748278711"}
        JSONObject jsonObject = new JSONObject(jsonString);
        return jsonObject.getJSONArray("result").getString(0);
    }

    /**
     * 获取文件base64编码
     *
     * @param path      文件路径
     * @param urlEncode 如果Content-Type是application/x-www-form-urlencoded时,传true
     * @return 包含base64编码信息和文件长度的元组
     * @throws IOException IO异常
     */
    public static Tuple getFileContentAsBase64(String path, boolean urlEncode) throws IOException {
        byte[] b = Files.readAllBytes(Paths.get(path));
        String base64 = Base64.getEncoder().encodeToString(b);
        if (urlEncode) {
            base64 = URLEncoder.encode(base64, "utf-8");
        }
        return new Tuple(base64, b.length);
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     * 使用统一的百度AccessToken管理器
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getAccessToken() throws IOException {
        return BaiduAccessTokenManager.getAccessToken(API_KEY, SECRET_KEY);
    }

    // 使用示例
    public static void main(String[] args) throws IOException {
        Tuple tuple = getFileContentAsBase64("D:\\temp\\2025.05.13_sound_to_word\\luvvoice.com-20250513-geaoHA.wav",
                false);
        String result = soundToWord("wav", "oVfdr0GiHPnBL2NYFsX4943T1ECJGasr", tuple.get(0), tuple.get(1));
        System.out.println(result);
    }
}
