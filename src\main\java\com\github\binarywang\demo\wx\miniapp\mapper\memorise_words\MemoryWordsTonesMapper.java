package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTones;

/**
 * 记忆单词-音色表Mapper接口
 */
@Mapper
public interface MemoryWordsTonesMapper {

    /**
     * 根据环境、音色类型Key和语速Key获取对应的音色信息
     * 
     * @param environment   环境，如"中文"或"英文"
     * @param voiceTypeKey  音色类型Key，如"普通话"、"英式英语"、"美式英语"
     * @param speedRatioKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 音色信息对象
     */
    @Select("SELECT * FROM memory_words_tones WHERE environment = #{environment} " +
            "AND voice_type_key = #{voiceTypeKey} " +
            "AND speed_ratio_key = #{speedRatioKey} " +
            "AND is_delete = '否' LIMIT 1")
    MemoryWordsTones getTonesInfo(@Param("environment") String environment,
            @Param("voiceTypeKey") String voiceTypeKey,
            @Param("speedRatioKey") String speedRatioKey);

    /**
     * 根据环境和音色类型Key获取音色类型值
     * 
     * @param environment  环境，如"中文"或"英文"
     * @param voiceTypeKey 音色类型Key，如"普通话"、"英式英语"、"美式英语"
     * @return 音色类型值
     */
    @Select("SELECT voice_type_value FROM memory_words_tones WHERE environment = #{environment} " +
            "AND voice_type_key = #{voiceTypeKey} " +
            "AND is_delete = '否' LIMIT 1")
    String getVoiceTypeValue(@Param("environment") String environment,
            @Param("voiceTypeKey") String voiceTypeKey);

    /**
     * 根据语速Key获取语速值
     * 
     * @param speedRatioKey 语速Key，如"慢速"、"中速"、"快速"
     * @return 语速值
     */
    @Select("SELECT speed_ratio_value FROM memory_words_tones WHERE speed_ratio_key = #{speedRatioKey} " +
            "AND is_delete = '否' LIMIT 1")
    Float getSpeedRatioValue(@Param("speedRatioKey") String speedRatioKey);
}