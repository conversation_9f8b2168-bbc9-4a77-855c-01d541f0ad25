user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # 开启gzip压缩
    gzip  on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;

    # 停食项目反向代理配置
    server {
        listen       80;
        listen       [::]:80;
        server_name  example.com;  # 请修改为实际的域名
        
        # 强制HTTPS
        # return 301 https://$host$request_uri;
        
        # 访问日志
        access_log  /var/log/nginx/stopfood.access.log  main;
        
        # stopfood项目代理
        location /stopfood/ {
            proxy_pass http://localhost:19094/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 设置超时时间
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
        
        # 其他静态资源路径配置
        location /stopfood/static/ {
            alias /home/<USER>/cursor_test/19094_stopfood/static/;
            expires 30d;
        }
    }
    
    # HTTPS配置(可选)
    server {
        listen       443 ssl http2;
        listen       [::]:443 ssl http2;
        server_name  example.com;  # 请修改为实际的域名
        
        # SSL证书配置
        ssl_certificate "/etc/pki/nginx/server.crt";
        ssl_certificate_key "/etc/pki/nginx/private/server.key";
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 10m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;
        
        # 访问日志
        access_log  /var/log/nginx/stopfood.ssl.access.log  main;
        
        # stopfood项目代理
        location /stopfood/ {
            proxy_pass http://localhost:19094/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 设置超时时间
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
        
        # 其他静态资源路径配置
        location /stopfood/static/ {
            alias /home/<USER>/cursor_test/19094_stopfood/static/;
            expires 30d;
        }
    }
} 