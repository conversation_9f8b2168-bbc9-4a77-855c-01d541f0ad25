# 数据库表结构文档

## 目录

- [数据库表结构文档](#数据库表结构文档)
  - [目录](#目录)
  - [表结构更新记录](#表结构更新记录)
  - [表结构详情](#表结构详情)
    - [common\_user\_suggestions（通用-用户建议表）](#common_user_suggestions通用-用户建议表)
    - [字段说明](#字段说明)
    - [索引说明](#索引说明)
    - [weixin\_user（微信用户信息表）](#weixin_user微信用户信息表)
    - [字段说明](#字段说明-1)
    - [索引说明](#索引说明-1)
    - [memory\_words\_textbook（记忆单词-课本单词表）](#memory_words_textbook记忆单词-课本单词表)
    - [字段说明](#字段说明-2)
    - [索引说明](#索引说明-2)
    - [memory\_words\_tts\_record（记忆单词-TTS语音合成记录表）](#memory_words_tts_record记忆单词-tts语音合成记录表)
    - [字段说明](#字段说明-3)
    - [索引说明](#索引说明-3)
    - [memory\_words\_tones（记忆单词-音色表）](#memory_words_tones记忆单词-音色表)
    - [字段说明](#字段说明-4)
    - [索引说明](#索引说明-4)
    - [memory\_words\_ali\_tones（记忆单词-阿里云音色表）](#memory_words_ali_tones记忆单词-阿里云音色表)
    - [字段说明](#字段说明-5)
    - [索引说明](#索引说明-5)
    - [memory\_words\_user\_and\_record\_relation（记忆单词-用户与录音关系表）](#memory_words_user_and_record_relation记忆单词-用户与录音关系表)
    - [字段说明](#字段说明-6)
    - [索引说明](#索引说明-6)
    - [memory\_words\_compare\_result（记忆单词-比对结果表）](#memory_words_compare_result记忆单词-比对结果表)
    - [字段说明](#字段说明-7)
    - [索引说明](#索引说明-7)
    - [stopfood\_heat\_record（热量记录表）](#stopfood_heat_record热量记录表)
    - [字段说明](#字段说明-8)
    - [索引说明](#索引说明-8)
    - [stopfood\_dietary\_mode（用户饮食模式表）](#stopfood_dietary_mode用户饮食模式表)
    - [字段说明](#字段说明-9)
    - [索引说明](#索引说明-9)
    - [stopfood\_bmr（用户基础代谢表）](#stopfood_bmr用户基础代谢表)
    - [字段说明](#字段说明-10)
    - [索引说明](#索引说明-10)
    - [stopfood\_daily\_weight\_record（用户每日体重记录表）](#stopfood_daily_weight_record用户每日体重记录表)
    - [字段说明](#字段说明-11)
    - [索引说明](#索引说明-11)

## 表结构更新记录

| 日期       | 表名                                  | 操作类型 | 描述                                                                          |
| ---------- | ------------------------------------- | -------- | ----------------------------------------------------------------------------- |
| 2024-05-22 | memory_words_textbook                 | 创建     | 创建记忆单词-课本单词表                                                       |
| 2023-10-16 | weixin_user                           | 创建     | 创建微信用户信息表                                                            |
| 2024-08-26 | weixin_user                           | 修改     | 增加生日、身高、体重字段                                                      |
| 2024-08-27 | weixin_user                           | 修改     | 将birth_date字段从date类型改为datetime类型                                    |
| 2024-08-27 | stopfood_heat_record                  | 创建     | 创建热量记录表                                                                |
| 2024-08-27 | stopfood_heat_record                  | 修改     | 修改表结构定义，移除字符集和排序规则设置                                      |
| 2024-08-30 | memory_words_tts_record               | 创建     | 创建记忆单词-TTS语音合成记录表                                                |
| 2024-09-06 | memory_words_tones                    | 创建     | 创建记忆单词-音色表                                                           |
| 2024-12-19 | memory_words_ali_tones                | 创建     | 创建记忆单词-阿里云音色表                                                     |
| 2024-12-19 | memory_words_tts_record               | 修改     | 增加api_type、voice_value、speed_key、speed_value字段，修改voice_type字段含义 |
| 2024-12-20 | common_user_suggestions               | 创建     | 创建通用-用户建议表                                                           |
| 2024-12-27 | stopfood_dietary_mode                 | 创建     | 创建用户饮食模式表                                                            |
| 2024-12-27 | stopfood_bmr                          | 创建     | 创建用户基础代谢表                                                            |
| 2024-12-27 | stopfood_dietary_mode                 | 修改     | 将daily_heat_consumption字段替换为coefficient字段，用于计算用户每日计划热量   |
| 2024-12-30 | memory_words_user_and_record_relation | 创建     | 创建记忆单词-用户与录音关系表                                                 |
| 2024-12-31 | stopfood_daily_weight_record          | 创建     | 创建用户每日体重记录表                                                        |
| 2025-07-10 | memory_words_compare_result           | 创建     | 创建记忆单词-比对结果表                                                       |

## 表结构详情

### common_user_suggestions（通用-用户建议表）

```sql
CREATE TABLE `common_user_suggestions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `open_id` varchar(64) NOT NULL COMMENT '微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `app_id` varchar(50) DEFAULT NULL COMMENT '小程序id',
  `suggestion` text COMMENT '用户意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_open_id` (`open_id`) USING BTREE
) COMMENT='通用-用户建议表'
```

### 字段说明

| 字段名      | 数据类型    | 是否必填 | 默认值            | 说明               |
| ----------- | ----------- | -------- | ----------------- | ------------------ |
| id          | bigint      | 是       | 自增              | 记录ID             |
| open_id     | varchar(64) | 是       | 无                | 微信openid         |
| union_id    | varchar(64) | 否       | NULL              | 微信unionid        |
| app_id      | varchar(50) | 否       | NULL              | 小程序id           |
| suggestion  | text        | 否       | NULL              | 用户意见           |
| create_time | datetime    | 是       | CURRENT_TIMESTAMP | 创建时间           |
| update_time | datetime    | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新 |

### 索引说明

| 索引名称    | 索引类型 | 索引字段 | 说明                 |
| ----------- | -------- | -------- | -------------------- |
| PRIMARY     | 主键索引 | id       | 主键                 |
| idx_open_id | 普通索引 | open_id  | 提高按openid查询效率 |

### weixin_user（微信用户信息表）

```sql
CREATE TABLE `weixin_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(64) NOT NULL COMMENT '微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionid，可为空',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `gender` tinyint(1) DEFAULT '2' COMMENT '性别（0-男，1-女，2-未知）',
  `birth_date` datetime DEFAULT NULL COMMENT '用户生日',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_union_id` (`union_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';
```

### 字段说明

| 字段名      | 数据类型     | 是否必填 | 默认值            | 说明                       |
| ----------- | ------------ | -------- | ----------------- | -------------------------- |
| id          | bigint(20)   | 是       | 自增              | 主键ID                     |
| open_id     | varchar(64)  | 是       | 无                | 微信openid                 |
| union_id    | varchar(64)  | 否       | NULL              | 微信unionid，可为空        |
| phone       | varchar(20)  | 否       | NULL              | 手机号                     |
| nick_name   | varchar(50)  | 否       | NULL              | 用户昵称                   |
| gender      | tinyint(1)   | 否       | 2                 | 性别（0-男，1-女，2-未知） |
| birth_date  | datetime     | 否       | NULL              | 用户生日                   |
| height      | decimal(5,2) | 否       | NULL              | 身高(cm)                   |
| weight      | decimal(5,2) | 否       | NULL              | 体重(kg)                   |
| avatar_url  | varchar(500) | 否       | NULL              | 头像URL                    |
| login_ip    | varchar(50)  | 否       | NULL              | 最后登录IP                 |
| login_date  | datetime     | 否       | NULL              | 最后登录时间               |
| create_time | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间                   |
| update_time | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新         |
| remark      | varchar(500) | 否       | NULL              | 备注                       |

### 索引说明

| 索引名称     | 索引类型 | 索引字段 | 说明                  |
| ------------ | -------- | -------- | --------------------- |
| PRIMARY      | 主键索引 | id       | 主键                  |
| uk_open_id   | 唯一索引 | open_id  | 确保openid唯一        |
| idx_union_id | 普通索引 | union_id | 提高按unionid查询效率 |

### memory_words_textbook（记忆单词-课本单词表）

```sql
CREATE TABLE `memory_words_textbook` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `subject` VARCHAR (50) DEFAULT NULL COMMENT '学科',
  `textbook_version` VARCHAR (50) DEFAULT NULL COMMENT '教材版本',
  `grade` INT DEFAULT NULL COMMENT '年级',
  `term` INT DEFAULT NULL COMMENT '学期',
  `unit_sort` INT DEFAULT NULL COMMENT '单元排序',
  `unit_info` VARCHAR (50) DEFAULT NULL COMMENT '单元信息',
  `lesson_sort` INT DEFAULT NULL COMMENT '课文排序',
  `lesson_title` VARCHAR (100) DEFAULT NULL COMMENT '课文标题',
  `words` TEXT COMMENT '词语',
  `lesson_words` TEXT COMMENT '课文划词',
  `is_delete` VARCHAR (2) DEFAULT '否' COMMENT '是否删除（是 否）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) COMMENT = '记忆单词-课本单词表'
```

### 字段说明

| 字段名           | 数据类型     | 是否必填 | 默认值            | 说明               |
| ---------------- | ------------ | -------- | ----------------- | ------------------ |
| id               | bigint       | 是       | 自增              | 主键ID             |
| subject          | varchar(50)  | 否       | NULL              | 学科               |
| textbook_version | varchar(50)  | 否       | NULL              | 教材版本           |
| grade            | int          | 否       | NULL              | 年级               |
| term             | int          | 否       | NULL              | 学期               |
| unit_sort        | int          | 否       | NULL              | 单元排序           |
| unit_info        | varchar(50)  | 否       | NULL              | 单元信息           |
| lesson_sort      | int          | 否       | NULL              | 课文排序           |
| lesson_title     | varchar(100) | 否       | NULL              | 课文标题           |
| words            | text         | 否       | NULL              | 词语               |
| lesson_words     | text         | 否       | NULL              | 课文划词           |
| is_delete        | varchar(2)   | 否       | 否                | 是否删除（是 否）  |
| create_time      | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间           |
| update_time      | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新 |

### 索引说明

| 索引名称 | 索引类型 | 索引字段 | 说明 |
| -------- | -------- | -------- | ---- |
| PRIMARY  | 主键索引 | id       | 主键 |

### memory_words_tts_record（记忆单词-TTS语音合成记录表）

```sql
CREATE TABLE `memory_words_tts_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `open_id` varchar(64) NOT NULL COMMENT '创建人-微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '创建人-微信unionid',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `api_type` varchar(50) DEFAULT NULL COMMENT '接口类型，可选值"火山引擎"或"阿里云"',
  `environment` varchar(10) DEFAULT '中文' COMMENT '环境，可选值为"中文"或"英文"',
  `voice_type` varchar(50) DEFAULT NULL COMMENT '音色类型-key，如 普通话、英式英语、美式英语',
  `voice_value` varchar(59) DEFAULT NULL COMMENT '音色类型-value，如BV700_streaming、BV040_streaming、BV027_streaming',
  `text_content` text COMMENT '合成的文本内容',
  `text_type` varchar(10) DEFAULT 'plain' COMMENT '文本类型，plain(普通文本)或ssml(带标记语言)',
  `speed_key` varchar(50) DEFAULT NULL COMMENT '语速-key',
  `speed_value` float DEFAULT '1' COMMENT '语速-value，实际值',
  `repeat_count` int DEFAULT '1' COMMENT '重复次数，仅在textType=ssml时生效',
  `pause_seconds` float DEFAULT '1' COMMENT '停顿秒数，仅在textType=ssml时生效',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '合成后的语音文件URL',
  `audio_duration` int DEFAULT NULL COMMENT '语音时长(秒)',
  `status` tinyint DEFAULT '0' COMMENT '状态：0-处理中，1-成功，2-失败',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息，仅在状态为失败时有值',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) COMMENT='记忆单词-TTS语音合成记录表'
```

### 字段说明

| 字段名         | 数据类型     | 是否必填 | 默认值            | 说明                                                                |
| -------------- | ------------ | -------- | ----------------- | ------------------------------------------------------------------- |
| id             | bigint       | 是       | 自增              | 记录ID                                                              |
| open_id        | varchar(64)  | 是       | 无                | 创建人-微信openid                                                   |
| union_id       | varchar(64)  | 否       | NULL              | 创建人-微信unionid                                                  |
| create_time    | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间                                                            |
| update_time    | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                                                  |
| api_type       | varchar(50)  | 否       | NULL              | 接口类型，可选值"火山引擎"或"阿里云"                                |
| environment    | varchar(10)  | 否       | 中文              | 环境，可选值为"中文"或"英文"                                        |
| voice_type     | varchar(50)  | 否       | NULL              | 音色类型-key，如 普通话、英式英语、美式英语                         |
| voice_value    | varchar(59)  | 否       | NULL              | 音色类型-value，如BV700_streaming、BV040_streaming、BV027_streaming |
| text_content   | text         | 否       | NULL              | 合成的文本内容                                                      |
| text_type      | varchar(10)  | 否       | plain             | 文本类型，plain(普通文本)或ssml(带标记语言)                         |
| speed_key      | varchar(50)  | 否       | NULL              | 语速-key                                                            |
| speed_value    | float        | 否       | 1                 | 语速-value，实际值                                                  |
| repeat_count   | int          | 否       | 1                 | 重复次数，仅在textType=ssml时生效                                   |
| pause_seconds  | float        | 否       | 1                 | 停顿秒数，仅在textType=ssml时生效                                   |
| audio_url      | varchar(255) | 否       | NULL              | 合成后的语音文件URL                                                 |
| audio_duration | int          | 否       | NULL              | 语音时长(秒)                                                        |
| status         | tinyint      | 否       | 0                 | 状态：0-处理中，1-成功，2-失败                                      |
| error_msg      | varchar(255) | 否       | NULL              | 错误信息，仅在状态为失败时有值                                      |

### 索引说明

| 索引名称    | 索引类型 | 索引字段 | 说明                 |
| ----------- | -------- | -------- | -------------------- |
| PRIMARY     | 主键索引 | id       | 主键                 |
| idx_open_id | 普通索引 | open_id  | 提高按openid查询效率 |

### memory_words_tones（记忆单词-音色表）

```sql
CREATE TABLE `memory_words_tones` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `environment` VARCHAR(10) COMMENT '环境，可选值为"中文"或"英文"',
  `voice_type_key` VARCHAR(50) COMMENT '音色类型-key，如普通话、英式英语、美式英语',
  `voice_type_value` VARCHAR(50) COMMENT '音色类型-value，参考https://www.volcengine.com/docs/6561/97465',
  `speed_ratio_key` VARCHAR(50) COMMENT '语速-key，慢速，中速，快速',
  `speed_ratio_value` FLOAT(10,1) COMMENT '语速-value',
  `description` VARCHAR(200) COMMENT '描述',
  `is_delete` VARCHAR(2) DEFAULT '否' COMMENT '是否删除，默认是否',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) COMMENT = '记忆单词-音色表'
```

### 字段说明

| 字段名            | 数据类型     | 是否必填 | 默认值            | 说明                                                           |
| ----------------- | ------------ | -------- | ----------------- | -------------------------------------------------------------- |
| id                | bigint       | 是       | 自增              | 记录ID                                                         |
| environment       | varchar(10)  | 否       | NULL              | 环境，可选值为"中文"或"英文"                                   |
| voice_type_key    | varchar(50)  | 否       | NULL              | 音色类型-key，如普通话、英式英语、美式英语                     |
| voice_type_value  | varchar(50)  | 否       | NULL              | 音色类型-value，参考https://www.volcengine.com/docs/6561/97465 |
| speed_ratio_key   | varchar(50)  | 否       | NULL              | 语速-key，慢速，中速，快速                                     |
| speed_ratio_value | float(10,1)  | 否       | NULL              | 语速-value                                                     |
| description       | varchar(200) | 否       | NULL              | 描述                                                           |
| is_delete         | varchar(2)   | 否       | 否                | 是否删除，默认是否                                             |
| create_time       | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间                                                       |
| update_time       | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                                             |

### 索引说明

| 索引名称 | 索引类型 | 索引字段 | 说明 |
| -------- | -------- | -------- | ---- |
| PRIMARY  | 主键索引 | id       | 主键 |

### memory_words_ali_tones（记忆单词-阿里云音色表）

```sql
CREATE TABLE `memory_words_ali_tones` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `environment` varchar(10) DEFAULT '中文' COMMENT '环境，可选值为"中文"或"英文"',
  `sample_rate` int DEFAULT NULL COMMENT '采样率: 8000, 16000',
  `voice_key` varchar(50) DEFAULT NULL COMMENT '发音人-key，如普通话、英式英语、美式英语',
  `voice_value` varchar(50) DEFAULT NULL COMMENT '发音人-value',
  `volume` int DEFAULT NULL COMMENT '音量0-100',
  `speech_rate_key` varchar(50) DEFAULT NULL COMMENT '语速-key，慢速，中速，快速',
  `speech_rate_value` int DEFAULT NULL COMMENT '语速-value',
  `pitch_rate_key` int DEFAULT NULL COMMENT '语调-key',
  `pitch_rate_value` int DEFAULT NULL COMMENT '语调-value',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `is_delete` varchar(2) DEFAULT '否' COMMENT '是否删除，默认是否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) COMMENT='记忆单词-阿里云音色表'
```

### 字段说明

| 字段名            | 数据类型     | 是否必填 | 默认值            | 说明                                     |
| ----------------- | ------------ | -------- | ----------------- | ---------------------------------------- |
| id                | bigint       | 是       | 自增              | 记录ID                                   |
| environment       | varchar(10)  | 否       | 中文              | 环境，可选值为"中文"或"英文"             |
| sample_rate       | int          | 否       | NULL              | 采样率: 8000, 16000                      |
| voice_key         | varchar(50)  | 否       | NULL              | 发音人-key，如普通话、英式英语、美式英语 |
| voice_value       | varchar(50)  | 否       | NULL              | 发音人-value                             |
| volume            | int          | 否       | NULL              | 音量0-100                                |
| speech_rate_key   | varchar(50)  | 否       | NULL              | 语速-key，慢速，中速，快速               |
| speech_rate_value | int          | 否       | NULL              | 语速-value                               |
| pitch_rate_key    | int          | 否       | NULL              | 语调-key                                 |
| pitch_rate_value  | int          | 否       | NULL              | 语调-value                               |
| description       | varchar(200) | 否       | NULL              | 描述                                     |
| is_delete         | varchar(2)   | 否       | 否                | 是否删除，默认是否                       |
| create_time       | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间                                 |
| update_time       | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                       |

### 索引说明

| 索引名称 | 索引类型 | 索引字段 | 说明 |
| -------- | -------- | -------- | ---- |
| PRIMARY  | 主键索引 | id       | 主键 |

### memory_words_user_and_record_relation（记忆单词-用户与录音关系表）

```sql
CREATE TABLE `memory_words_user_and_record_relation` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `open_id` VARCHAR (64) NOT NULL COMMENT '微信openid',
  `union_id` VARCHAR (64) DEFAULT NULL COMMENT '微信unionid',
  `record_id` BIGINT DEFAULT NULL COMMENT '录音id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_open_id` (`open_id`) USING BTREE,
  KEY `idx_record_id` (`record_id`) USING BTREE
) COMMENT = '记忆单词-用户与录音关系'
```

### 字段说明

| 字段名      | 数据类型    | 是否必填 | 默认值            | 说明               |
| ----------- | ----------- | -------- | ----------------- | ------------------ |
| id          | bigint      | 是       | 自增              | 记录ID             |
| open_id     | varchar(64) | 是       | 无                | 微信openid         |
| union_id    | varchar(64) | 否       | NULL              | 微信unionid        |
| record_id   | bigint      | 否       | NULL              | 录音id             |
| create_time | datetime    | 是       | CURRENT_TIMESTAMP | 创建时间           |
| update_time | datetime    | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新 |

### 索引说明

| 索引名称      | 索引类型 | 索引字段  | 说明                 |
| ------------- | -------- | --------- | -------------------- |
| PRIMARY       | 主键索引 | id        | 主键                 |
| idx_open_id   | 普通索引 | open_id   | 提高按openid查询效率 |
| idx_record_id | 普通索引 | record_id | 提高按录音id查询效率 |

### memory_words_compare_result（记忆单词-比对结果表）

```sql
CREATE TABLE `memory_words_compare_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对比表主键id',
  `open_id` varchar(64) NOT NULL COMMENT '创建人-微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '创建人-微信unionid',
  `relation_id` bigint DEFAULT NULL COMMENT 'memory_words_user_and_record_relation表的主键id',
  `original_words` text COMMENT '原始的单词词组等',
  `user_words` text COMMENT '用户输入的单词词组等',
  `compare_result` text COMMENT 'original_wordsuser_words的比对结果，以压缩的json格式存下来',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) COMMENT='记忆单词-比对结果表'
```

### 字段说明

| 字段名         | 数据类型    | 是否必填 | 默认值            | 说明                                                       |
| -------------- | ----------- | -------- | ----------------- | ---------------------------------------------------------- |
| id             | bigint      | 是       | 自增              | 对比表主键id                                               |
| open_id        | varchar(64) | 是       | 无                | 创建人-微信openid                                          |
| union_id       | varchar(64) | 否       | NULL              | 创建人-微信unionid                                         |
| relation_id    | bigint      | 否       | NULL              | memory_words_user_and_record_relation表的主键id            |
| original_words | text        | 否       | NULL              | 原始的单词词组等                                           |
| user_words     | text        | 否       | NULL              | 用户输入的单词词组等                                       |
| compare_result | text        | 否       | NULL              | original_wordsuser_words的比对结果，以压缩的json格式存下来 |
| create_time    | datetime    | 是       | CURRENT_TIMESTAMP | 创建时间                                                   |
| update_time    | datetime    | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                                         |

### 索引说明

| 索引名称    | 索引类型 | 索引字段 | 说明                 |
| ----------- | -------- | -------- | -------------------- |
| PRIMARY     | 主键索引 | id       | 主键                 |
| idx_open_id | 普通索引 | open_id  | 提高按openid查询效率 |

### stopfood_heat_record（热量记录表）

```sql
CREATE TABLE `stopfood_heat_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `open_id` varchar(64) DEFAULT NULL COMMENT '微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `event_name` varchar(100) DEFAULT NULL COMMENT '事件名称',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：摄入热量，消耗热量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位：克，分钟等',
  `quantity` int DEFAULT NULL COMMENT '单位对应的数量',
  `heat_value` int DEFAULT NULL COMMENT '热量值，单位：千卡(kcal)',
  `carbohydrate` int DEFAULT NULL COMMENT '碳水化合物，单位：克',
  `protein` int DEFAULT NULL COMMENT '蛋白质，单位：克',
  `fat` int DEFAULT NULL COMMENT '脂肪，单位：克',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) COMMENT='热量记录表'
```

### 字段说明

| 字段名       | 数据类型     | 是否必填 | 默认值            | 说明                     |
| ------------ | ------------ | -------- | ----------------- | ------------------------ |
| id           | bigint       | 是       | 自增              | 记录ID                   |
| open_id      | varchar(64)  | 否       | NULL              | 微信openid               |
| union_id     | varchar(64)  | 否       | NULL              | 微信unionid              |
| event_name   | varchar(100) | 否       | NULL              | 事件名称                 |
| type         | varchar(20)  | 否       | NULL              | 类型：摄入热量，消耗热量 |
| unit         | varchar(20)  | 否       | NULL              | 单位：克，分钟等         |
| quantity     | int          | 否       | NULL              | 单位对应的数量           |
| heat_value   | int          | 否       | NULL              | 热量值，单位：千卡(kcal) |
| carbohydrate | int          | 否       | NULL              | 碳水化合物，单位：克     |
| protein      | int          | 否       | NULL              | 蛋白质，单位：克         |
| fat          | int          | 否       | NULL              | 脂肪，单位：克           |
| create_time  | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间                 |
| update_time  | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新       |

### 索引说明

| 索引名称    | 索引类型 | 索引字段 | 说明                 |
| ----------- | -------- | -------- | -------------------- |
| PRIMARY     | 主键索引 | id       | 主键                 |
| idx_open_id | 普通索引 | open_id  | 提高按openid查询效率 |

### stopfood_dietary_mode（用户饮食模式表）

```sql
CREATE TABLE `stopfood_dietary_mode` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(64)  NOT NULL COMMENT '微信openid',
  `union_id` varchar(64)  DEFAULT NULL COMMENT '微信unionid，可为空',
  `mode` varchar(64)  DEFAULT NULL COMMENT '饮食模式：快速减重、平稳减重、体重维持、自定义',
  `coefficient` double DEFAULT NULL COMMENT '系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`)
) COMMENT='用户饮食模式'
```

### 字段说明

| 字段名      | 数据类型    | 是否必填 | 默认值            | 说明                                                       |
| ----------- | ----------- | -------- | ----------------- | ---------------------------------------------------------- |
| id          | bigint      | 是       | 自增              | 主键ID                                                     |
| open_id     | varchar(64) | 是       | 无                | 微信openid                                                 |
| union_id    | varchar(64) | 否       | NULL              | 微信unionid，可为空                                        |
| mode        | varchar(64) | 否       | NULL              | 饮食模式：快速减重、平稳减重、体重维持、自定义             |
| coefficient | double      | 否       | NULL              | 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数） |
| create_time | datetime    | 是       | CURRENT_TIMESTAMP | 创建时间                                                   |
| update_time | datetime    | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                                         |

### 索引说明

| 索引名称   | 索引类型 | 索引字段 | 说明           |
| ---------- | -------- | -------- | -------------- |
| PRIMARY    | 主键索引 | id       | 主键           |
| uk_open_id | 唯一索引 | open_id  | 确保openid唯一 |

### stopfood_bmr（用户基础代谢表）

```sql
CREATE TABLE `stopfood_bmr` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(64) NOT NULL COMMENT '微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionid，可为空',
  `bmr_value` int DEFAULT NULL COMMENT '用户的基础代谢，单位：千卡(kcal)',
  `generation_mode` varchar(64) DEFAULT NULL COMMENT '基础代谢的生成方式：公式计算、自定义',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`)
) COMMENT='用户基础代谢表'
```

### 字段说明

| 字段名          | 数据类型    | 是否必填 | 默认值            | 说明                                 |
| --------------- | ----------- | -------- | ----------------- | ------------------------------------ |
| id              | bigint      | 是       | 自增              | 主键ID                               |
| open_id         | varchar(64) | 是       | 无                | 微信openid                           |
| union_id        | varchar(64) | 否       | NULL              | 微信unionid，可为空                  |
| bmr_value       | int         | 否       | NULL              | 用户的基础代谢，单位：千卡(kcal)     |
| generation_mode | varchar(64) | 否       | NULL              | 基础代谢的生成方式：公式计算、自定义 |
| create_time     | datetime    | 是       | CURRENT_TIMESTAMP | 创建时间                             |
| update_time     | datetime    | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新                   |

### 索引说明

| 索引名称   | 索引类型 | 索引字段 | 说明           |
| ---------- | -------- | -------- | -------------- |
| PRIMARY    | 主键索引 | id       | 主键           |
| uk_open_id | 唯一索引 | open_id  | 确保openid唯一 |

### stopfood_daily_weight_record（用户每日体重记录表）

```sql
CREATE TABLE `stopfood_daily_weight_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(64) NOT NULL COMMENT '微信openid',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionid，可为空',
  `record_date` date NOT NULL COMMENT '记录日期',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重，单位：千克(kg)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id_date` (`open_id`, `record_date`),
  KEY `idx_open_id` (`open_id`)
) COMMENT='用户每日体重记录表'
```

### 字段说明

| 字段名      | 数据类型     | 是否必填 | 默认值            | 说明                 |
| ----------- | ------------ | -------- | ----------------- | -------------------- |
| id          | bigint       | 是       | 自增              | 主键ID               |
| open_id     | varchar(64)  | 是       | 无                | 微信openid           |
| union_id    | varchar(64)  | 否       | NULL              | 微信unionid，可为空  |
| record_date | date         | 是       | 无                | 记录日期             |
| weight      | decimal(5,2) | 否       | NULL              | 体重，单位：千克(kg) |
| create_time | datetime     | 是       | CURRENT_TIMESTAMP | 创建时间             |
| update_time | datetime     | 是       | CURRENT_TIMESTAMP | 更新时间，自动更新   |

### 索引说明

| 索引名称        | 索引类型 | 索引字段             | 说明                               |
| --------------- | -------- | -------------------- | ---------------------------------- |
| PRIMARY         | 主键索引 | id                   | 主键                               |
| uk_open_id_date | 唯一索引 | open_id, record_date | 确保同一用户在同一天只能有一条记录 |
| idx_open_id     | 普通索引 | open_id              | 提高按openid查询效率               |