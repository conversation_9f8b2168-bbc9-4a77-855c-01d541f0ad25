package com.github.binarywang.demo.wx.miniapp.vo.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions;

/**
 * 用户建议视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户建议信息")
public class UserSuggestionVO {

    /**
     * 记录ID
     */
    @Schema(description = "记录ID")
    private Long id;

    /**
     * 微信openid
     */
    @Schema(description = "微信openid")
    private String openId;

    /**
     * 微信unionid
     */
    @Schema(description = "微信unionid")
    private String unionId;

    /**
     * 小程序id
     */
    @Schema(description = "小程序id")
    private String appId;

    /**
     * 用户意见
     */
    @Schema(description = "用户意见")
    private String suggestion;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 从实体类转换为VO
     * 
     * @param entity 实体类对象
     * @return VO对象
     */
    public static UserSuggestionVO fromEntity(CommonUserSuggestions entity) {
        if (entity == null) {
            return null;
        }

        return UserSuggestionVO.builder()
                .id(entity.getId())
                .openId(entity.getOpenId())
                .unionId(entity.getUnionId())
                .appId(entity.getAppId())
                .suggestion(entity.getSuggestion())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }
}