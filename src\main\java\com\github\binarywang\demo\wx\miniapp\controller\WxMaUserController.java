package com.github.binarywang.demo.wx.miniapp.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.entity.WeixinUser;
import com.github.binarywang.demo.wx.miniapp.mapper.WeixinUserMapper;
import com.github.binarywang.demo.wx.miniapp.utils.IpUtils;
import com.github.binarywang.demo.wx.miniapp.utils.JsonUtils;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.RedisUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.vo.LoginResult;
import com.github.binarywang.demo.wx.miniapp.dto.UserProfileRequest;
import com.github.binarywang.demo.wx.miniapp.dto.UserInfoRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.nio.charset.StandardCharsets;
import com.alibaba.fastjson2.JSONObject;

/**
 * 微信小程序用户接口
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@RestController
@Slf4j
@RequestMapping("/wx/user/{appid}")
@Tag(name = "小程序用户接口")
public class WxMaUserController {
    private final WxMaService wxMaService;
    private final JwtUtils jwtUtils;
    private final RedisUtils redisUtils;
    private final WeixinUserMapper weixinUserMapper;

    @Value("${jwt.expiration:604800}")
    private Long tokenExpiration;

    @Autowired
    public WxMaUserController(WxMaService wxMaService, JwtUtils jwtUtils, RedisUtils redisUtils,
            WeixinUserMapper weixinUserMapper) {
        this.wxMaService = wxMaService;
        this.jwtUtils = jwtUtils;
        this.redisUtils = redisUtils;
        this.weixinUserMapper = weixinUserMapper;
    }

    /**
     * 登陆接口
     */
    @GetMapping("/login")
    @Operation(summary = "登录接口", description = "通过code获取微信用户的openId和session_key")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "code", description = "微信小程序授权码", required = true, in = ParameterIn.QUERY)
    })
    public ApiResponse<LoginResult> login(@PathVariable String appid, @RequestParam String code,
            HttpServletRequest request) {
        if (StringUtils.isBlank(code)) {
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "code不能为空");
        }

        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                    String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            log.info("获取到的session信息: {}", JsonUtils.toJson(session));

            // 生成JWT token
            String token = jwtUtils.generateToken(session.getOpenid(), session.getSessionKey());

            // 将token存入Redis，key为 wx:token:openId，过期时间与token一致
            String tokenKey = TokenUtils.getTokenKey(session.getOpenid());
            redisUtils.set(tokenKey, token, tokenExpiration);
            log.info("用户[{}]的token已存入Redis，key={}", session.getOpenid(), tokenKey);

            // 获取客户端IP地址
            String ipAddr = IpUtils.getIpAddr(request);

            // 根据openId判断用户是否存在
            WeixinUser weixinUser = weixinUserMapper.selectWeixinUserByOpenId(session.getOpenid());
            Date now = new Date();

            if (weixinUser == null) {
                // 新用户，创建用户记录
                weixinUser = new WeixinUser();
                weixinUser.setOpenId(session.getOpenid());
                weixinUser.setUnionId(session.getUnionid()); // unionId可能为null
                weixinUser.setLoginIp(ipAddr);
                weixinUser.setLoginDate(now);
                weixinUser.setCreateTime(now);
                weixinUser.setUpdateTime(now);

                // 插入新用户
                weixinUserMapper.insertWeixinUser(weixinUser);
                log.info("新用户[{}]首次登录，已创建用户记录", session.getOpenid());
            } else {
                // 老用户，更新登录信息
                weixinUser.setLoginIp(ipAddr);
                weixinUser.setLoginDate(now);
                // 如果有unionId且用户的unionId为空，则更新
                if (StringUtils.isNotBlank(session.getUnionid()) && StringUtils.isBlank(weixinUser.getUnionId())) {
                    weixinUser.setUnionId(session.getUnionid());
                }

                // 更新用户信息
                weixinUserMapper.updateWeixinUser(weixinUser);
                log.info("用户[{}]登录成功，已更新登录信息", session.getOpenid());
            }

            // 构建登录结果
            LoginResult loginResult = LoginResult.builder()
                    .sessionKey(session.getSessionKey())
                    .openId(session.getOpenid())
                    .unionId(session.getUnionid())
                    .token(token)
                    .build();

            return ApiResponse.success("登录成功", loginResult);
        } catch (WxErrorException e) {
            log.error("微信登录失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, e.toString());
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * <pre>
     * 获取用户绑定手机号信息
     * </pre>
     */
    @GetMapping("/phone")
    @Operation(summary = "获取用户手机号", description = "获取微信小程序用户绑定的手机号信息")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "sessionKey", description = "会话密钥", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "signature", description = "签名", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "rawData", description = "原始数据", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "encryptedData", description = "加密数据", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "iv", description = "加密算法的初始向量", required = true, in = ParameterIn.QUERY)
    })
    public ApiResponse<String> phone(@PathVariable String appid, @RequestParam String sessionKey,
            @RequestParam String signature,
            @RequestParam String rawData, @RequestParam String encryptedData, @RequestParam String iv,
            HttpServletRequest request) {
        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                    String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        // 用户信息校验
        if (!wxMaService.getUserService().checkUserInfo(sessionKey, rawData, signature)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, ApiResponseConstant.Message.UNAUTHORIZED);
        }

        try {
            // 解密
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(sessionKey, encryptedData,
                    iv);
            String phoneNumber = phoneNoInfo.getPhoneNumber();

            // 从token中获取openId
            String token = TokenUtils.getTokenFromRequest(request);
            String openId = null;
            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                log.warn("从请求中无法获取openId，将使用会话中的openId");
                // 尝试从当前会话中获取openId (可能为null)
                openId = sessionKey.split("_")[0];
            }

            if (StringUtils.isNotBlank(openId)) {
                WeixinUser weixinUser = weixinUserMapper.selectWeixinUserByOpenId(openId);
                if (weixinUser != null) {
                    // 获取客户端IP地址
                    String ipAddr = IpUtils.getIpAddr(request);
                    Date now = new Date();

                    // 更新用户手机号
                    weixinUser.setPhone(phoneNumber);
                    weixinUser.setLoginIp(ipAddr);
                    weixinUser.setLoginDate(now);

                    weixinUserMapper.updateWeixinUser(weixinUser);
                    log.info("已更新用户[{}]的手机号", openId);
                } else {
                    log.warn("用户[{}]在更新手机号时未找到记录", openId);
                }
            } else {
                log.warn("无法确定用户的openId，无法更新用户手机号");
            }

            return ApiResponse.success("获取用户手机号成功", phoneNumber);
        } catch (Exception e) {
            log.error("获取用户手机号失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取用户手机号失败: " + e.getMessage());
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * <pre>
     * 更新用户个人资料（性别、生日、身高、体重）
     * </pre>
     */
    @PostMapping("/update")
    @Operation(summary = "更新用户个人资料", description = "更新用户的性别、生日、身高、体重等基础信息")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    public ApiResponse<WeixinUser> updateUserProfile(@PathVariable String appid,
            @RequestBody UserProfileRequest profileRequest,
            HttpServletRequest request) {
        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                    String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            // 从token中获取openId
            String token = TokenUtils.getTokenFromRequest(request);
            if (StringUtils.isBlank(token)) {
                WxMaConfigHolder.remove();// 清理ThreadLocal
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                WxMaConfigHolder.remove();// 清理ThreadLocal
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 根据openId获取用户信息
            WeixinUser weixinUser = weixinUserMapper.selectWeixinUserByOpenId(openId);
            if (weixinUser == null) {
                WxMaConfigHolder.remove();// 清理ThreadLocal
                return ApiResponse.error(ApiResponseConstant.Code.NOT_FOUND, "用户不存在");
            }

            // 获取客户端IP地址
            String ipAddr = IpUtils.getIpAddr(request);
            Date now = new Date();

            // 更新用户信息
            if (profileRequest.getGender() != null) {
                weixinUser.setGender(profileRequest.getGender());
            }
            if (profileRequest.getBirthDate() != null) {
                weixinUser.setBirthDate(profileRequest.getBirthDate());
            }
            if (profileRequest.getHeight() != null) {
                weixinUser.setHeight(profileRequest.getHeight());
            }
            if (profileRequest.getWeight() != null) {
                weixinUser.setWeight(profileRequest.getWeight());
            }
            if (StringUtils.isNotBlank(profileRequest.getNickName())) {
                weixinUser.setNickName(profileRequest.getNickName());
            }
            if (StringUtils.isNotBlank(profileRequest.getAvatarUrl())) {
                weixinUser.setAvatarUrl(profileRequest.getAvatarUrl());
            }

            weixinUser.setLoginIp(ipAddr);
            weixinUser.setLoginDate(now);

            // 更新用户信息
            weixinUserMapper.updateWeixinUser(weixinUser);
            log.info("已更新用户[{}]的个人资料", openId);

            // 返回更新后的用户信息
            return ApiResponse.success("更新用户个人资料成功", weixinUser);
        } catch (Exception e) {
            log.error("更新用户个人资料失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "更新用户个人资料失败: " + e.getMessage());
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * <pre>
     * 根据openId获取用户详细信息
     * </pre>
     */
    @PostMapping("/getUserInfo")
    @Operation(summary = "获取用户详细信息", description = "根据openId获取用户的头像、昵称、手机号、性别、生日、身高、体重等详细信息")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    public ApiResponse<WeixinUser> getUserInfo(@PathVariable String appid,
            @RequestBody UserInfoRequest userInfoRequest,
            HttpServletRequest request) {
        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "appid不存在");
        }

        if (userInfoRequest == null || StringUtils.isBlank(userInfoRequest.getOpenId())) {
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR, "openId不能为空");
        }

        try {
            WeixinUser weixinUser = weixinUserMapper.selectWeixinUserByOpenId(userInfoRequest.getOpenId());
            if (weixinUser == null) {
                return ApiResponse.error(ApiResponseConstant.Code.NOT_FOUND, "用户不存在");
            }
            return ApiResponse.success(weixinUser);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取用户信息失败");
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * 验证token是否有效
     */
    @GetMapping("/verifyToken")
    @Operation(summary = "验证token", description = "验证token是否有效且未过期")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    public ApiResponse<Boolean> verifyToken(@PathVariable String appid, HttpServletRequest request) {
        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                    String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            // 从请求头中获取token
            String token = TokenUtils.getTokenFromRequest(request);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "未提供认证令牌");
            }

            // 从token中获取openId
            String openId = jwtUtils.getOpenidFromToken(token);
            if (openId == null) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的认证令牌");
            }

            // 从Redis中获取存储的token
            String redisToken = (String) redisUtils.get(TokenUtils.getTokenKey(openId));
            if (redisToken == null) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "认证令牌已过期或无效");
            }

            // 验证请求中的token是否与Redis中存储的token一致
            if (!token.equals(redisToken)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "认证令牌不匹配");
            }

            // 验证token是否有效
            if (!jwtUtils.validateToken(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "认证令牌已过期");
            }

            return ApiResponse.success("token验证成功", true);
        } catch (Exception e) {
            log.error("Token验证异常：", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "服务器内部错误");
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * 获取用户当天的微信运动步数
     */
    @GetMapping("/weRunData")
    @Operation(summary = "获取用户当天微信步数", description = "获取用户当天的微信运动步数，需要用户授权scope.werun")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "sessionKey", description = "会话密钥", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "encryptedData", description = "加密数据", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "iv", description = "加密算法的初始向量", required = true, in = ParameterIn.QUERY)
    })
    public ApiResponse<JSONObject> getWeRunData(@PathVariable String appid,
            @RequestParam String sessionKey,
            @RequestParam String encryptedData,
            @RequestParam String iv,
            HttpServletRequest request) {

        if (!wxMaService.switchover(appid)) {
            WxMaConfigHolder.remove();// 清理ThreadLocal
            return ApiResponse.error(ApiResponseConstant.Code.PARAM_ERROR,
                    String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            // 从请求头中获取token
            String token = TokenUtils.getTokenFromRequest(request);
            if (StringUtils.isBlank(token)) {
                WxMaConfigHolder.remove();// 清理ThreadLocal
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "用户未授权或会话已过期");
            }

            // 从token中获取openId
            String openId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(openId)) {
                WxMaConfigHolder.remove();// 清理ThreadLocal
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无法获取用户身份信息");
            }

            log.info("获取用户[{}]微信运动步数，准备解密数据", openId);

            try {
                // 解密微信运动步数数据
                String decryptedData = decryptWxData(sessionKey, encryptedData, iv);

                // 解析解密后的JSON数据
                JSONObject jsonObject = JSONObject.parseObject(decryptedData);

                // 记录解密结果
                log.info("成功解密用户[{}]的微信运动数据: {}", openId, jsonObject);

                return ApiResponse.success("获取微信步数数据成功", jsonObject);
            } catch (Exception e) {
                log.error("解密微信步数数据失败", e);
                return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "解密微信步数数据失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("获取微信步数失败", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取微信步数失败: " + e.getMessage());
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    /**
     * 解密微信加密数据
     *
     * @param sessionKey    会话密钥
     * @param encryptedData 加密数据
     * @param iv            加密算法的初始向量
     * @return 解密后的数据
     */
    private String decryptWxData(String sessionKey, String encryptedData, String iv) {
        try {
            log.debug("开始解密微信数据: sessionKey长度={}, encryptedData长度={}, iv长度={}",
                     sessionKey != null ? sessionKey.length() : 0,
                     encryptedData != null ? encryptedData.length() : 0,
                     iv != null ? iv.length() : 0);

            // 使用标准Base64解码
            byte[] keyBytes = java.util.Base64.getDecoder().decode(sessionKey);
            byte[] ivBytes = java.util.Base64.getDecoder().decode(iv);
            byte[] dataBytes = java.util.Base64.getDecoder().decode(encryptedData);

            // 使用标准Java加密库进行AES-128-CBC解密，PKCS5Padding在Java中等同于PKCS7Padding
            javax.crypto.Cipher cipher = javax.crypto.Cipher.getInstance("AES/CBC/PKCS5Padding");
            javax.crypto.spec.SecretKeySpec keySpec = new javax.crypto.spec.SecretKeySpec(keyBytes, "AES");
            javax.crypto.spec.IvParameterSpec ivSpec = new javax.crypto.spec.IvParameterSpec(ivBytes);

            cipher.init(javax.crypto.Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decryptedBytes = cipher.doFinal(dataBytes);

            String decryptedData = new String(decryptedBytes, StandardCharsets.UTF_8);
            log.debug("微信数据解密成功，解密后数据长度: {}", decryptedData.length());
            return decryptedData;

        } catch (Exception e) {
            log.error("解密微信数据失败: sessionKey={}, iv={}, encryptedData前20字符={}, 错误信息={}",
                     sessionKey,
                     iv,
                     encryptedData != null && encryptedData.length() > 20 ? encryptedData.substring(0, 20) + "..." : encryptedData,
                     e.getMessage(), e);
            throw new RuntimeException("解密微信数据失败: " + e.getMessage(), e);
        }
    }
}
