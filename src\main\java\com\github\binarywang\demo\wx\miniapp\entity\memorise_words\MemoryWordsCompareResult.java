package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 记忆单词-比对结果实体类
 */
@Data
public class MemoryWordsCompareResult {
    /**
     * 对比表主键id
     */
    private Long id;

    /**
     * 创建人-微信openid
     */
    private String openId;

    /**
     * 创建人-微信unionid
     */
    private String unionId;

    /**
     * memory_words_user_and_record_relation表的主键id
     */
    private Long relationId;

    /**
     * 原始的单词词组等
     */
    private String originalWords;

    /**
     * 用户输入的单词词组等
     */
    private String userWords;

    /**
     * original_words和user_words的比对结果，以压缩的json格式存下来
     */
    private String compareResult;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}