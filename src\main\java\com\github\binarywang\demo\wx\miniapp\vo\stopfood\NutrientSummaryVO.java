package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 营养素汇总响应VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "营养素汇总响应")
public class NutrientSummaryVO {

    @Schema(description = "碳水化合物总量，单位：克", example = "300")
    private Integer totalCarbohydrate;

    @Schema(description = "蛋白质总量，单位：克", example = "120")
    private Integer totalProtein;

    @Schema(description = "脂肪总量，单位：克", example = "80")
    private Integer totalFat;

    @Schema(description = "查询的开始日期", example = "2024-01-01")
    private String startDate;

    @Schema(description = "查询的结束日期", example = "2024-01-01")
    private String endDate;
}