# 生产环境配置
spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: chenrui
    password: <PERSON><PERSON><PERSON>@wuxi
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 30
      # 连接池最小空闲连接数
      minimum-idle: 10
      # 连接最大空闲时间
      idle-timeout: 600000
      # 连接最大生存时间
      max-lifetime: 1800000
      # 连接超时时间
      connection-timeout: 30000
  # Redis配置
  redis:
    host: localhost
    port: 16379
    password: Xiaoxuxu@yymf
    database: 0
    timeout: 10000
    jedis:
      pool:
        max-active: 32
        max-wait: -1
        max-idle: 16
        min-idle: 8

# 日志配置
logging:
  level:
    com.github.binarywang.demo.wx.miniapp: warn
    org.springframework.web: warn
  file:
    name: /var/log/stopfood/application.log
    max-size: 10MB
    max-history: 30

# Swagger配置
swagger:
  enabled: false  # 生产环境关闭Swagger 