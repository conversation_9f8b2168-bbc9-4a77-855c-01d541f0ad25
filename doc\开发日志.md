# 项目开发日志

## 项目概述

这是一个基于Spring Boot的微信小程序后端服务，用于提供微信小程序的接口支持。项目主要使用了以下技术栈：

- Spring Boot 2.6.3
- MyBatis
- MySQL
- Redis
- JWT认证
- OpenAPI 3 (原Swagger 2)

## 开发规范

### 项目结构规范

项目遵循标准的Spring Boot项目结构：

```
src/main/java/com/github/binarywang/demo/wx/miniapp/
├── config/         # 配置类
├── constant/       # 常量定义
├── controller/     # 控制器
├── entity/         # 实体类
├── dto/            # 数据传输对象
├── vo/             # 视图对象
├── mapper/         # MyBatis映射接口
├── service/        # 业务服务层
├── utils/          # 工具类
└── WxMaDemoApplication.java  # 应用入口类
```

### 代码规范

1. **命名规范**
   - 类名：采用大驼峰命名法（例如：`WeixinUser`、`UserProfileRequest`）
   - 方法名、变量名：采用小驼峰命名法（例如：`getUserInfo`、`openId`）
   - 常量：全部大写，单词间用下划线分隔（例如：`API_RESPONSE_CONSTANT`）
   - 包名：全部小写（例如：`com.github.binarywang.demo.wx.miniapp.controller`）

2. **注释规范**
   - 类注释：说明类的用途
   - 方法注释：说明方法的功能、参数和返回值
   - 复杂逻辑的代码块应添加适当的注释

3. **类文件分类规范**
   - 接口请求参数类（Request对象）放在`dto`包下
   - 接口返回参数类（Response/VO对象）放在`vo`包下
   - 数据库实体类放在`entity`包下
   - 不要在不同包中放置相同作用的类，避免引用混乱

#### 详细说明
1. **dto包下的类**
   - 存放所有接口入参对象，如 `FastModeRequest`、`UserProfileRequest` 等
   - 命名规则：通常以 `Request` 或 `DTO` 结尾，表示接口请求参数
   - 主要用于接收客户端请求的数据，然后传递给服务层处理
   - 应该只包含接口所需的参数，避免冗余字段
   - 通常包含数据验证注解，如 `@NotNull`, `@NotBlank`, `@Size` 等
   - DTO主要职责是数据传输，不包含业务逻辑
   - 每个字段应该有 `@Schema` 注解，方便生成API文档

2. **vo包下的类**
   - 存放所有接口返回对象，如 `ApiResponse`、`LoginResult`、`HeatBudgetVO` 等
   - 命名规则：通常以 `VO`、`Result` 或具体的业务名称结尾
   - 主要用于封装返回给客户端的数据，定制前端需要的数据结构
   - 可以通过构建器模式创建，方便转换和组装数据
   - VO对象通常比实体对象更"瘦"，只包含前端需要的字段
   - 应该提供从实体类转换为VO的方法，如 `fromEntity(Entity entity)`
   - 每个字段应该有 `@Schema` 注解，方便生成API文档
   - VO应该是不可变的，只提供getter方法，不提供setter方法

3. **entity包下的类**
   - 存放所有数据库实体类，如 `WeixinUser`、`StopfoodFastRecord` 等
   - 命名规则：通常与数据库表名对应，采用驼峰命名法
   - 主要用于ORM映射，与数据库表结构一一对应
   - 必须包含id、createTime、updateTime等基础字段
   - 实体类应该专注于表示数据，不应包含复杂的业务逻辑
   - 通常使用 `@Data` 注解生成getter、setter、equals、hashCode等方法
   - 可以使用 `@Builder` 注解提供构建器模式

#### 三者的区别与联系
- **DTO(Data Transfer Object)**: 
  * 主要关注**输入**，负责将API请求的数据封装成对象
  * 侧重于API接口定义和数据校验
  * 与前端紧密耦合，结构随API接口变化而变化
  * 例如：`HeatBudgetRequest`

- **Entity**: 
  * 主要关注**持久化**，与数据库表结构一一对应
  * 侧重于数据存储和访问
  * 与数据库紧密耦合，结构随数据库表变化而变化
  * 例如：`StopfoodHeatBudget`

- **VO(View Object)**: 
  * 主要关注**输出**，负责将内部数据组织成适合前端展示的格式
  * 侧重于数据展示和序列化
  * 与前端视图层紧密耦合，结构随前端展示需求变化而变化
  * 例如：`HeatBudgetVO`

#### 使用场景与最佳实践
- **正确使用DTO**:
  * 当需要接收并验证客户端输入时
  * 当API请求参数复杂或需要特殊验证时
  * 用于跨模块的数据传递，隐藏内部实现细节

- **正确使用Entity**:
  * 用于数据持久化和数据库交互
  * 在服务层和数据访问层之间传递数据
  * 不要直接在API响应中返回实体对象，避免暴露内部数据结构

- **正确使用VO**:
  * 当需要定制API响应格式时
  * 当需要组合多个实体数据为一个响应时
  * 当需要屏蔽内部字段不对外暴露时

#### 类之间的转换
- 在服务层中，经常需要在dto、vo和entity之间进行转换
- 可以使用以下方式进行转换：
  * 手动setter/getter：适用于字段较少或需要特殊处理的情况
  * BeanUtils.copyProperties：适用于同名属性的批量复制
  * ModelMapper等工具库：适用于复杂对象的映射
  * 构建器模式：如使用lombok的@Builder注解，提供流式构建对象的能力
  * 在VO类中提供静态方法如 `fromEntity()`：方便直接从实体类转换为VO

#### 代码示例
```java
// DTO示例 - 接口入参
@Data
@Schema(description = "热量预算请求参数")
public class HeatBudgetRequest {
    @NotBlank(message = "模式不能为空")
    @Schema(description = "模式：体重维持、平稳减重、效率减重、用户自定义", required = true)
    private String mode;
    
    @NotNull(message = "每日摄入热量不能为空")
    @Schema(description = "每日摄入热量，单位：千卡(kcal)", required = true)
    private Integer dailyIntake;
    
    // 其他字段...
}

// Entity示例 - 数据库实体
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopfoodHeatBudget {
    private Long id;
    private String openId;
    private String mode;
    private Integer dailyIntake;
    private Date createTime;
    private Date updateTime;
    // 其他字段...
}

// VO示例 - 接口返回
@Data
@Builder
@Schema(description = "热量预算信息")
public class HeatBudgetVO {
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "模式：体重维持、平稳减重、效率减重、用户自定义")
    private String mode;
    
    @Schema(description = "每日摄入热量，单位：千卡(kcal)")
    private Integer dailyIntake;
    
    // 其他字段...
    
    // 从实体转换为VO的静态方法
    public static HeatBudgetVO fromEntity(StopfoodHeatBudget entity) {
        if (entity == null) {
            return null;
        }
        return HeatBudgetVO.builder()
                .id(entity.getId())
                .mode(entity.getMode())
                .dailyIntake(entity.getDailyIntake())
                // 其他字段映射...
                .build();
    }
}

// Controller层使用示例
@PostMapping("/setBudget")
public ApiResponse<HeatBudgetVO> setHeatBudget(@Valid @RequestBody HeatBudgetRequest request) {
    // 1. 接收DTO参数
    // 2. 转换DTO为Entity
    StopfoodHeatBudget budget = new StopfoodHeatBudget();
    budget.setMode(request.getMode());
    budget.setDailyIntake(request.getDailyIntake());
    
    // 3. 数据库操作
    heatBudgetMapper.insert(budget);
    
    // 4. 转换Entity为VO并返回
    return ApiResponse.success("设置热量预算成功", HeatBudgetVO.fromEntity(budget));
}
```

4. **API接口规范**
   - 使用Swagger注解对API进行文档化
   - API路径采用RESTful风格
   - 请求参数和响应结果使用统一的数据格式
   - **所有新增接口优先使用POST方式**
   - 所有接口必须在接口文档中详细记录

5. **接口文档规范**
   - 所有接口都必须在`doc/接口文档.md`中详细说明
   - 文档应包含以下内容：
     * 接口名称和用途
     * 请求URL
     * 请求方式（GET/POST）
     * 请求参数说明（包括参数名、类型、是否必填、说明）
     * 请求参数示例（JSON格式）
     * 返回参数说明（包括参数名、类型、说明）
     * 返回参数示例（JSON格式）
     * 错误码说明
   - 接口文档必须与实际接口实现保持同步更新
   - 对复杂业务逻辑的接口，应添加必要的流程说明

   示例格式：
   ```markdown
   ### 接口名称：更新用户个人资料

   **接口描述**：更新微信用户的性别、生日、身高、体重等信息

   **请求URL**：/wx/user/{appid}/profile

   **请求方式**：POST

   **请求参数**：

   | 参数名    | 类型       | 是否必填 | 说明                       |
   | --------- | ---------- | -------- | -------------------------- |
   | appid     | String     | 是       | 微信小程序appid            |
   | gender    | Integer    | 否       | 性别（0-男，1-女，2-未知） |
   | birthDate | Date       | 否       | 用户生日                   |
   | height    | BigDecimal | 否       | 身高(cm)                   |
   | weight    | BigDecimal | 否       | 体重(kg)                   |

   **请求参数示例**：
   ```json
   {
     "gender": 0,
     "birthDate": "1990-01-01",
     "height": 175.5,
     "weight": 65.5
   }
   ```

   **返回参数**：

   | 参数名  | 类型    | 说明                   |
   | ------- | ------- | ---------------------- |
   | code    | Integer | 状态码，0表示成功      |
   | message | String  | 提示信息               |
   | data    | Object  | 返回数据，用户信息对象 |

   **返回示例**：
   ```json
   {
     "code": 0,
     "message": "更新用户个人资料成功",
     "data": {
       "id": 1,
       "openId": "oRrdQt0gOSjXXXXXXXXXXXXXXXXX",
       "nickName": "张三",
       "gender": 0,
       "birthDate": "1990-01-01",
       "height": 175.5,
       "weight": 65.5,
       "phone": "13800138000",
       "avatarUrl": "https://xxx.com/avatar.jpg",
       "createTime": "2023-10-16 10:00:00",
       "updateTime": "2024-08-26 15:30:00"
     }
   }
   ```

   **错误码说明**：

   | 错误码 | 说明           |
   | ------ | -------------- |
   | 401    | 未授权         |
   | 404    | 用户不存在     |
   | 500    | 服务器内部错误 |
   ```

### 数据库规范

1. **表设计规范**
   - 表名使用小写，单词间用下划线分隔
   - 必须包含`id`（主键）、`create_time`（创建时间）、`update_time`（更新时间）字段
   - 字段名使用小写，单词间用下划线分隔
   - 每个字段都应有注释说明其用途

2. **SQL规范**
   - 使用MyBatis的XML配置文件编写SQL语句
   - 所有表字段与实体类属性的映射关系都应在resultMap中定义
   - 使用动态SQL处理条件查询和可选字段更新

3. **数据库变更记录**
   - 所有数据库表结构的变更都应在`doc/数据库表结构文档.md`中记录
   - 变更记录应包括日期、表名、操作类型和描述

## 安全规范

1. **认证与授权**
   - 使用JWT进行用户认证
   - Token存储在Redis中，支持失效和刷新
   - 敏感接口必须进行身份验证

2. **数据安全**
   - 敏感信息（如密码）不应明文存储
   - API响应中不应返回敏感信息
   - 用户输入数据必须进行验证和过滤

## 错误处理规范

1. **异常处理**
   - 使用全局异常处理器处理异常
   - 返回统一格式的错误响应
   - 区分业务异常和系统异常

2. **日志记录**
   - 使用SLF4J进行日志记录
   - 错误日志应包含足够的上下文信息以便调试
   - 敏感信息不应记录在日志中

## 开发流程

1. **版本控制**
   - 使用Git进行版本控制
   - 功能开发使用feature分支
   - 提交信息应清晰描述变更内容

2. **代码审查**
   - 所有代码变更应经过审查
   - 关注代码质量、性能和安全性

3. **测试**
   - 编写单元测试和集成测试
   - 在提交前运行测试以确保代码质量

## 重要更新记录

| 日期       | 内容                                  | 开发者 |
| ---------- | ------------------------------------- | ------ |
| 2023-10-16 | 创建微信用户信息表                    | -      |
| 2024-08-26 | 增加生日、身高、体重字段              | -      |
| 2024-08-26 | 添加用户个人资料更新接口              | -      |
| 2024-08-26 | 添加根据openid获取用户详细信息接口    | -      |
| 2024-08-26 | 添加接口文档规范                      | -      |
| 2024-08-27 | 添加热量分析接口                      | -      |
| 2024-08-30 | 添加分页查询热量记录接口              | -      |
| 2024-09-01 | 添加断食记录相关接口                  | -      |
| 2024-09-13 | 升级Swagger 2到OpenAPI 3，优化API文档 | -      |
| 2024-09-13 | 调整项目结构，规范化类文件归类        | -      |
| 2024-09-30 | 完善DTO、VO、Entity的使用规范         | -      |

## 开发日志记录

### 2024-03-21 代码优化与规范化

2. 热量分析功能规范化
   - 标记：将旧版本的 `analyzeHeatFromText` 方法标记为 `@Deprecated`
   - 原因：新版本 `analyzeHeatFromTextV2` 提供了更多功能：
     * 支持营养素（碳水化合物、蛋白质、脂肪）分析
     * 支持体重信息记录
     * 更准确的热量计算
   - 建议：新代码应该使用 V2 版本的方法

### 2024-03-21 热量分析功能优化

1. 修复了热量分析功能中的JSON解析错误
   - 问题：解析大模型返回的JSON结果时，返回内容包含了Markdown代码块标记```json，导致解析失败
   - 修改：更新了`StopfoodPromptUtils.java`中的`parseBatchCalorieCalculationResponseV2`方法
   - 改进：
     * 添加了对Markdown代码块的处理，在解析前去除```json和```标记
     * 修复了字符串中的中文引号问题
     * 添加了错误日志记录功能，方便问题排查
   - 影响：提高了热量分析功能的稳定性和可靠性

## 文档维护规范

1. **规则同步要求**
   - 如果在开发日志中新增了任何开发规范、编码习惯或工作流程
   - 需要及时将这些规则同步到对应的规则文件中
   - 目前的规则文件包括：
     * `command-rule.mdc`：Windows环境下的命令行操作规范
     * 其他规则文件（待补充）
   - 确保规则文件和开发日志中的规范保持一致
   - 在提交代码时，检查是否需要更新相关规则文件

2. **文档更新流程**
   - 先在开发日志中记录变更
   - 检查变更是否涉及任何开发规范或工作习惯
   - 如果涉及，则同步更新对应的规则文件
   - 在提交时，将开发日志和规则文件的更新放在同一个提交中