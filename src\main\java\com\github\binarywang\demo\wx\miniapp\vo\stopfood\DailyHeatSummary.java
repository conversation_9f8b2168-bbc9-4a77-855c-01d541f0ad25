package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 每日热量汇总
 */
@Data
public class DailyHeatSummary {
    /**
     * 日期，格式：yyyy-MM-dd
     */
    private String formattedDate;

    /**
     * 总摄入热量
     */
    private Double totalIntake;

    /**
     * 总消耗热量
     */
    private Double totalConsumption;

    /**
     * 净热量（摄入-消耗）
     */
    private Double netHeat;

    /**
     * 当日热量记录明细
     */
    private List<HeatRecordDetail> details;

    @Data
    public static class HeatRecordDetail {
        /**
         * 事件名称
         */
        private String eventName;

        /**
         * 类型（摄入/消耗）
         */
        private String type;

        /**
         * 热量值
         */
        private Double heatValue;

        /**
         * 记录时间
         */
        private Date recordTime;
    }
}