package com.github.binarywang.demo.wx.miniapp.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.RedisUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Token认证过滤器
 */
@Slf4j
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final RedisUtils redisUtils;
    private final ObjectMapper objectMapper;

    // 不需要验证token的URL路径
    private final List<String> excludeUrls = Arrays.asList(
            "/wx/user/*/login",
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v3/api-docs",
            "/v3/api-docs/swagger-config",
            "/doc.html",
            "/webjars/**");

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public TokenAuthenticationFilter(JwtUtils jwtUtils, RedisUtils redisUtils, ObjectMapper objectMapper) {
        this.jwtUtils = jwtUtils;
        this.redisUtils = redisUtils;
        this.objectMapper = objectMapper;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();

        // 如果请求的路径不需要验证，则直接放行
        if (isExcludedUrl(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 从请求头中获取token
        String token = TokenUtils.getTokenFromRequest(request);
        if (!StringUtils.hasText(token)) {
            // 如果token为空，返回错误信息
            responseError(response, 401, "未提供认证令牌");
            return;
        }

        try {
            // 从token中获取openid
            String openid = jwtUtils.getOpenidFromToken(token);
            if (openid == null) {
                responseError(response, 401, "无效的认证令牌");
                return;
            }

            // 从Redis中获取存储的token
            String redisToken = (String) redisUtils.get(TokenUtils.getTokenKey(openid));
            if (redisToken == null) {
                responseError(response, 401, "认证令牌已过期或无效");
                return;
            }

            // 验证请求中的token是否与Redis中存储的token一致
            if (!token.equals(redisToken)) {
                responseError(response, 401, "认证令牌不匹配");
                return;
            }

            // 验证token是否有效
            if (!jwtUtils.validateToken(token)) {
                responseError(response, 401, "认证令牌已过期");
                return;
            }

            // 验证通过，继续执行过滤器链
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("Token验证异常：", e);
            responseError(response, 500, "服务器内部错误");
        }
    }

    /**
     * 判断请求是否不需要验证
     * 
     * @param requestURI 请求URI
     * @return 是否排除
     */
    private boolean isExcludedUrl(String requestURI) {
        for (String pattern : excludeUrls) {
            if (pathMatcher.match(pattern, requestURI)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回错误信息
     * 
     * @param response HTTP响应
     * @param code     错误码
     * @param message  错误信息
     * @throws IOException IO异常
     */
    private void responseError(HttpServletResponse response, int code, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(code);
        response.getWriter().write(objectMapper.writeValueAsString(ApiResponse.error(code, message)));
    }
}