package com.github.binarywang.demo.wx.miniapp.service.stopfood;

import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.DailyHeatSummary;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.HeatAnalysisResult;

import java.util.*;

/**
 * 热量分析服务接口
 */
public interface HeatAnalysisService {

    /**
     * 分析文本中的热量摄入和消耗信息
     *
     * @param content 需要分析的文本内容
     * @return 热量分析结果列表
     * @deprecated 请使用 {@link #analyzeHeatFromTextV2(String)} 代替。
     *             新版本提供了更准确的热量分析，并增加了营养素（碳水化合物、蛋白质、脂肪）和体重信息的支持。
     */
    @Deprecated
    List<HeatAnalysisResult> analyzeHeatFromText(String content);

    /**
     * 从文本中分析热量信息（V2版本，包含营养素和体重信息）
     */
    List<HeatAnalysisResult> analyzeHeatFromTextV2(String content);

    /**
     * 获取用户今日的热量记录
     *
     * @param openId 微信openid
     * @return 今日热量记录列表
     * @deprecated 请使用 {@link #getTodayHeatRecordsAndNutrients(String)} 代替。
     *             新版本提供了营养素信息（碳水化合物、蛋白质、脂肪）的支持。
     */
    @Deprecated
    List<HeatAnalysisResult> getTodayHeatRecords(String openId);

    /**
     * 获取用户今日的热量记录及营养素
     *
     * @param openId 微信openid
     * @return 今日热量记录列表，包含营养素信息
     */
    List<HeatAnalysisResult> getTodayHeatRecordsAndNutrients(String openId);

    /**
     * 分页获取用户每天的热量记录
     *
     * @param openId   微信openid
     * @param pageNum  页码，从1开始
     * @param pageSize 每页记录数
     * @return 分页结果，包含每天的热量汇总
     */
    PageResult<DailyHeatSummary> getDailyHeatSummaryByPage(String openId, Integer pageNum, Integer pageSize);

    /**
     * 精确计算热量
     * 
     * @param type      摄入热量 或 消耗热量
     * @param eventName 事件名称
     * @param unit      单位
     * @param quantity  数量
     * @return 热量值
     */
    @Deprecated
    Integer accurateCalculationOfHeat(String type, String eventName, String unit, Integer quantity);

    HeatAnalysisResult accurateCalculateHeatAndNutrient(String type, String eventName, String unit, Integer quantity);
}