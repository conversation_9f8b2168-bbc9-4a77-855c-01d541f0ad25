package com.github.binarywang.demo.wx.miniapp.service.memorise_words;

import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.MwCompareResultRequest;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwCompareResultVO;

/**
 * 记忆单词-比对结果Service接口
 */
public interface MemoryWordsCompareResultService {

    /**
     * 新增比对结果
     * 
     * @param request 比对结果请求参数
     * @param openId  用户openId
     * @param unionId 用户unionId
     * @return 比对结果
     */
    MwCompareResultVO addCompareResult(MwCompareResultRequest request, String openId, String unionId);

    /**
     * 分页查询比对结果
     * 
     * @param openId     open_id
     * @param relationId relation_id，可为null
     * @param pageNum    页码（从1开始）
     * @param pageSize   每页大小
     * @return 分页结果
     */
    com.github.binarywang.demo.wx.miniapp.vo.PageResult<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult> pageCompareResults(
            String openId, Long relationId, Integer pageNum, Integer pageSize);

    /**
     * 根据id查询单条比对结果
     * 
     * @param id 比对结果主键id
     * @return 比对结果VO
     */
    MwCompareResultVO getCompareResultById(Long id);
}