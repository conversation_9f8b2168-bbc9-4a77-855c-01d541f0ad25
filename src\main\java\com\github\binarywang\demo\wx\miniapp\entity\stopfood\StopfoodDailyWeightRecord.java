package com.github.binarywang.demo.wx.miniapp.entity.stopfood;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopfoodDailyWeightRecord {
    private Long id;
    private String openId;
    private String unionId;
    private String recordDate;
    private Double weight;
    private Date createTime;
    private Date updateTime;
}