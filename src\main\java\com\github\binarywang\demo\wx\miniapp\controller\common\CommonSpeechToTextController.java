package com.github.binarywang.demo.wx.miniapp.controller.common;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.utils.baidu.BaiduSpeechToTextUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Base64;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/common/speechToText")
@Tag(name = "通用-语音转文字")
public class CommonSpeechToTextController {

    @Autowired
    private JwtUtils jwtUtils;

    @PostMapping("/baidu")
    @Operation(summary = "语音文件上传转文字", description = "上传语音文件并转换为文本，支持多种音频格式", responses = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回转换结果", content = @Content(mediaType = "application/json"))
    })
    public ApiResponse<String> convertSpeechToText(
            @Parameter(description = "要上传的语音文件", required = true) @RequestParam("file") MultipartFile file,
            @Parameter(description = "音频格式，如wav、pcm等", example = "wav") @RequestParam(value = "format", defaultValue = "pcm") String format,
            HttpServletRequest httpRequest) {
        try {
            // 验证token并获取openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            if (StringUtils.isBlank(token)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            String tokenOpenId = jwtUtils.getOpenidFromToken(token);
            if (StringUtils.isBlank(tokenOpenId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED, "无效的用户身份");
            }

            // 获取机器唯一标识，这里使用计算机名+用户名
            String computerName = System.getenv("COMPUTERNAME");
            String userName = System.getProperty("user.name");
            String cuid = (computerName + "_" + userName).replaceAll("[^a-zA-Z0-9]", "");

            // 将文件转为Base64编码
            byte[] bytes = file.getBytes();
            String base64String = Base64.getEncoder().encodeToString(bytes);

            // 调用BaiduSpeechToTextUtils进行转换
            String result = BaiduSpeechToTextUtils.soundToWord(format, cuid, base64String, bytes.length);
            return ApiResponse.success(result);
        } catch (IOException e) {
            log.error("语音转文字失败", e);
            return ApiResponse.error(500, "语音转文字失败: " + e.getMessage());
        }
    }

}
