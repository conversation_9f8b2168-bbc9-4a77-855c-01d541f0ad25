package com.github.binarywang.demo.wx.miniapp.service.impl.common;

import com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions;
import com.github.binarywang.demo.wx.miniapp.mapper.common.CommonUserSuggestionsMapper;
import com.github.binarywang.demo.wx.miniapp.service.common.CommonUserSuggestionsService;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 通用-用户建议表Service实现类
 */
@Service
@Slf4j
public class CommonUserSuggestionsServiceImpl implements CommonUserSuggestionsService {

    @Autowired
    private CommonUserSuggestionsMapper commonUserSuggestionsMapper;

    @Override
    public Long saveSuggestion(String openId, String unionId, String appId, String suggestion) {
        try {
            log.info("保存用户建议，openId:{}，appId:{}，suggestion:{}", openId, appId, suggestion);

            CommonUserSuggestions entity = CommonUserSuggestions.builder()
                    .openId(openId)
                    .unionId(unionId)
                    .appId(appId)
                    .suggestion(suggestion)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();

            int result = commonUserSuggestionsMapper.insert(entity);
            if (result > 0) {
                log.info("用户建议保存成功，记录ID:{}", entity.getId());
                return entity.getId();
            } else {
                log.error("用户建议保存失败");
                return null;
            }
        } catch (Exception e) {
            log.error("保存用户建议失败", e);
            return null;
        }
    }

    @Override
    public CommonUserSuggestions getSuggestionById(Long id) {
        try {
            log.info("根据ID查询用户建议，id:{}", id);
            return commonUserSuggestionsMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID查询用户建议失败", e);
            return null;
        }
    }

    @Override
    public List<CommonUserSuggestions> getSuggestionsByOpenId(String openId) {
        try {
            log.info("根据openId查询用户建议列表，openId:{}", openId);
            return commonUserSuggestionsMapper.selectByOpenId(openId);
        } catch (Exception e) {
            log.error("根据openId查询用户建议列表失败", e);
            return null;
        }
    }

    @Override
    public PageResult<CommonUserSuggestions> getSuggestionsByPage(String openId, Integer pageNum, Integer pageSize) {
        try {
            log.info("分页查询用户建议列表，openId:{}，pageNum:{}，pageSize:{}", openId, pageNum, pageSize);

            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;

            // 查询总数
            Long total = commonUserSuggestionsMapper.countByOpenId(openId);

            // 查询数据
            List<CommonUserSuggestions> list = commonUserSuggestionsMapper.selectByOpenIdWithPage(openId, offset,
                    pageSize);

            PageResult<CommonUserSuggestions> result = new PageResult<>();
            result.setList(list);
            result.setTotal(total);
            result.setPageNum(pageNum);
            result.setPageSize(pageSize);

            // 计算总页数
            int pages = (int) Math.ceil((double) total / pageSize);
            result.setPages(pages);

            return result;
        } catch (Exception e) {
            log.error("分页查询用户建议列表失败", e);
            return null;
        }
    }

    @Override
    public boolean deleteSuggestion(Long id, String openId) {
        try {
            log.info("删除用户建议，id:{}，openId:{}", id, openId);
            int result = commonUserSuggestionsMapper.deleteByIdAndOpenId(id, openId);
            boolean success = result > 0;
            log.info("删除用户建议结果:{}", success ? "成功" : "失败");
            return success;
        } catch (Exception e) {
            log.error("删除用户建议失败", e);
            return false;
        }
    }
}