package com.github.binarywang.demo.wx.miniapp.constant;

/**
 * API响应常量类
 */
public class ApiResponseConstant {

    /**
     * 响应状态码
     */
    public static class Code {
        /**
         * 成功状态码
         */
        public static final Integer SUCCESS = 0;

        /**
         * 参数错误
         */
        public static final Integer PARAM_ERROR = 400;

        /**
         * 未授权
         */
        public static final Integer UNAUTHORIZED = 401;

        /**
         * 禁止访问
         */
        public static final Integer FORBIDDEN = 403;

        /**
         * 资源不存在
         */
        public static final Integer NOT_FOUND = 404;

        /**
         * 系统内部错误
         */
        public static final Integer INTERNAL_ERROR = 500;

        /**
         * 业务处理失败
         */
        public static final Integer BUSINESS_ERROR = 10000;
    }

    /**
     * 响应消息
     */
    public static class Message {
        /**
         * 成功消息
         */
        public static final String SUCCESS = "success";

        /**
         * 参数错误消息
         */
        public static final String PARAM_ERROR = "参数错误";

        /**
         * 未授权消息
         */
        public static final String UNAUTHORIZED = "未授权";

        /**
         * 禁止访问消息
         */
        public static final String FORBIDDEN = "禁止访问";

        /**
         * 资源不存在消息
         */
        public static final String NOT_FOUND = "资源不存在";

        /**
         * 系统内部错误消息
         */
        public static final String INTERNAL_ERROR = "系统内部错误";

        /**
         * 业务处理失败消息
         */
        public static final String BUSINESS_ERROR = "业务处理失败";
    }
}