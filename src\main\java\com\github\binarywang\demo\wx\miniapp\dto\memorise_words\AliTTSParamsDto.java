package com.github.binarywang.demo.wx.miniapp.dto.memorise_words;

import lombok.Data;

/**
 * 语音合成请求参数
 */
@Data
public class AliTTSParamsDto {

    /** 环境，可选值为"中文"或"英文"，默认为"中文" */
    private String environment = "中文";

    /**
     * 采样率: 8000, 16000
     * 默认16000
     */
    private Integer sampleRate = 16000;

    /**
     * 发音人-key，如普通话、英式英语、美式英语
     * 默认普通话
     * 实际值会通过memory_words_ali_tones表映射为对应的API参数值
     * 
     * https://ai.aliyun.com/nls/tts?spm=5176.12061031.J_3674916160.7.11116822R9qMQI
     * https://help.aliyun.com/zh/isi/developer-reference/overview-of-speech-synthesis?spm=a2c4g.11186623.0.0.4ec7807cQS8Y5M#5186fe1abb7ag
     */
    private String voiceKey = "普通话";

    /**
     * 音量0-100
     * 默认50
     */
    private Integer volume = 50;

    /**
     * 语速-key，慢速，中速，快速
     * 默认慢速
     * 实际值会通过memory_words_ali_tones表映射为对应的API参数值
     */
    private String speechRateKey = "慢速";

    /** 要合成的文本内容 */
    private String textContent;

    /**
     * 重复次数，仅在textType=ssml时生效
     * 用于指定文本重复播放的次数
     */
    private Integer repeatCount = 1;

    /**
     * 停顿秒数，仅在textType=ssml时生效
     * 用于在SSML标记中指定停顿的时长，单位为秒
     */
    private Float pauseSeconds = 1.0f;

}
