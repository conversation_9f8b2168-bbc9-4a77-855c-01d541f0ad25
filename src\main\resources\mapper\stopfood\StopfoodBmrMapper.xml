<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodBmrMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr" id="StopfoodBmrResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="bmrValue" column="bmr_value"/>
        <result property="generationMode" column="generation_mode"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectStopfoodBmrVo">
        select id, open_id, union_id, bmr_value, generation_mode, create_time, update_time
        from stopfood_bmr
    </sql>

    <!-- 根据openId查询基础代谢 -->
    <select id="selectByOpenId" parameterType="String" resultMap="StopfoodBmrResult">
        <include refid="selectStopfoodBmrVo"/>
        where open_id = #{openId}
        limit 1
    </select>

    <!-- 插入新的基础代谢记录 -->
    <insert id="insert" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr" useGeneratedKeys="true" keyProperty="id">
        insert into stopfood_bmr
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="bmrValue != null">bmr_value,</if>
            <if test="generationMode != null">generation_mode,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="bmrValue != null">#{bmrValue},</if>
            <if test="generationMode != null">#{generationMode},</if>
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP()
        </trim>
    </insert>

    <!-- 根据ID更新基础代谢 -->
    <update id="updateById" parameterType="com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr">
        update stopfood_bmr
        <set>
            <if test="bmrValue != null">bmr_value = #{bmrValue},</if>
            <if test="generationMode != null">generation_mode = #{generationMode},</if>
            update_time = CURRENT_TIMESTAMP()
        </set>
        where id = #{id} and open_id = #{openId}
    </update>

</mapper> 