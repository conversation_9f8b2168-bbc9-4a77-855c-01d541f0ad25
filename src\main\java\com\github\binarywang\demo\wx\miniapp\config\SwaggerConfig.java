package com.github.binarywang.demo.wx.miniapp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

/**
 * API文档配置类
 */
@Configuration
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true")
public class SwaggerConfig {

    @Value("${swagger.info.title:微信小程序服务端API}")
    private String title;

    @Value("${swagger.info.description:微信小程序服务端接口文档}")
    private String description;

    @Value("${swagger.info.version:1.0.0}")
    private String version;

    @Value("${swagger.info.contact.name:<PERSON><PERSON>}")
    private String contactName;

    @Value("${swagger.info.contact.url:https://github.com/binarywang}")
    private String contactUrl;

    @Value("${swagger.info.contact.email:<EMAIL>}")
    private String contactEmail;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .description(description)
                        .version(version)
                        .contact(new Contact()
                                .name(contactName)
                                .url(contactUrl)
                                .email(contactEmail)));
    }
}