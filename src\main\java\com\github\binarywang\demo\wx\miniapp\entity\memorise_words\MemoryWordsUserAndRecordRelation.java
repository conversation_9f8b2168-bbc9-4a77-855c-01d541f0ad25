package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 记忆单词-用户与录音关系表实体类
 */
@Data
public class MemoryWordsUserAndRecordRelation {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionid
     */
    private String unionId;

    /**
     * 录音id，关联memory_words_tts_record表的主键
     */
    private Long recordId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}