package com.github.binarywang.demo.wx.miniapp.mapper.common;

import com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用-用户建议表Mapper接口
 */
@Mapper
public interface CommonUserSuggestionsMapper {

    /**
     * 插入用户建议
     * 
     * @param suggestion 用户建议对象
     * @return 影响行数
     */
    int insert(CommonUserSuggestions suggestion);

    /**
     * 根据ID查询用户建议
     * 
     * @param id 记录ID
     * @return 用户建议对象
     */
    CommonUserSuggestions selectById(@Param("id") Long id);

    /**
     * 根据openId查询用户建议列表
     * 
     * @param openId 微信openid
     * @return 用户建议列表
     */
    List<CommonUserSuggestions> selectByOpenId(@Param("openId") String openId);

    /**
     * 分页查询用户建议列表
     * 
     * @param openId   微信openid
     * @param offset   偏移量
     * @param pageSize 每页大小
     * @return 用户建议列表
     */
    List<CommonUserSuggestions> selectByOpenIdWithPage(@Param("openId") String openId,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize);

    /**
     * 统计用户建议总数
     * 
     * @param openId 微信openid
     * @return 总数
     */
    Long countByOpenId(@Param("openId") String openId);

    /**
     * 根据ID和openId删除用户建议
     * 
     * @param id     记录ID
     * @param openId 微信openid
     * @return 影响行数
     */
    int deleteByIdAndOpenId(@Param("id") Long id, @Param("openId") String openId);
}