<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.github.binarywang.demo.wx.miniapp.mapper.common.CommonUserSuggestionsMapper">

    <resultMap type="com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions" id="CommonUserSuggestionsResult">
        <id property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="appId" column="app_id"/>
        <result property="suggestion" column="suggestion"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCommonUserSuggestionsVo">
        select id, open_id, union_id, app_id, suggestion, create_time, update_time
        from common_user_suggestions
    </sql>

    <!-- 插入用户建议 -->
    <insert id="insert" parameterType="com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions" useGeneratedKeys="true" keyProperty="id">
        insert into common_user_suggestions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="suggestion != null and suggestion != ''">suggestion,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="suggestion != null and suggestion != ''">#{suggestion},</if>
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        </trim>
    </insert>

    <!-- 根据ID查询用户建议 -->
    <select id="selectById" parameterType="Long" resultMap="CommonUserSuggestionsResult">
        <include refid="selectCommonUserSuggestionsVo"/>
        where id = #{id}
    </select>

    <!-- 根据openId查询用户建议列表 -->
    <select id="selectByOpenId" parameterType="String" resultMap="CommonUserSuggestionsResult">
        <include refid="selectCommonUserSuggestionsVo"/>
        where open_id = #{openId}
        order by create_time desc
    </select>

    <!-- 分页查询用户建议列表 -->
    <select id="selectByOpenIdWithPage" resultMap="CommonUserSuggestionsResult">
        <include refid="selectCommonUserSuggestionsVo"/>
        where open_id = #{openId}
        order by create_time desc
        limit #{offset}, #{pageSize}
    </select>

    <!-- 统计用户建议总数 -->
    <select id="countByOpenId" parameterType="String" resultType="Long">
        select count(*) from common_user_suggestions
        where open_id = #{openId}
    </select>

    <!-- 根据ID和openId删除用户建议 -->
    <delete id="deleteByIdAndOpenId">
        delete from common_user_suggestions
        where id = #{id} and open_id = #{openId}
    </delete>

</mapper> 