package com.github.binarywang.demo.wx.miniapp.service.impl.memorise_words;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.ArrayList;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsUserAndRecordRelation;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecordWithRelation;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsUserAndRecordRelationMapper;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsUserAndRecordRelationService;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTtsRecordWithRelationVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 记忆单词-用户与录音关系表Service实现类
 */
@Service
@Slf4j
public class MemoryWordsUserAndRecordRelationServiceImpl implements MemoryWordsUserAndRecordRelationService {

    @Autowired
    private MemoryWordsUserAndRecordRelationMapper relationMapper;

    @Override
    public Long saveRelation(MemoryWordsUserAndRecordRelation relation) {
        try {
            // 先检查是否已存在绑定关系
            MemoryWordsUserAndRecordRelation existingRelation = relationMapper.selectByOpenIdAndRecordId(
                    relation.getOpenId(), relation.getRecordId());

            if (existingRelation != null) {
                // 如果已存在绑定关系，直接返回已存在记录的ID
                log.info("用户[{}]与录音[{}]的绑定关系已存在，记录ID：{}",
                        relation.getOpenId(), relation.getRecordId(), existingRelation.getId());
                return existingRelation.getId();
            }

            // 如果不存在，则插入新记录
            relationMapper.insert(relation);
            log.info("用户[{}]与录音[{}]的绑定关系创建成功，记录ID：{}",
                    relation.getOpenId(), relation.getRecordId(), relation.getId());
            return relation.getId();
        } catch (Exception e) {
            log.error("保存用户与录音关系记录失败", e);
            return null;
        }
    }

    @Override
    public boolean hasPermission(String openId, Long recordId) {
        try {
            MemoryWordsUserAndRecordRelation relation = relationMapper.selectByOpenIdAndRecordId(openId, recordId);
            return relation != null;
        } catch (Exception e) {
            log.error("检查用户权限失败, openId={}, recordId={}", openId, recordId, e);
            return false;
        }
    }

    @Override
    public PageResult<MemoryWordsTtsRecord> getRecordsByOpenIdAndContent(String openId, String content, Integer page,
            Integer size) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询数据列表
            List<MemoryWordsTtsRecord> records = relationMapper.selectRecordsByOpenIdAndContent(openId, content, offset,
                    size);

            // 查询总数
            long total = relationMapper.countRecordsByOpenIdAndContent(openId, content);

            // 计算总页数
            int pages = (int) Math.ceil((double) total / size);

            // 构建分页结果
            PageResult<MemoryWordsTtsRecord> pageResult = new PageResult<>();
            pageResult.setList(records != null ? records : new ArrayList<>());
            pageResult.setTotal(total);
            pageResult.setPages(pages);
            pageResult.setPageNum(page);
            pageResult.setPageSize(size);

            return pageResult;
        } catch (Exception e) {
            log.error("分页查询用户可访问的录音记录失败, openId={}, content={}, page={}, size={}", openId, content, page, size, e);

            // 返回空的分页结果
            PageResult<MemoryWordsTtsRecord> emptyResult = new PageResult<>();
            emptyResult.setList(new ArrayList<>());
            emptyResult.setTotal(0L);
            emptyResult.setPages(0);
            emptyResult.setPageNum(page);
            emptyResult.setPageSize(size);
            return emptyResult;
        }
    }

    @Override
    public PageResult<MwTtsRecordWithRelationVO> getRecordsWithRelationIdByOpenIdAndContent(String openId,
            String content, Integer page,
            Integer size) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询数据列表（包含关系ID）
            List<MemoryWordsTtsRecordWithRelation> records = relationMapper
                    .selectRecordsWithRelationIdByOpenIdAndContent(openId,
                            content, offset,
                            size);

            // 查询总数
            long total = relationMapper.countRecordsByOpenIdAndContent(openId, content);

            // 计算总页数
            int pages = (int) Math.ceil((double) total / size);

            // 转换为VO对象
            List<MwTtsRecordWithRelationVO> voList = new ArrayList<>();
            if (records != null) {
                for (MemoryWordsTtsRecordWithRelation record : records) {
                    MwTtsRecordWithRelationVO vo = new MwTtsRecordWithRelationVO();
                    // 复制所有字段
                    vo.setId(record.getId());
                    vo.setOpenId(record.getOpenId());
                    vo.setUnionId(record.getUnionId());
                    vo.setApiType(record.getApiType());
                    vo.setEnvironment(record.getEnvironment());
                    vo.setVoiceType(record.getVoiceType());
                    vo.setVoiceValue(record.getVoiceValue());
                    vo.setTextContent(record.getTextContent());
                    vo.setTextType(record.getTextType());
                    vo.setSpeedKey(record.getSpeedKey());
                    vo.setSpeedValue(record.getSpeedValue());
                    vo.setRepeatCount(record.getRepeatCount());
                    vo.setPauseSeconds(record.getPauseSeconds());
                    vo.setAudioUrl(record.getAudioUrl());
                    vo.setAudioDuration(record.getAudioDuration());
                    vo.setStatus(record.getStatus());
                    vo.setErrorMsg(record.getErrorMsg());
                    vo.setCreateTime(record.getCreateTime());
                    vo.setUpdateTime(record.getUpdateTime());

                    // 设置关系ID
                    vo.setRelationId(record.getRelationId());

                    voList.add(vo);
                }
            }

            // 构建分页结果
            PageResult<MwTtsRecordWithRelationVO> pageResult = new PageResult<>();
            pageResult.setList(voList);
            pageResult.setTotal(total);
            pageResult.setPages(pages);
            pageResult.setPageNum(page);
            pageResult.setPageSize(size);

            return pageResult;
        } catch (Exception e) {
            log.error("分页查询用户可访问的录音记录（包含关系ID）失败, openId={}, content={}, page={}, size={}", openId, content, page, size,
                    e);

            // 返回空的分页结果
            PageResult<MwTtsRecordWithRelationVO> emptyResult = new PageResult<>();
            emptyResult.setList(new ArrayList<>());
            emptyResult.setTotal(0L);
            emptyResult.setPages(0);
            emptyResult.setPageNum(page);
            emptyResult.setPageSize(size);
            return emptyResult;
        }
    }

    @Override
    public MemoryWordsTtsRecord getRecordByOpenIdAndRecordId(String openId, Long recordId) {
        try {
            return relationMapper.selectRecordByOpenIdAndRecordId(openId, recordId);
        } catch (Exception e) {
            log.error("根据openId和recordId查询录音记录详情失败, openId={}, recordId={}", openId, recordId, e);
            return null;
        }
    }

    @Override
    public int deleteRelationsByOpenIdAndRecordIds(String openId, List<Long> recordIds) {
        try {
            return relationMapper.deleteByOpenIdAndRecordIds(openId, recordIds);
        } catch (Exception e) {
            log.error("删除用户与录音的关系记录失败, openId={}, recordIds={}", openId, recordIds, e);
            return 0;
        }
    }
}