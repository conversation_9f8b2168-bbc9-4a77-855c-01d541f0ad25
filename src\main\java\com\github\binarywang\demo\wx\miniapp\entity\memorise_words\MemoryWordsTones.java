package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 记忆单词-音色表实体类
 */
@Data
public class MemoryWordsTones {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 环境，可选值为"中文"或"英文"
     */
    private String environment;

    /**
     * 音色类型-key，如普通话、英式英语、美式英语
     */
    private String voiceTypeKey;

    /**
     * 音色类型-value，参考https://www.volcengine.com/docs/6561/97465
     */
    private String voiceTypeValue;

    /**
     * 语速-key，慢速，中速，快速
     */
    private String speedRatioKey;

    /**
     * 语速-value
     */
    private Float speedRatioValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除，默认是否
     */
    private String isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}