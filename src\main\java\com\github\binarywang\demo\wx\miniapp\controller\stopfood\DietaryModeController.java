package com.github.binarywang.demo.wx.miniapp.controller.stopfood;

import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.TokenUtils;
import com.github.binarywang.demo.wx.miniapp.vo.ApiResponse;
import com.github.binarywang.demo.wx.miniapp.constant.ApiResponseConstant;
import com.github.binarywang.demo.wx.miniapp.dto.stopfood.DietaryModeRequest;
import com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDietaryMode;
import com.github.binarywang.demo.wx.miniapp.mapper.stopfood.StopfoodDietaryModeMapper;
import com.github.binarywang.demo.wx.miniapp.vo.stopfood.DietaryModeVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 饮食模式控制器
 */
@RestController
@Slf4j
@RequestMapping("/wx/dietaryMode/{appid}")
@Tag(name = "饮食模式接口")
public class DietaryModeController {

    private final StopfoodDietaryModeMapper dietaryModeMapper;
    private final JwtUtils jwtUtils;

    @Autowired
    public DietaryModeController(
            StopfoodDietaryModeMapper dietaryModeMapper,
            JwtUtils jwtUtils) {
        this.dietaryModeMapper = dietaryModeMapper;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 设置饮食模式
     */
    @Operation(summary = "设置饮食模式", description = "新增或更新用户的饮食模式，表中存在openId为更新，表中没有openId为新增", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    }, requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "饮食模式设置请求", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = DietaryModeRequest.class))))
    @PostMapping("/setDietaryMode")
    public ApiResponse<DietaryModeVO> setDietaryMode(
            @PathVariable String appid,
            @Valid @RequestBody DietaryModeRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户是否已有饮食模式
            StopfoodDietaryMode existingDietaryMode = dietaryModeMapper.selectByOpenId(openId);

            if (existingDietaryMode != null) {
                // 更新已有饮食模式
                existingDietaryMode.setMode(request.getMode());
                existingDietaryMode.setCoefficient(request.getCoefficient());
                dietaryModeMapper.updateById(existingDietaryMode);
                log.info("用户[{}]更新饮食模式: 模式[{}], 系数[{}]", openId, request.getMode(),
                        request.getCoefficient());
                return ApiResponse.success("更新饮食模式成功", DietaryModeVO.fromEntity(existingDietaryMode));
            } else {
                // 创建新饮食模式
                StopfoodDietaryMode newDietaryMode = new StopfoodDietaryMode();
                newDietaryMode.setOpenId(openId);
                newDietaryMode.setMode(request.getMode());
                newDietaryMode.setCoefficient(request.getCoefficient());
                dietaryModeMapper.insert(newDietaryMode);
                log.info("用户[{}]新增饮食模式: 模式[{}], 系数[{}]", openId, request.getMode(),
                        request.getCoefficient());
                return ApiResponse.success("设置饮食模式成功", DietaryModeVO.fromEntity(newDietaryMode));
            }
        } catch (Exception e) {
            log.error("设置饮食模式出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "设置饮食模式失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的饮食模式
     */
    @Operation(summary = "获取饮食模式", description = "根据用户的openId获取饮食模式", parameters = {
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH)
    })
    @PostMapping("/getDietaryMode")
    public ApiResponse<DietaryModeVO> getDietaryMode(
            @PathVariable String appid,
            HttpServletRequest httpRequest) {
        try {
            // 从token中获取用户openid
            String token = TokenUtils.getTokenFromRequest(httpRequest);
            String openId = null;

            if (StringUtils.isNotBlank(token)) {
                openId = jwtUtils.getOpenidFromToken(token);
            }

            if (StringUtils.isBlank(openId)) {
                return ApiResponse.error(ApiResponseConstant.Code.UNAUTHORIZED,
                        ApiResponseConstant.Message.UNAUTHORIZED);
            }

            // 查询用户的饮食模式
            StopfoodDietaryMode dietaryMode = dietaryModeMapper.selectByOpenId(openId);

            if (dietaryMode != null) {
                return ApiResponse.success("获取饮食模式成功", DietaryModeVO.fromEntity(dietaryMode));
            } else {
                return ApiResponse.success("用户暂无饮食模式", null);
            }
        } catch (Exception e) {
            log.error("获取饮食模式出错", e);
            return ApiResponse.error(ApiResponseConstant.Code.INTERNAL_ERROR, "获取饮食模式失败: " + e.getMessage());
        }
    }
}