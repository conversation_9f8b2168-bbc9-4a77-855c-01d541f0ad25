package com.github.binarywang.demo.wx.miniapp.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小程序登录结果对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResult {

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 用户的唯一标识
     */
    private String openId;

    /**
     * 用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回
     */
    private String unionId;

    /**
     * JWT令牌
     */
    private String token;
}