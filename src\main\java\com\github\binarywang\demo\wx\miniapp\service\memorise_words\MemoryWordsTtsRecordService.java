package com.github.binarywang.demo.wx.miniapp.service.memorise_words;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;
import java.util.List;

/**
 * 记忆单词-TTS语音合成记录Service接口
 */
public interface MemoryWordsTtsRecordService {

    /**
     * 保存TTS语音合成记录
     * 
     * @param record TTS记录
     * @return 保存后的记录ID
     */
    Long saveRecord(MemoryWordsTtsRecord record);

    /**
     * 更新TTS语音合成记录
     * 
     * @param record TTS记录
     * @return 是否更新成功
     */
    boolean updateRecord(MemoryWordsTtsRecord record);

    /**
     * 根据openId和文本内容分页查询TTS记录
     * 
     * @param openId  用户openId
     * @param content 文本内容关键词(可为null)
     * @param page    页码(从0开始)
     * @param size    每页大小
     * @return 分页结果
     */
    PageResult<MemoryWordsTtsRecord> getRecordsByOpenIdAndContent(String openId, String content, Integer page,
            Integer size);

    /**
     * 根据ID和openId查询单条TTS记录
     * 
     * @param id     记录ID
     * @param openId 用户openId
     * @return TTS记录实体，如果不存在则返回null
     */
    MemoryWordsTtsRecord getRecordByIdAndOpenId(Long id, String openId);

    /**
     * 根据ID集合和openId删除TTS记录
     * 
     * @param ids    记录ID集合
     * @param openId 用户openId
     * @return 删除的记录数量
     */
    int deleteRecordsByIdsAndOpenId(List<Long> ids, String openId);
}