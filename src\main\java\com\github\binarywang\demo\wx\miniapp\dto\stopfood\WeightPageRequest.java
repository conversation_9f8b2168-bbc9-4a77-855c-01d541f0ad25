package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 体重分页查询请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "体重分页查询请求")
public class WeightPageRequest {

    @Schema(description = "页码，从1开始，默认为1", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页记录数，默认为7（最近7天），最大为21天", example = "7")
    @Min(value = 1, message = "每页记录数必须大于0")
    @Max(value = 21, message = "每页记录数不能超过21天")
    private Integer pageSize = 7;

    /**
     * 获取页码，如果为空则返回默认值1
     */
    public Integer getPageNum() {
        if (pageNum == null || pageNum <= 0) {
            return 1;
        }
        return pageNum;
    }

    /**
     * 获取每页记录数，如果为空则返回默认值7
     */
    public Integer getPageSize() {
        if (pageSize == null || pageSize <= 0) {
            return 7;
        }
        if (pageSize > 21) {
            return 21;
        }
        return pageSize;
    }
}