package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTextbook;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwTextbookTitleVO;

/**
 * 记忆单词-课本单词Mapper接口
 */
@Mapper
public interface MemoryWordsTextbookMapper {
        /**
         * 查询记忆单词-课本单词列表
         * 
         * @return 记忆单词-课本单词列表
         */
        List<MemoryWordsTextbook> selectMemoryWordsTextbookList();

        /**
         * 根据ID查询记忆单词-课本单词
         * 
         * @param id 记忆单词-课本单词ID
         * @return 记忆单词-课本单词
         */
        MemoryWordsTextbook selectMemoryWordsTextbookById(Long id);

        /**
         * 根据条件查询记忆单词-课本单词
         * 
         * @param subject         学科
         * @param textbookVersion 教材版本
         * @param grade           年级
         * @param term            学期
         * @param unitInfo        单元信息
         * @param lessonTitle     课文标题
         * @return 记忆单词-课本单词
         */
        MemoryWordsTextbook selectMemoryWordsTextbookByCondition(@Param("subject") String subject,
                        @Param("textbookVersion") String textbookVersion,
                        @Param("grade") Integer grade,
                        @Param("term") Integer term,
                        @Param("unitInfo") String unitInfo,
                        @Param("lessonTitle") String lessonTitle);

        /**
         * 新增记忆单词-课本单词
         * 
         * @param memoryWordsTextbook 记忆单词-课本单词
         * @return 结果
         */
        int insertMemoryWordsTextbook(MemoryWordsTextbook memoryWordsTextbook);

        /**
         * 修改记忆单词-课本单词
         * 
         * @param memoryWordsTextbook 记忆单词-课本单词
         * @return 结果
         */
        int updateMemoryWordsTextbook(MemoryWordsTextbook memoryWordsTextbook);

        /**
         * 删除记忆单词-课本单词
         * 
         * @param id 记忆单词-课本单词ID
         * @return 结果
         */
        int deleteMemoryWordsTextbookById(Long id);

        /**
         * 根据条件查询课文标题列表
         *
         * @param subject         学科
         * @param textbookVersion 教材版本
         * @param grade           年级
         * @param term            学期
         * @return 课文标题列表
         */
        List<MwTextbookTitleVO> selectLessonTitlesByCondition(@Param("subject") String subject,
                        @Param("textbookVersion") String textbookVersion,
                        @Param("grade") Integer grade,
                        @Param("term") Integer term);

        /**
         * 根据ID列表查询记忆单词-课本单词记录
         *
         * @param ids ID列表
         * @return 记忆单词-课本单词记录列表
         */
        List<MemoryWordsTextbook> selectMemoryWordsTextbookByIds(@Param("ids") List<Long> ids);
}