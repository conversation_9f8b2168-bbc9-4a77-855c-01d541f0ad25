# 记忆单词比对结果接口说明

## 接口概述

新增了记忆单词比对结果功能，用于将用户默写的内容与原始内容进行比对，并保存比对结果。

## 接口详情

### 新增比对结果接口

**接口地址：** `POST /memoriseWords/compareResult/addCompareResult`

**接口描述：** 根据relationId获取原始内容，与用户输入内容进行比对，并保存结果

**请求参数：**
```json
{
  "relationId": 123,
  "userWords": "hello\nworld\ntest"
}
```

**参数说明：**
- `relationId`: memory_words_user_and_record_relation表的主键id（必填）
- `userWords`: OCR识别的用户默写内容，按"\n"分割（必填）

**响应示例：**
```json
{
  "code": 200,
  "message": "比对结果保存成功",
  "data": {
    "summary": {
      "totalCount": 3,
      "correctCount": 2,
      "wrongCount": 1,
      "blankCount": 0
    },
    "details": [
      {
        "originalWord": "hello",
        "userWord": "hello",
        "result": "对"
      },
      {
        "originalWord": "world",
        "userWord": "word",
        "result": "错"
      },
      {
        "originalWord": "test",
        "userWord": "test",
        "result": "对"
      }
    ]
  }
}
```

## 业务逻辑

1. **数据查询：** 根据relationId查询memory_words_tts_record表的text_content字段
2. **内容分割：** 将原始内容和用户输入内容按"\n"分割成单词列表
3. **顺序比对：** 按列表顺序逐一比对每个单词
4. **结果统计：**
   - 总词数：原始单词列表长度
   - 正确数：完全匹配的单词数量
   - 错误数：不匹配的单词数量
   - 空白数：用户未写的单词数量
5. **数据存储：** 将比对结果以JSON格式保存到memory_words_compare_result表

## 比对规则

- **对：** 原始单词与用户输入单词完全一致
- **错：** 原始单词与用户输入单词不一致
- **空白：** 用户未输入对应位置的单词

## 数据库表

### memory_words_compare_result（记忆单词-比对结果表）

| 字段名         | 类型        | 说明                                            |
| -------------- | ----------- | ----------------------------------------------- |
| id             | bigint      | 主键ID                                          |
| open_id        | varchar(64) | 用户openId                                      |
| union_id       | varchar(64) | 用户unionId                                     |
| relation_id    | bigint      | memory_words_user_and_record_relation表的主键id |
| original_words | text        | 原始的单词词组等                                |
| user_words     | text        | 用户输入的单词词组等                            |
| compare_result | text        | 比对结果JSON                                    |
| create_time    | datetime    | 创建时间                                        |
| update_time    | datetime    | 更新时间                                        |

## 注意事项

1. 接口需要用户登录，通过token验证身份
2. relationId必须存在于memory_words_user_and_record_relation表中
3. 比对结果会自动过滤空字符串
4. 如果用户输入的单词数量少于原始单词数量，缺少的部分会被标记为"空白" 