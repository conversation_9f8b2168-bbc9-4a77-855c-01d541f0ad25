package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 体重记录视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "体重记录信息")
public class WeightRecordVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 微信openid
     */
    @Schema(description = "微信openid")
    private String openId;

    /**
     * 微信unionid，可为空
     */
    @Schema(description = "微信unionid，可为空")
    private String unionId;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期")
    private String recordDate;

    /**
     * 体重，单位：千克(kg)
     */
    @Schema(description = "体重，单位：千克(kg)")
    private Double weight;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 从实体类转换为VO
     * 
     * @param entity 实体类对象
     * @return VO对象
     */
    public static WeightRecordVO fromEntity(
            com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodDailyWeightRecord entity) {
        if (entity == null) {
            return null;
        }

        return WeightRecordVO.builder()
                .id(entity.getId())
                .openId(entity.getOpenId())
                .unionId(entity.getUnionId())
                .recordDate(entity.getRecordDate())
                .weight(entity.getWeight())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }
}