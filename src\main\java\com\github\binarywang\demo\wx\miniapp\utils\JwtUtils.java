package com.github.binarywang.demo.wx.miniapp.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
@Slf4j
@Component
public class JwtUtils {

    private static final String CLAIM_KEY_OPENID = "openId";
    private static final String CLAIM_KEY_SESSION_KEY = "session_key";
    private static final String CLAIM_KEY_CREATED = "created";

    @Value("${jwt.secret:xiaochengxuJwtSecret}")
    private String secret;

    @Value("${jwt.expiration:86400}")
    private Long expiration;

    /**
     * 根据小程序用户的openId和session_key生成JWT token
     *
     * @param openId     用户的openId
     * @param sessionKey 小程序会话密钥
     * @return JWT token
     */
    public String generateToken(String openId, String sessionKey) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_OPENID, openId);
        claims.put(CLAIM_KEY_SESSION_KEY, sessionKey);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }

    /**
     * 从token中获取openId
     *
     * @param token JWT token
     * @return openId
     */
    public String getOpenidFromToken(String token) {
        String openId;
        try {
            final Claims claims = getClaimsFromToken(token);
            openId = (String) claims.get(CLAIM_KEY_OPENID);
        } catch (Exception e) {
            log.error("从token中获取openId失败", e);
            openId = null;
        }
        return openId;
    }

    /**
     * 从token中获取session_key
     *
     * @param token JWT token
     * @return session_key
     */
    public String getSessionKeyFromToken(String token) {
        String sessionKey;
        try {
            final Claims claims = getClaimsFromToken(token);
            sessionKey = (String) claims.get(CLAIM_KEY_SESSION_KEY);
        } catch (Exception e) {
            log.error("从token中获取session_key失败", e);
            sessionKey = null;
        }
        return sessionKey;
    }

    /**
     * 验证token是否仍然有效
     *
     * @param token JWT token
     * @return 是否有效
     */
    public Boolean validateToken(String token) {
        return !isTokenExpired(token);
    }

    /**
     * 根据数据声明生成token
     *
     * @param claims 数据声明
     * @return token
     */
    private String generateToken(Map<String, Object> claims) {
        Date expirationDate = generateExpirationDate();
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 从token中获取JWT中的负载
     *
     * @param token JWT token
     * @return Claims
     */
    private Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("从token中获取负载失败", e);
            claims = null;
        }
        return claims;
    }

    /**
     * 生成token的过期时间
     *
     * @return 过期时间
     */
    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }

    /**
     * 判断token是否已经失效
     *
     * @param token JWT token
     * @return 是否失效
     */
    private Boolean isTokenExpired(String token) {
        Date expiration;
        try {
            final Claims claims = getClaimsFromToken(token);
            expiration = claims.getExpiration();
        } catch (Exception e) {
            log.error("判断token是否已经失效失败", e);
            return true;
        }
        return expiration.before(new Date());
    }
}