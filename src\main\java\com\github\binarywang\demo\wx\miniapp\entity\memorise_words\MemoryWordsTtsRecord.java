package com.github.binarywang.demo.wx.miniapp.entity.memorise_words;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 记忆单词-TTS语音合成记录实体类
 */
@Data
public class MemoryWordsTtsRecord {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 创建人-微信openid
     */
    private String openId;

    /**
     * 创建人-微信unionid
     */
    private String unionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 接口类型，可选值"火山引擎"或"阿里云"
     */
    private String apiType;

    /**
     * 环境，可选值为"中文"或"英文"
     */
    private String environment;

    /**
     * 音色类型-key，如 普通话、英式英语、美式英语
     */
    private String voiceType;

    /**
     * 音色类型-value，如BV700_streaming、BV040_streaming、BV027_streaming
     */
    private String voiceValue;

    /**
     * 合成的文本内容
     */
    private String textContent;

    /**
     * 文本类型，plain(普通文本)或ssml(带标记语言)
     */
    private String textType;

    /**
     * 语速-key
     */
    private String speedKey;

    /**
     * 语速-value，实际值
     */
    private Float speedValue;

    /**
     * 重复次数，仅在textType=ssml时生效
     */
    private Integer repeatCount;

    /**
     * 停顿秒数，仅在textType=ssml时生效
     */
    private Float pauseSeconds;

    /**
     * 合成后的语音文件URL
     */
    private String audioUrl;

    /**
     * 语音时长(秒)
     */
    private Integer audioDuration;

    /**
     * 状态：0-处理中，1-成功，2-失败
     */
    private Byte status;

    /**
     * 错误信息，仅在状态为失败时有值
     */
    private String errorMsg;
}