package com.github.binarywang.demo.wx.miniapp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.binarywang.demo.wx.miniapp.filter.TokenAuthenticationFilter;
import com.github.binarywang.demo.wx.miniapp.utils.JwtUtils;
import com.github.binarywang.demo.wx.miniapp.utils.RedisUtils;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 处理Swagger资源路径
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final JwtUtils jwtUtils;
    private final RedisUtils redisUtils;
    private final ObjectMapper objectMapper;

    public WebConfig(JwtUtils jwtUtils, RedisUtils redisUtils, ObjectMapper objectMapper) {
        this.jwtUtils = jwtUtils;
        this.redisUtils = redisUtils;
        this.objectMapper = objectMapper;
    }

    /**
     * 注册Token认证过滤器
     * 
     * @return 过滤器注册Bean
     */
    @Bean
    public FilterRegistrationBean<TokenAuthenticationFilter> tokenAuthenticationFilter() {
        FilterRegistrationBean<TokenAuthenticationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TokenAuthenticationFilter(jwtUtils, redisUtils, objectMapper));
        registrationBean.addUrlPatterns("/*"); // 拦截所有请求
        registrationBean.setName("tokenAuthenticationFilter");
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE); // 设置为最高优先级
        return registrationBean;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Knife4j资源路径
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}