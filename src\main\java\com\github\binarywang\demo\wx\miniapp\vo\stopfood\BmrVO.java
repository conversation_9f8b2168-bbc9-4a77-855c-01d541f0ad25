package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 基础代谢视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "基础代谢信息")
public class BmrVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 微信openid
     */
    @Schema(description = "微信openid")
    private String openId;

    /**
     * 微信unionid，可为空
     */
    @Schema(description = "微信unionid，可为空")
    private String unionId;

    /**
     * 用户的基础代谢，单位：千卡(kcal)
     */
    @Schema(description = "用户的基础代谢，单位：千卡(kcal)")
    private Integer bmrValue;

    /**
     * 基础代谢的生成方式：公式计算、自定义
     */
    @Schema(description = "基础代谢的生成方式：公式计算、自定义")
    private String generationMode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 从实体类转换为VO
     * 
     * @param entity 实体类对象
     * @return VO对象
     */
    public static BmrVO fromEntity(
            com.github.binarywang.demo.wx.miniapp.entity.stopfood.StopfoodBmr entity) {
        if (entity == null) {
            return null;
        }

        return BmrVO.builder()
                .id(entity.getId())
                .openId(entity.getOpenId())
                .unionId(entity.getUnionId())
                .bmrValue(entity.getBmrValue())
                .generationMode(entity.getGenerationMode())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }
}