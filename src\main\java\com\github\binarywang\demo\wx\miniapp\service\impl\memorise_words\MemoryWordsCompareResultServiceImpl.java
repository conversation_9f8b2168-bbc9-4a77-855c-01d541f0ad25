package com.github.binarywang.demo.wx.miniapp.service.impl.memorise_words;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import com.github.binarywang.demo.wx.miniapp.dto.memorise_words.MwCompareResultRequest;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult;
import com.github.binarywang.demo.wx.miniapp.mapper.memorise_words.MemoryWordsCompareResultMapper;
import com.github.binarywang.demo.wx.miniapp.service.memorise_words.MemoryWordsCompareResultService;
import com.github.binarywang.demo.wx.miniapp.utils.JsonUtils;
import com.github.binarywang.demo.wx.miniapp.vo.memorise_words.MwCompareResultVO;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.net.URLDecoder;
import java.io.UnsupportedEncodingException;

/**
 * 记忆单词-比对结果Service实现类
 */
@Service
@Slf4j
public class MemoryWordsCompareResultServiceImpl implements MemoryWordsCompareResultService {

    @Autowired
    private MemoryWordsCompareResultMapper compareResultMapper;

    @Override
    public MwCompareResultVO addCompareResult(MwCompareResultRequest request, String openId, String unionId) {
        log.info("开始处理比对结果，relationId: {}, openId: {}", request.getRelationId(), openId);

        // 1. 根据relationId查询原始内容
        String originalTextContent = compareResultMapper.selectTextContentByRelationId(request.getRelationId());
        if (StringUtils.isBlank(originalTextContent)) {
            throw new RuntimeException("未找到对应的原始内容，relationId: " + request.getRelationId());
        }

        // 2. 解码用户输入内容（前端使用encodeURIComponent编码）
        String decodedUserWords;
        try {
            decodedUserWords = URLDecoder.decode(request.getUserWords(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn("URL解码失败，使用原始内容，relationId: {}, error: {}", request.getRelationId(), e.getMessage());
            decodedUserWords = request.getUserWords();
        }

        // 3. 按"\n"分割原始内容和用户输入内容
        List<String> originalWords = Arrays.asList(originalTextContent.split("\n"));
        List<String> userWords = Arrays.asList(decodedUserWords.split("\n"));

        // 过滤空字符串
        originalWords = originalWords.stream().filter(word -> StringUtils.isNotBlank(word))
                .collect(java.util.stream.Collectors.toList());
        userWords = userWords.stream().filter(word -> StringUtils.isNotBlank(word))
                .collect(java.util.stream.Collectors.toList());

        // 4. 进行比对
        MwCompareResultVO compareResult = compareWords(originalWords, userWords);

        // 5. 保存到数据库
        MemoryWordsCompareResult record = new MemoryWordsCompareResult();
        record.setOpenId(openId);
        record.setUnionId(unionId);
        record.setRelationId(request.getRelationId());
        record.setOriginalWords(originalTextContent);
        record.setUserWords(decodedUserWords); // 保存解码后的内容
        record.setCompareResult(JsonUtils.toJson(compareResult));

        compareResultMapper.insert(record);

        log.info("比对结果处理完成，记录ID: {}", record.getId());
        return compareResult;
    }

    @Override
    public com.github.binarywang.demo.wx.miniapp.vo.PageResult<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult> pageCompareResults(
            String openId, Long relationId, Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 1)
            pageNum = 1;
        if (pageSize == null || pageSize < 1)
            pageSize = 10;
        int offset = (pageNum - 1) * pageSize;
        long total = compareResultMapper.countCompareResults(openId, relationId);
        int pages = (int) Math.ceil((double) total / pageSize);
        java.util.List<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult> list = compareResultMapper
                .selectCompareResultsByPage(openId, relationId, offset, pageSize);
        com.github.binarywang.demo.wx.miniapp.vo.PageResult<com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsCompareResult> result = new com.github.binarywang.demo.wx.miniapp.vo.PageResult<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(total);
        result.setPages(pages);
        result.setList(list);
        return result;
    }

    @Override
    public MwCompareResultVO getCompareResultById(Long id) {
        if (id == null) {
            throw new RuntimeException("比对结果ID不能为空");
        }

        // 查询数据库记录
        MemoryWordsCompareResult record = compareResultMapper.selectById(id);
        if (record == null) {
            throw new RuntimeException("未找到对应的比对结果，ID: " + id);
        }

        // 解析compare_result字段，转换为VO对象
        try {
            MwCompareResultVO result = JsonUtils.fromJson(record.getCompareResult(), MwCompareResultVO.class);
            if (result == null) {
                throw new RuntimeException("比对结果数据格式错误，ID: " + id);
            }
            return result;
        } catch (Exception e) {
            log.error("解析比对结果数据失败，ID: {}, error: {}", id, e.getMessage());
            throw new RuntimeException("比对结果数据解析失败，ID: " + id);
        }
    }

    /**
     * 比对单词列表
     * 
     * @param originalWords 原始单词列表
     * @param userWords     用户输入单词列表
     * @return 比对结果
     */
    private MwCompareResultVO compareWords(List<String> originalWords, List<String> userWords) {
        MwCompareResultVO result = new MwCompareResultVO();

        // 初始化汇总信息
        MwCompareResultVO.SummaryInfo summary = new MwCompareResultVO.SummaryInfo();
        summary.setTotalCount(originalWords.size());
        summary.setCorrectCount(0);
        summary.setWrongCount(0);
        summary.setBlankCount(0);

        // 初始化明细信息
        List<MwCompareResultVO.DetailInfo> details = new ArrayList<>();

        // 按顺序比对
        for (int i = 0; i < originalWords.size(); i++) {
            MwCompareResultVO.DetailInfo detail = new MwCompareResultVO.DetailInfo();
            detail.setOriginalWord(originalWords.get(i));

            // 获取用户对应的单词（如果用户输入不足，则为空）
            String userWord = (i < userWords.size()) ? userWords.get(i) : "";
            detail.setUserWord(userWord);

            // 判断比对结果
            if (StringUtils.isBlank(userWord)) {
                detail.setResult("空白");
                summary.setBlankCount(summary.getBlankCount() + 1);
            } else if (originalWords.get(i).equals(userWord)) {
                detail.setResult("对");
                summary.setCorrectCount(summary.getCorrectCount() + 1);
            } else {
                detail.setResult("错");
                summary.setWrongCount(summary.getWrongCount() + 1);
            }

            details.add(detail);
        }

        result.setSummary(summary);
        result.setDetails(details);

        return result;
    }
}