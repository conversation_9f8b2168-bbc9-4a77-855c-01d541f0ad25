package com.github.binarywang.demo.wx.miniapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户个人资料请求对象
 */
@Data
@Schema(name = "用户个人资料请求", description = "用于更新用户的性别、生日、身高、体重等基础信息")
public class UserProfileRequest {

    @Schema(description = "性别（0-男，1-女，2-未知）", example = "0")
    private Integer gender;

    @Schema(description = "用户生日", example = "1990-01-01")
    private Date birthDate;

    @Schema(description = "身高(cm)", example = "175.5")
    private BigDecimal height;

    @Schema(description = "体重(kg)", example = "65.5")
    private BigDecimal weight;

    @Schema(description = "用户昵称", example = "张三")
    private String nickName;

    @Schema(description = "头像URL", example = "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132")
    private String avatarUrl;
}