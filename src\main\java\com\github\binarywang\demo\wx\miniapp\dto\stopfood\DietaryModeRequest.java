package com.github.binarywang.demo.wx.miniapp.dto.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 饮食模式请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "饮食模式请求参数")
public class DietaryModeRequest {

    /**
     * 饮食模式：快速减重、平稳减重、体重维持、自定义
     */
    @NotBlank(message = "饮食模式不能为空")
    @Schema(description = "饮食模式：快速减重、平稳减重、体重维持、自定义", required = true)
    private String mode;

    /**
     * 系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）
     */
    @NotNull(message = "系数不能为空")
    @Schema(description = "系数，两位小数。（用户每日计划热量=用户基础代谢热量*系数）", required = true)
    private Double coefficient;
}