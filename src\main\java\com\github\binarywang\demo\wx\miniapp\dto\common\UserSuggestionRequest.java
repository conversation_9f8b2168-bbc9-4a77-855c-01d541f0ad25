package com.github.binarywang.demo.wx.miniapp.dto.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户建议请求参数
 */
@Data
@Schema(description = "用户建议请求参数")
public class UserSuggestionRequest {

    /**
     * 小程序id
     */
    @Schema(description = "小程序id", example = "wx1234567890abcdef")
    private String appId;

    /**
     * 用户意见
     */
    @NotBlank(message = "用户意见不能为空")
    @Size(max = 1000, message = "用户意见长度不能超过1000个字符")
    @Schema(description = "用户意见", required = true, example = "希望能增加更多功能")
    private String suggestion;
}