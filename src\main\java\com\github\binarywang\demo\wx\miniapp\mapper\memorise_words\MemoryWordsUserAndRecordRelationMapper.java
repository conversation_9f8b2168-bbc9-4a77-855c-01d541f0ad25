package com.github.binarywang.demo.wx.miniapp.mapper.memorise_words;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsUserAndRecordRelation;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecord;
import com.github.binarywang.demo.wx.miniapp.entity.memorise_words.MemoryWordsTtsRecordWithRelation;

/**
 * 记忆单词-用户与录音关系表Mapper接口
 */
@Mapper
public interface MemoryWordsUserAndRecordRelationMapper {

        /**
         * 插入用户与录音关系记录
         * 
         * @param relation 关系记录实体
         * @return 影响的行数
         */
        @Insert("INSERT INTO memory_words_user_and_record_relation(open_id, union_id, record_id) " +
                        "VALUES(#{openId}, #{unionId}, #{recordId})")
        @Options(useGeneratedKeys = true, keyProperty = "id")
        int insert(MemoryWordsUserAndRecordRelation relation);

        /**
         * 根据openId和recordId查询关系记录
         * 
         * @param openId   用户openId
         * @param recordId 录音记录ID
         * @return 关系记录实体
         */
        @Select("SELECT * FROM memory_words_user_and_record_relation WHERE open_id = #{openId} AND record_id = #{recordId}")
        MemoryWordsUserAndRecordRelation selectByOpenIdAndRecordId(@Param("openId") String openId,
                        @Param("recordId") Long recordId);

        /**
         * 根据openId分页查询用户可访问的录音记录
         * 
         * @param openId  用户openId
         * @param content 文本内容关键词(可为null)
         * @param offset  偏移量
         * @param limit   限制数量
         * @return 录音记录列表
         */
        @Select("<script>" +
                        "SELECT t.* FROM memory_words_tts_record t " +
                        "INNER JOIN memory_words_user_and_record_relation r ON t.id = r.record_id " +
                        "WHERE r.open_id = #{openId} " +
                        "<if test='content != null and content != \"\"'>" +
                        "AND t.text_content LIKE CONCAT('%', #{content}, '%') " +
                        "</if>" +
                        "ORDER BY t.create_time DESC " +
                        "LIMIT #{limit} OFFSET #{offset}" +
                        "</script>")
        List<MemoryWordsTtsRecord> selectRecordsByOpenIdAndContent(@Param("openId") String openId,
                        @Param("content") String content,
                        @Param("offset") int offset,
                        @Param("limit") int limit);

        /**
         * 根据openId分页查询用户可访问的录音记录（包含关系ID）
         * 
         * @param openId  用户openId
         * @param content 文本内容关键词(可为null)
         * @param offset  偏移量
         * @param limit   限制数量
         * @return 录音记录列表（包含关系ID）
         */
        @Select("<script>" +
                        "SELECT t.*, r.id as relation_id FROM memory_words_tts_record t " +
                        "INNER JOIN memory_words_user_and_record_relation r ON t.id = r.record_id " +
                        "WHERE r.open_id = #{openId} " +
                        "<if test='content != null and content != \"\"'>" +
                        "AND t.text_content LIKE CONCAT('%', #{content}, '%') " +
                        "</if>" +
                        "ORDER BY t.create_time DESC " +
                        "LIMIT #{limit} OFFSET #{offset}" +
                        "</script>")
        List<MemoryWordsTtsRecordWithRelation> selectRecordsWithRelationIdByOpenIdAndContent(
                        @Param("openId") String openId,
                        @Param("content") String content,
                        @Param("offset") int offset,
                        @Param("limit") int limit);

        /**
         * 根据openId和文本内容查询用户可访问的录音记录总数
         * 
         * @param openId  用户openId
         * @param content 文本内容关键词(可为null)
         * @return 总记录数
         */
        @Select("<script>" +
                        "SELECT COUNT(1) FROM memory_words_tts_record t " +
                        "INNER JOIN memory_words_user_and_record_relation r ON t.id = r.record_id " +
                        "WHERE r.open_id = #{openId} " +
                        "<if test='content != null and content != \"\"'>" +
                        "AND t.text_content LIKE CONCAT('%', #{content}, '%') " +
                        "</if>" +
                        "</script>")
        long countRecordsByOpenIdAndContent(@Param("openId") String openId, @Param("content") String content);

        /**
         * 根据openId和recordId查询录音记录详情
         * 
         * @param openId   用户openId
         * @param recordId 录音记录ID
         * @return 录音记录实体
         */
        @Select("SELECT t.* FROM memory_words_tts_record t " +
                        "INNER JOIN memory_words_user_and_record_relation r ON t.id = r.record_id " +
                        "WHERE r.open_id = #{openId} AND r.record_id = #{recordId}")
        MemoryWordsTtsRecord selectRecordByOpenIdAndRecordId(@Param("openId") String openId,
                        @Param("recordId") Long recordId);

        /**
         * 根据openId和recordId集合删除用户与录音的关系记录
         * 
         * @param openId    用户openId
         * @param recordIds 录音记录ID集合
         * @return 删除的记录数量
         */
        @Delete("<script>" +
                        "DELETE FROM memory_words_user_and_record_relation " +
                        "WHERE open_id = #{openId} " +
                        "AND record_id IN " +
                        "<foreach collection='recordIds' item='recordId' open='(' separator=',' close=')'>" +
                        "#{recordId}" +
                        "</foreach>" +
                        "</script>")
        int deleteByOpenIdAndRecordIds(@Param("openId") String openId, @Param("recordIds") List<Long> recordIds);
}