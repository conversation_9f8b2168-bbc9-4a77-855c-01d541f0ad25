package com.github.binarywang.demo.wx.miniapp.vo.stopfood;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 每日营养素汇总响应VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "每日营养素汇总响应")
public class DailyNutrientSummaryVO {

    @Schema(description = "记录日期，格式：YYYY-MM-DD", example = "2024-01-15")
    private String recordDate;

    @Schema(description = "热量总摄入，单位：千卡(kcal)", example = "1800")
    private Integer totalIntakeCalories;

    @Schema(description = "热量总消耗，单位：千卡(kcal)", example = "500")
    private Integer totalConsumeCalories;

    @Schema(description = "净热量（摄入-消耗），单位：千卡(kcal)", example = "1300")
    private Integer netCalories;

    @Schema(description = "总摄入的碳水化合物，单位：克", example = "300")
    private Integer totalCarbohydrate;

    @Schema(description = "总摄入的蛋白质，单位：克", example = "120")
    private Integer totalProtein;

    @Schema(description = "总摄入的脂肪，单位：克", example = "80")
    private Integer totalFat;

    /**
     * 计算净热量（摄入热量 - 消耗热量）
     */
    public void calculateNetCalories() {
        if (totalIntakeCalories != null && totalConsumeCalories != null) {
            this.netCalories = totalIntakeCalories - totalConsumeCalories;
        } else {
            this.netCalories = 0;
        }
    }
}