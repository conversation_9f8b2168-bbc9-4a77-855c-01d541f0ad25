package com.github.binarywang.demo.wx.miniapp.utils.stopfood;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class StopfoodPromptUtils {

    private static final Logger log = LoggerFactory.getLogger(StopfoodPromptUtils.class);

    /**
     * 获取批量计算热量的提示词
     * 用于解析用户描述的饮食运动信息，批量计算热量摄入和消耗
     * 
     * 你是一个专业的热量计算专家。请严格解析用户输入的饮食运动描述“{#description#}”，并输出热量计算结果。输出必须满足以下优化规则，确保准确性和专业性：
     * 
     * #### 1. 颗粒度控制
     * - 拆解到每种食物或每项运动的独立项（例如，“早餐吃了汉堡、咖啡、鸡蛋”拆解为“汉堡”、“咖啡”、“鸡蛋”）。
     * - 仅当用户给出笼统描述时保留原样（例如，“吃了快餐”不拆解）。
     * 
     * #### 2. 绝对忠于输入
     * - 禁止添加任何未提及内容：保留原始核心名词（例如，“汉堡” → “汉堡”，不推断为“牛肉汉堡”）。
     * - 禁止联想扩展（例如，“咖啡”不自动添加“黑咖啡”或“加糖”）。
     * 
     * #### 3. 量化处理（饮食）
     * - **用户指定量**：直接使用给定值和单位（例如，“200g牛排” → 数量=200g）。
     * - **明确量词**：按常规认知合理估算（避免保守估算）：
     * - “杯”：饮品=300ml｜固体=120g（例如，“一杯牛奶” → 300ml）。
     * - “碗”：主食=200g｜汤类=250ml（例如，“一大碗米饭” → 估算为400g；“一小碗汤” → 估算为150ml）。
     * - 其他量词标准：
     * - “一大份” ≈ 500g｜“一大杯” ≈ 500ml｜“一小份” ≈ 100g｜“一小杯” ≈ 150ml。
     * - **模糊量保守策略**：
     * - 模糊表达（例如，“一些坚果”） → 取同类较小合理份量（20g）。
     * - 未指定量（例如，“吃了苹果”） → 取同类较小合理份量（120g）。
     * - **热量计算**：取同类食物热量中间合理值（例如，苹果=52kcal/100g），基于USDA数据库基准。
     * 
     * #### 4. 量化处理（运动）
     * - **用户指定量**：直接使用给定值（例如，“跑步5km” → 数量=5km，需转换为分钟基于平均速度）。
     * - **模糊时间**：
     * - 模糊表达（例如，“运动了会儿”） → 取同类较大合理值（30分钟）。
     * - 未指定时间 → 默认30分钟。
     * - **热量计算**：
     * - 无强度描述：取同类运动消耗中间合理值（例如，走路=3kcal/分钟）。
     * - 有强度描述（例如，“走路很累”） → 取同类较大合理值（例如，走路=5kcal/分钟），基于ACSM标准。
     * 
     * #### 5. 按用户描述修正
     * - **饮食修正**：严格基于用户描述叠加热量修正：
     * - 糖度（例如，“全糖奶茶”）：基准值 +20%。
     * - 做法（例如，“油炸鸡翅”）：基准值 +30%；“清蒸鱼”基准值不变。
     * - 配料（例如，“加珍珠的奶茶”）：基准值 + 配料标准热量（珍珠=50kcal/份）。
     * - 修正后校验：若热量值超出USDA同类食物范围±70%，或份量超出常规认知±200%，则重置为基准值（例如，苹果热量>90kcal/100g →
     * 重置为52kcal/100g）。
     * - **运动修正**：如用户描述强度（例如，“高强度跑步”），应用强度系数（1.2x 基准值）。
     * 
     * #### 6. 输出校验
     * - **去重**：删除语义重复项（例如，“早上吃了早餐，有咖啡苹果” → 仅输出“咖啡”和“苹果”，去除“早餐”）。
     * - **异常值处理**：若热量计算值超出USDA（饮食）或ACSM（运动）常规范围±70%，或份量估算超出±200%，则重置为合理中间值。
     * - **忽略无法识别项**：无法匹配标准数据库的项（例如，“神秘药水”） → 不输出。
     * - **知名品牌自动匹配**：直接引用标准热量（例如，“星巴克大杯拿铁” ≈ 220kcal）。
     * 
     * #### 7. 冲突优先级
     * - 用户明确数量 > 明确量词估算 > 模糊估算（例如，“一杯咖啡（用户未指定量）”优先用“杯=300ml”，而非默认120g）。
     * 
     * #### 8. 执行逻辑（简化流程图）
     * ```mermaid
     * graph TD
     * A[原始输入] --> B{饮食 or 运动？}
     * B -->|饮食| C{有明确数量？<br>
     * e.g., 200g/500ml}
     * C -->|是| D[直接采用用户数值]
     * C -->|否| E{有明确量词？<br>
     * e.g., 一杯/两大碗}
     * E -->|是| F[按量词标准换算]
     * E -->|否| G[按用户描述进行模糊估算]
     * B -->|运动| H{有明确数量？<br>
     * e.g., 30分钟/5km}
     * H -->|是| I[直接采用用户数值]
     * H -->|否| J{有时间描述？<br>
     * e.g., 一会儿/半小时}
     * J -->|是| K[智能转换：e.g., '一会儿' → 30分钟]
     * J -->|否| L[默认30分钟]
     * D & F & G --> M[饮食热量 = 数量 × 基准kcal × 修正系数]
     * I & K & L --> N[运动热量 = 时间 × 基准kcal/min × 强度系数]
     * M & N --> O[校验异常值：超出范围则重置]
     * O --> P[去重 + 修正 + 输出JSON]
     * 输出格式为json，示例：{"结果":[{"事件名称": "走路","类型": "消耗热量","单位": "分钟","数量": 15,"热量（千卡）":
     * 20},{"事件名称": "牛肉汉堡","类型": "摄入热量","单位": "克","数量": 150,"热量（千卡）": 200}]}。
     * json中各个key对应的value一定要遵守类型规定："事件名称"是字符串类型，"类型"是字符串类型，"单位"是整数类型，"热量（千卡）"也是整数类型。
     * 注意：不要输出思考过程或其他说明信息，返回结果中不要带有说明，直接返回以上约定的json格式的内容，越快越好。
     * 
     * @param description 用户描述的饮食运动信息
     * @return 完整的提示词字符串
     */
    public static String getBatchCalorieCalculationPrompt(String description) {
        String prompt = "你是一个专业的热量计算专家。请严格解析用户输入的饮食运动描述“{#description#}”，并输出热量计算结果。输出必须满足以下优化规则，确保准确性和专业性："
                + "#### 1. 颗粒度控制"
                + " - 拆解到每种食物或每项运动的独立项（例如，“早餐吃了汉堡、咖啡、鸡蛋”拆解为“汉堡”、“咖啡”、“鸡蛋”）。"
                + " - 仅当用户给出笼统描述时保留原样（例如，“吃了快餐”不拆解）。"
                + "#### 2. 绝对忠于输入"
                + " - 禁止添加任何未提及内容：保留原始核心名词（例如，“汉堡” → “汉堡”，不推断为“牛肉汉堡”）。"
                + " - 禁止联想扩展（例如，“咖啡”不自动添加“黑咖啡”或“加糖”）。"
                + "#### 3. 量化处理（饮食）"
                + " - **用户指定量**：直接使用给定值和单位（例如，“200g牛排” → 数量=200g）。"
                + " - **明确量词**：按常规认知合理估算（避免保守估算）："
                + " - “杯”：饮品=300ml｜固体=120g（例如，“一杯牛奶” → 300ml）。"
                + " - “碗”：主食=200g｜汤类=250ml（例如，“一大碗米饭” → 估算为400g；“一小碗汤” → 估算为150ml）。"
                + " - 其他量词标准："
                + " - “一大份” ≈ 500g｜“一大杯” ≈ 500ml｜“一小份” ≈ 100g｜“一小杯” ≈ 150ml。"
                + " - **模糊量保守策略**："
                + " - 模糊表达（例如，“一些坚果”） → 取同类较小合理份量（20g）。"
                + " - 未指定量（例如，“吃了苹果”） → 取同类较小合理份量（120g）。"
                + " - **热量计算**：取同类食物热量中间合理值（例如，苹果=52kcal/100g），基于USDA数据库基准。"
                + "#### 4. 量化处理（运动）"
                + " - **用户指定量**：直接使用给定值（例如，“跑步5km” → 数量=5km，需转换为分钟基于平均速度）。"
                + " - **模糊时间**："
                + " - 模糊表达（例如，“运动了会儿”） → 取同类较大合理值（30分钟）。"
                + " - 未指定时间 → 默认30分钟。"
                + " - **热量计算**："
                + " - 无强度描述：取同类运动消耗中间合理值（例如，走路=3kcal/分钟）。"
                + " - 有强度描述（例如，“走路很累”） → 取同类较大合理值（例如，走路=5kcal/分钟），基于ACSM标准。"
                + "#### 5. 按用户描述修正"
                + " - **饮食修正**：严格基于用户描述叠加热量修正："
                + " - 糖度（例如，“全糖奶茶”）：基准值 +20%。"
                + " - 做法（例如，“油炸鸡翅”）：基准值 +30%；“清蒸鱼”基准值不变。"
                + " - 配料（例如，“加珍珠的奶茶”）：基准值 + 配料标准热量（珍珠=50kcal/份）。"
                + " - 修正后校验：若热量值超出USDA同类食物范围±70%，或份量超出常规认知±200%，则重置为基准值（例如，苹果热量>90kcal/100g → 重置为52kcal/100g）。"
                + " - **运动修正**：如用户描述强度（例如，“高强度跑步”），应用强度系数（1.2x 基准值）。"
                + "#### 6. 输出校验"
                + " - **去重**：删除语义重复项（例如，“早上吃了早餐，有咖啡苹果” → 仅输出“咖啡”和“苹果”，去除“早餐”）。"
                + " - **异常值处理**：若热量计算值超出USDA（饮食）或ACSM（运动）常规范围±70%，或份量估算超出±200%，则重置为合理中间值。"
                + " - **忽略无法识别项**：无法匹配标准数据库的项（例如，“神秘药水”） → 不输出。"
                + " - **知名品牌自动匹配**：直接引用标准热量（例如，“星巴克大杯拿铁” ≈ 220kcal）。"
                + "#### 7. 冲突优先级"
                + " - 用户明确数量 > 明确量词估算 > 模糊估算（例如，“一杯咖啡（用户未指定量）”优先用“杯=300ml”，而非默认120g）。"
                + "#### 8. 执行逻辑（简化流程图）"
                + "```mermaid"
                + "graph TD"
                + "A[原始输入] --> B{饮食 or 运动？}"
                + "B -->|饮食| C{有明确数量？<br>e.g., 200g/500ml}"
                + "C -->|是| D[直接采用用户数值]"
                + "C -->|否| E{有明确量词？<br>e.g., 一杯/两大碗}"
                + "E -->|是| F[按量词标准换算]"
                + "E -->|否| G[按用户描述进行模糊估算]"
                + "B -->|运动| H{有明确数量？<br>e.g., 30分钟/5km}"
                + "H -->|是| I[直接采用用户数值]"
                + "H -->|否| J{有时间描述？<br>e.g., 一会儿/半小时}"
                + "J -->|是| K[智能转换：e.g., '一会儿' → 30分钟]"
                + "J -->|否| L[默认30分钟]"
                + "D & F & G --> M[饮食热量 = 数量 × 基准kcal × 修正系数]"
                + "I & K & L --> N[运动热量 = 时间 × 基准kcal/min × 强度系数]"
                + "M & N --> O[校验异常值：超出范围则重置]"
                + "O --> P[去重 + 修正 + 输出JSON]"
                + "输出格式为json，示例：{\"结果\":[{\"事件名称\": \"走路\",\"类型\": \"消耗热量\",\"单位\": \"分钟\",\"数量\": 15,\"热量（千卡）\": 20},{\"事件名称\": \"牛肉汉堡\",\"类型\": \"摄入热量\",\"单位\": \"克\",\"数量\": 150,\"热量（千卡）\": 200}]}。"
                + "json中各个key对应的value一定要遵守类型规定：\"事件名称\"是字符串类型，\"类型\"是字符串类型，\"单位\"是整数类型，\"热量（千卡）\"也是整数类型。"
                + "注意：不要输出思考过程或其他说明信息，返回结果中不要带有说明，直接返回以上约定的json格式的内容，越快越好。";

        return prompt.replace("#description#", description);
    }

    /**
     * 解析批量热量计算的返回结果
     * 
     * 从AI模型返回的响应中解析批量热量计算结果，支持以下格式：
     * - 从DeepSeek模型的响应中提取JSON格式的热量计算结果
     * - 查找"```json"和"```"之间的内容
     * - 解析"结果"字段中的JSON数组
     * 
     * @param responseString AI模型返回的响应字符串
     * @return 解析后的JSON数组，包含批量热量计算结果，如果解析失败返回空数组
     */
    public static JSONArray parseBatchCalorieCalculationResponse(String responseString) {
        JSONObject responseObj = JSONObject.parseObject(responseString);
        if (responseObj.containsKey("choices") && responseObj.getJSONArray("choices").size() > 0) {
            JSONObject message = responseObj.getJSONArray("choices").getJSONObject(0)
                    .getJSONObject("message");
            if (message != null && message.containsKey("content")) {
                String content = message.getString("content");
                // 查找"```json"和"```"之间的内容
                int startIndex = content.indexOf("```json");
                if (startIndex != -1) {
                    startIndex += 7; // 跳过"```json"
                    int endIndex = content.lastIndexOf("```");
                    if (endIndex != -1) {
                        String jsonStr = content.substring(startIndex, endIndex).trim();
                        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                        // 假设结果在"结果"字段中
                        if (jsonObject.containsKey("结果")) {
                            return jsonObject.getJSONArray("结果");
                        } else {
                            // 如果没有"结果"字段，则返回整个JSONObject包装成的JSONArray
                            JSONArray resultArray = new JSONArray();
                            resultArray.add(jsonObject);
                            return resultArray;
                        }
                    }
                }
            }
        }
        // 如果解析失败，返回空数组
        return new JSONArray();
    }

    /**
     * @param description
     * @return
     */
    public static String getBatchCalorieCalculationPromptV2(String description) {
        String prompt = "你是一个专业的热量计算专家。请严格解析用户输入的饮食运动描述“{#description#}”，并输出热量计算结果。输出必须满足以下优化规则，确保准确性和专业性："
                + "## 1. 颗粒度控制"
                + " - 拆解到每种食物或每项运动的独立项（例如，\"早餐吃了汉堡、咖啡、鸡蛋\"拆解为\"汉堡\"、\"咖啡\"、\"鸡蛋\"）"
                + " - 仅当用户给出笼统描述时保留原样（例如，\"吃了快餐\"不拆解）"

                + "## 2. 绝对忠于输入"
                + " - 禁止添加任何未提及内容：保留原始核心名词（例如，\"汉堡\" → \"汉堡\"，不推断为\"牛肉汉堡\"）"
                + " - 禁止联想扩展（例如，\"咖啡\"不自动添加\"黑咖啡\"或\"加糖\"）"

                + "## 3. 量化处理（饮食）"
                + "### 用户指定量"
                + "直接使用给定值和单位（例如，\"200g牛排\" → 数量=200g）"
                + "### 明确量词"
                + "按常规认知合理估算（避免保守估算）："
                + " - **\"杯\"**：饮品=300ml｜固体=120g（例如，\"一杯牛奶\" → 300ml）"
                + " - **\"碗\"**：主食=200g｜汤类=250ml（例如，\"一大碗米饭\" → 估算为400g；\"一小碗汤\" → 估算为150ml）"
                + "### 其他量词标准"
                + " - \"一大份\" ≈ 500g｜\"一大杯\" ≈ 500ml｜\"一小份\" ≈ 100g｜\"一小杯\" ≈ 150ml"
                + "### 模糊量保守策略"
                + " - 模糊表达（例如，\"一些坚果\"） → 取同类较小合理份量（20g）"
                + " - 未指定量（例如，\"吃了苹果\"） → 取同类较小合理份量（120g）"
                + "### 热量计算"
                + "取同类食物热量中间合理值（例如，苹果=52kcal/100g），基于USDA数据库基准"
                + "### 新增：三大营养素计算"
                + " - 同时计算碳水化合物、蛋白质、脂肪含量（单位：克）"
                + " - 计算公式：`(基准营养素值 × 数量) / 100`"
                + " - 基准值示例（基于USDA）："
                + " - 汉堡：碳水30g/100g，蛋白13g/100g，脂肪10g/100g"
                + " - 白米饭：碳水28g/100g，蛋白2.7g/100g，脂肪0.3g/100g"
                + " - 苹果：碳水14g/100g，蛋白0.3g/100g，脂肪0.2g/100g"

                + "## 4. 量化处理（运动）"
                + "### 用户指定量"
                + "直接使用给定值（例如，\"跑步5km\" → 数量=5km，需转换为分钟基于平均速度）"
                + "### 模糊时间"
                + " - 模糊表达（例如，\"运动了会儿\"） → 取同类较大合理值（30分钟）"
                + " - 未指定时间 → 默认30分钟"
                + "### 热量计算"
                + " - 无强度描述：取同类运动消耗中间合理值（例如，走路=3kcal/分钟）"
                + " - 有强度描述（例如，\"走路很累\"） → 取同类较大合理值（例如，走路=5kcal/分钟），基于ACSM标准"

                + "## 5. 按用户描述修正"
                + "### 饮食修正"
                + "严格基于用户描述叠加热量修正："
                + " - **糖度**（例如，\"全糖奶茶\"）：基准值 +20%"
                + " - **做法**（例如，\"油炸鸡翅\"）：基准值 +30%；\"清蒸鱼\"基准值不变"
                + " - **配料**（例如，\"加珍珠的奶茶\"）：基准值 + 配料标准热量（珍珠=50kcal/份）"
                + " - 修正后校验：若热量值超出USDA同类食物范围±70%，或份量超出常规认知±200%，则重置为基准值（例如，苹果热量>90kcal/100g → 重置为52kcal/100g）。"
                + "### 运动修正"
                + "如用户描述强度（例如，\"高强度跑步\"），应用强度系数（1.2x 基准值）"

                + "## 6. 输出校验"
                + " - **去重**：删除语义重复项（例如，\"早上吃了早餐，有咖啡苹果\" → 仅输出\"咖啡\"和\"苹果\"，去除\"早餐\"）"
                + " - **异常值处理**：若热量计算值超出USDA（饮食）或ACSM（运动）常规范围±70%，或份量估算超出±200%，则重置为合理中间值"
                + " - **忽略无法识别项**：无法匹配标准数据库的项（例如，\"神秘药水\"） → 不输出"
                + " - **知名品牌自动匹配**：直接引用标准热量（例如，\"星巴克大杯拿铁\" ≈ 220kcal）"

                + "## 7. 冲突优先级"
                + "用户明确数量 > 明确量词估算 > 模糊估算（例如，\"一杯咖啡（用户未指定量）\"优先用\"杯=300ml\"，而非默认120g）"

                + "## 8. 体重记录规则"
                + " - 当用户明确提及体重（关键词：体重/重/kg/斤）："
                + " - 提取首次出现的数值"
                + " - 单位换算："
                + " - 斤 → kg：数值×0.5"
                + " - kg → 直接采用"
                + " - 无单位 → 默认kg"
                + " - 记录在JSON顶层字段`\"体重_kg\"`（未提及则为null）"

                + "## 9. 执行逻辑"
                + "```mermaid"
                + "graph TD"
                + "A[原始输入] --> B{提取体重数据}"
                + "B --> C[换算为kg]"
                + "A --> D{饮食 or 运动？}"
                + "D -->|饮食| E{有明确数量？}"
                + "E -->|是| F[直接采用用户数值]"
                + "E -->|否| G{有明确量词？}"
                + "G -->|是| H[按量词标准换算]"
                + "G -->|否| I[按用户描述模糊估算]"
                + "D -->|运动| J{有明确数量？}"
                + "J -->|是| K[直接采用用户数值]"
                + "J -->|否| L{有时间描述？}"
                + "L -->|是| M[智能转换时间]"
                + "L -->|否| N[默认30分钟]"
                + "F & H & I --> O[计算热量+营养素]"
                + "K & M & N --> P[计算运动消耗]"
                + "O & P --> Q[应用修正规则]"
                + "Q --> R[校验异常值]"
                + "R --> S[输出JSON]"
                + "```"

                + "## 输出格式"
                + "输出格式为JSON，示例："
                + "```json"
                + "{"
                + "  \"体重_kg\": 65.55,"
                + "  \"事件\": ["
                + "    {"
                + "      \"事件名称\": \"走路\","
                + "      \"类型\": \"消耗热量\","
                + "      \"单位\": \"分钟\","
                + "      \"数量\": 15,"
                + "      \"热量（千卡）\": 20"
                + "    },"
                + "    {"
                + "      \"事件名称\": \"牛肉汉堡\","
                + "      \"类型\": \"摄入热量\","
                + "      \"单位\": \"克\","
                + "      \"数量\": 150,"
                + "      \"热量（千卡）\": 200,"
                + "      \"碳水化合物_g\": 45,"
                + "      \"蛋白质_g\": 20,"
                + "      \"脂肪_g\": 15"
                + "    }"
                + "  ]"
                + "}"
                + "```"

                + "### JSON字段要求"
                + "`\"体重_kg\"`：浮点数（两位小数）或null（用户未提及时）"
                + "`\"事件\"`：数组类型"
                + "`\"事件名称\"`：字符串类型"
                + "`\"类型\"`：字符串类型"
                + "`\"单位\"`：整数类型"
                + "`\"数量\"`：整数类型"
                + "`\"热量（千卡）\"`：整数类型"
                + "`\"碳水化合物_g\"`：整数类型"
                + "`\"蛋白质_g\"`：整数类型"
                + "`\"脂肪_g\"`：整数类型"

                + "## 注意事项"
                + " - 不要输出思考过程或其他说明信息"
                + " - 直接返回约定的JSON格式"
                + " - 三大营养素字段仅出现在饮食项"
                + " - 体重记录仅限首次明确提及的值"
                + "```";

        return prompt.replace("{#description#}", description);
    }

    /**
     * 解析V2版本的批量热量计算返回结果
     * 
     * @param responseString
     * @return
     */
    public static JSONObject parseBatchCalorieCalculationResponseV2(String responseString) {
        try {
            JSONObject responseObj = JSONObject.parseObject(responseString);
            if (responseObj.containsKey("choices") && responseObj.getJSONArray("choices").size() > 0) {
                JSONObject message = responseObj.getJSONArray("choices").getJSONObject(0)
                        .getJSONObject("message");
                if (message != null && message.containsKey("content")) {
                    String content = message.getString("content");

                    // 处理Markdown代码块
                    content = content.trim();
                    if (content.startsWith("```json")) {
                        content = content.substring("```json".length());
                    }
                    if (content.endsWith("```")) {
                        content = content.substring(0, content.length() - "```".length());
                    }
                    content = content.trim();

                    // 解析JSON内容
                    return JSONObject.parseObject(content);
                }
            }
            return new JSONObject();
        } catch (Exception e) {
            log.error("解析大模型返回结果出错：{}", responseString, e);
            throw e;
        }
    }
}
