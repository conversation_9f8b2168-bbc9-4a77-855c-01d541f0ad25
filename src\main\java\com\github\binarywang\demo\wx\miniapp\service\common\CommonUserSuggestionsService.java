package com.github.binarywang.demo.wx.miniapp.service.common;

import com.github.binarywang.demo.wx.miniapp.entity.common.CommonUserSuggestions;
import com.github.binarywang.demo.wx.miniapp.vo.PageResult;

import java.util.List;

/**
 * 通用-用户建议表Service接口
 */
public interface CommonUserSuggestionsService {

    /**
     * 保存用户建议
     * 
     * @param openId     微信openid
     * @param unionId    微信unionid
     * @param appId      小程序id
     * @param suggestion 用户意见
     * @return 保存后的记录ID
     */
    Long saveSuggestion(String openId, String unionId, String appId, String suggestion);

    /**
     * 根据ID查询用户建议
     * 
     * @param id 记录ID
     * @return 用户建议对象
     */
    CommonUserSuggestions getSuggestionById(Long id);

    /**
     * 根据openId查询用户建议列表
     * 
     * @param openId 微信openid
     * @return 用户建议列表
     */
    List<CommonUserSuggestions> getSuggestionsByOpenId(String openId);

    /**
     * 分页查询用户建议列表
     * 
     * @param openId   微信openid
     * @param pageNum  页码，从1开始
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageResult<CommonUserSuggestions> getSuggestionsByPage(String openId, Integer pageNum, Integer pageSize);

    /**
     * 删除用户建议
     * 
     * @param id     记录ID
     * @param openId 微信openid
     * @return 是否删除成功
     */
    boolean deleteSuggestion(Long id, String openId);
}