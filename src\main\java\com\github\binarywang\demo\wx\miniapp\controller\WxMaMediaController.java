package com.github.binarywang.demo.wx.miniapp.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.google.common.collect.Lists;
import com.google.common.io.Files;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

/**
 * <pre>
 *  小程序临时素材接口
 *  Created by BinaryWang on 2017/6/16.
 * </pre>
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@RestController
@RequestMapping("/wx/media/{appid}")
@Tag(name = "小程序媒体接口")
@AllArgsConstructor
@Slf4j
public class WxMaMediaController {
    private final WxMaService wxMaService;

    /**
     * 上传临时素材
     *
     * @return 素材的media_id列表，实际上如果有的话，只会有一个
     */
    @PostMapping("/upload")
    @Operation(summary = "上传媒体文件", description = "上传媒体文件到微信小程序")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "type", description = "媒体文件类型", required = true, in = ParameterIn.QUERY)
    })
    public WxMediaUploadResult uploadMedia(@PathVariable String appid, @RequestParam String type,
            @RequestParam("file") MultipartFile multipartFile) throws WxErrorException {
        if (!wxMaService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            File tempFile = File.createTempFile("upload_", ".tmp");
            multipartFile.transferTo(tempFile);
            WxMediaUploadResult uploadResult = wxMaService.getMediaService().uploadMedia(type, tempFile);
            tempFile.delete();
            return uploadResult;
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new WxErrorException("上传文件失败");
        } finally {
            WxMaConfigHolder.remove();
        }
    }

    /**
     * 下载临时素材
     */
    @GetMapping("/download/{mediaId}")
    @Operation(summary = "获取媒体文件", description = "获取微信小程序媒体文件")
    @Parameters({
            @Parameter(name = "appid", description = "微信小程序appid", required = true, in = ParameterIn.PATH),
            @Parameter(name = "mediaId", description = "媒体文件ID", required = true, in = ParameterIn.PATH)
    })
    public void getMedia(@PathVariable String appid, @PathVariable String mediaId,
            HttpServletResponse response) throws WxErrorException {
        if (!wxMaService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        try {
            File file = wxMaService.getMediaService().getMedia(mediaId);
            if (file != null) {
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
                Files.copy(file, response.getOutputStream());
            }
        } catch (IOException e) {
            log.error("下载文件失败", e);
            throw new WxErrorException("下载文件失败");
        } finally {
            WxMaConfigHolder.remove();
        }
    }
}
